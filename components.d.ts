/* eslint-disable */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
export {}

/* prettier-ignore */
declare module 'vue' {
  export interface GlobalComponents {
    AAvatar: typeof import('ant-design-vue/es')['Avatar']
    AButton: typeof import('ant-design-vue/es')['Button']
    ACascader: typeof import('ant-design-vue/es')['Cascader']
    ACheckbox: typeof import('ant-design-vue/es')['Checkbox']
    ADatePicker: typeof import('ant-design-vue/es')['DatePicker']
    ADrawer: typeof import('ant-design-vue/es')['Drawer']
    AEmpty: typeof import('ant-design-vue/es')['Empty']
    AForm: typeof import('ant-design-vue/es')['Form']
    AFormItem: typeof import('ant-design-vue/es')['FormItem']
    AImage: typeof import('ant-design-vue/es')['Image']
    AInput: typeof import('ant-design-vue/es')['Input']
    AInputPassword: typeof import('ant-design-vue/es')['InputPassword']
    AInputSearch: typeof import('ant-design-vue/es')['InputSearch']
    AMenu: typeof import('ant-design-vue/es')['Menu']
    AModal: typeof import('ant-design-vue/es')['Modal']
    APopconfirm: typeof import('ant-design-vue/es')['Popconfirm']
    ARadioButton: typeof import('ant-design-vue/es')['RadioButton']
    ARadioGroup: typeof import('ant-design-vue/es')['RadioGroup']
    ARangePicker: typeof import('ant-design-vue/es')['RangePicker']
    ASegmented: typeof import('ant-design-vue/es')['Segmented']
    ASelect: typeof import('ant-design-vue/es')['Select']
    ASelectOption: typeof import('ant-design-vue/es')['SelectOption']
    ASpin: typeof import('ant-design-vue/es')['Spin']
    ATextarea: typeof import('ant-design-vue/es')['Textarea']
    ATooltip: typeof import('ant-design-vue/es')['Tooltip']
    ATree: typeof import('ant-design-vue/es')['Tree']
    AUpload: typeof import('ant-design-vue/es')['Upload']
    BiPopup: typeof import('./src/components/MapPopup/biPopup.vue')['default']
    BlockColorExample: typeof import('./src/components/BlockColorExample/index.vue')['default']
    CoomTab: typeof import('./src/components/CoomTab/index.vue')['default']
    Echarts: typeof import('./src/components/Echarts/index.vue')['default']
    FineWaterPopup: typeof import('./src/components/MapPopup/fineWaterPopup.vue')['default']
    HomeTab: typeof import('./src/components/HomeTab/index.vue')['default']
    MainFooter: typeof import('./src/components/MainFooter/index.vue')['default']
    MainHander: typeof import('./src/components/MainHander/index.vue')['default']
    MapGlS2: typeof import('./src/components/MapGlS2/index.vue')['default']
    MapInputSearch: typeof import('./src/components/MapInputSearch/index.vue')['default']
    MapLegend: typeof import('./src/components/MapGlS2/cpns/MapLegend.vue')['default']
    MapOperate: typeof import('./src/components/MapOperate/index.vue')['default']
    MapPopup: typeof import('./src/components/MapPopup/index.vue')['default']
    PandectPopup: typeof import('./src/components/MapPopup/pandectPopup.vue')['default']
    RouterLink: typeof import('vue-router')['RouterLink']
    RouterView: typeof import('vue-router')['RouterView']
    UploadImage: typeof import('./src/components/UploadImage/index.vue')['default']
    UploadTable: typeof import('./src/components/UploadTable/index.vue')['default']
  }
}
