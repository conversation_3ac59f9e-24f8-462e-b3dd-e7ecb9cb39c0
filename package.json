{"name": "gis-map", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview"}, "dependencies": {"@deck.gl/aggregation-layers": "^9.1.11", "@deck.gl/geo-layers": "^9.1.11", "@deck.gl/layers": "^9.1.11", "@deck.gl/mapbox": "^9.1.11", "@turf/turf": "^7.1.0", "@tweenjs/tween.js": "^25.0.0", "@types/vue": "^2.0.0", "@vueuse/core": "^13.0.0", "ant-design-vue": "^4.2.0", "autofit.js": "^2.0.1", "axios": "^1.6.8", "d3-scale": "^4.0.2", "dayjs": "^1.11.13", "echarts": "^5.5.1", "exceljs": "^4.4.0", "jsencrypt": "^3.3.2", "jszip": "^3.10.1", "lodash": "^4.17.21", "mapbox-gl": "^3.3.0", "normalize.css": "^8.0.1", "pinia": "^2.1.7", "popmotion": "^11.0.5", "proj4": "^2.15.0", "three": "^0.171.0", "threebox-plugin": "^2.2.7", "vue": "^3.4.21", "vue-router": "^4.3.2", "xlsx": "https://cdn.sheetjs.com/xlsx-0.20.2/xlsx-0.20.2.tgz"}, "devDependencies": {"@rollup/plugin-json": "^6.1.0", "@types/mapbox-gl": "^3.1.0", "@types/node": "^20.14.12", "@vitejs/plugin-vue": "^5.0.4", "less": "^4.2.0", "rollup-plugin-visualizer": "^5.12.0", "typescript": "^5.5.4", "unplugin-vue-components": "^0.27.0", "vite": "^5.2.0", "vite-plugin-compression": "^0.5.1"}}