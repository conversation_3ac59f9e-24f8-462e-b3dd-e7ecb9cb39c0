<template>
  <div class="app flex f-column">
    <ConfigProvider :locale="zhCn">
      <router-view class="f-1" />
    </ConfigProvider>
  </div>
</template>

<script setup lang="ts">
import { onMounted } from 'vue'
import { ConfigProvider } from 'ant-design-vue'
import autofit from 'autofit.js'
import zhCn from 'ant-design-vue/es/locale/zh_CN'
import 'dayjs/locale/zh-cn'

onMounted(() => autofit.init({ designHeight: 1080, designWidth: 1920, renderDom: '#app', resize: true }))
</script>
