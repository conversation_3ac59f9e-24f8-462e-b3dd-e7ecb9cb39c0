/* flex */
.flex {
  display: flex;
}
.display-none {
  display: none;
}
.display-block {
  display: block;
}
.f-xy-center {
  display: flex;
  justify-content: center;
  align-items: center;
}
.f-x-center {
  display: flex;
  justify-content: center;
}
.f-y-center {
  display: flex;
  align-items: center;
}
.f-between {
  display: flex;
  justify-content: space-between;
}
.f-end {
  display: flex;
  justify-content: flex-end;
}
.f-wrap {
  flex-wrap: wrap;
}
.f-column {
  flex-direction: column;
}
.f-row-reverse {
  flex-direction: row-reverse;
}
.f-column-reverse {
  flex-direction: column-reverse;
}
.f-1 {
  flex: 1;
}
.f-2 {
  flex: 2;
}
.f-3 {
  flex: 3;
}
.f-4 {
  flex: 4;
}
.f-5 {
  flex: 5;
}
.f-6 {
  flex: 6;
}
.f-7 {
  flex: 7;
}
.f-8 {
  flex: 8;
}
.f-9 {
  flex: 9;
}
.f-10 {
  flex: 10;
}

.relative {
  position: relative;
}
.absolute {
  position: absolute;
}
.absolute-center {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}
.absolute-all {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
}

.z-index-9 {
  z-index: 9;
}
.z-index-10 {
  z-index: 10;
}
.z-index-99 {
  z-index: 99;
}
.z-index-100 {
  z-index: 100;
}
.z-index-999 {
  z-index: 999;
}

.overflow-auto {
  overflow: auto;
}

.overflow-hidden {
  overflow: hidden;
}
.overflow-scroll {
  overflow: scroll;
}

.box-shadow {
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05), 0 1px 3px rgba(0, 0, 0, 0.1);
}
.text-center {
  text-align: center;
}
.text-left {
  text-align: left;
}
.text-right {
  text-align: right;
}
.middle {
  vertical-align: middle;
}

/* 单行文本 */
.text-nowrap {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.nowrap {
  white-space: nowrap;
}

/* 多行文本 */
.text-nowrap-2 {
  word-break: break-all;
  overflow: hidden;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}
.text-nowrap-3 {
  word-break: break-all;
  overflow: hidden;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
}
.text-nowrap-4 {
  word-break: break-all;
  overflow: hidden;
  display: -webkit-box;
  -webkit-line-clamp: 4;
  -webkit-box-orient: vertical;
}
.text-nowrap-5 {
  word-break: break-all;
  overflow: hidden;
  display: -webkit-box;
  -webkit-line-clamp: 5;
  -webkit-box-orient: vertical;
}
.text-nowrap-6 {
  word-break: break-all;
  overflow: hidden;
  display: -webkit-box;
  -webkit-line-clamp: 6;
  -webkit-box-orient: vertical;
}

.border-eee {
  border: 1px solid #eee;
}
.border-666 {
  border: 1px solid #66666666;
}
.border-B-eee {
  border-bottom: 1px solid #eee;
}

/* grid */
.grid {
  display: grid;
}
.grid-col2 {
  grid-template-columns: 1fr 1fr;
}
.grid-col3 {
  grid-template-columns: 1fr 1fr 1fr;
}
.grid-col4 {
  grid-template-columns: 1fr 1fr 1fr 1fr;
}
.grid-col5 {
  grid-template-columns: 1fr 1fr 1fr 1fr 1fr;
}
.grid-row2 {
  grid-template-rows: 1fr 1fr;
}
.grid-row3 {
  grid-template-rows: 1fr 1fr 1fr;
}
.grid-row4 {
  grid-template-rows: 1fr 1fr 1fr 1fr;
}
.grid-row5 {
  grid-template-rows: 1fr 1fr 1fr 1fr;
}

/*  */

.all {
  width: 100%;
  height: 100%;
}
.W-auot {
  width: auto;
}
.H-auto {
  height: auto;
}
.W100 {
  width: 100%;
}
.H100 {
  height: 100%;
}

.H100vh {
  height: 100vh;
}
.H100vw {
  width: 100vw;
}
.H100vh60 {
  height: calc(100vh - 60px);
}
.H100vh50 {
  height: calc(100vh - 50px);
}

/* 字体 */
.fon-S12 {
  font-size: 12px;
}
.fon-S14 {
  font-size: 14px;
}
.fon-S16 {
  font-size: 16px;
}
.fon-S18 {
  font-size: 18px;
}
.fon-S20 {
  font-size: 20px;
}
.fon-S22 {
  font-size: 22px;
}
.fon-S24 {
  font-size: 24px;
}
.fon-S26 {
  font-size: 26px;
}
.fon-S28 {
  font-size: 28px;
}
.fon-S30 {
  font-size: 30px;
}
.fon-S32 {
  font-size: 32px;
}
.fon-S34 {
  font-size: 34px;
}
.fon-S36 {
  font-size: 36px;
}
.fon-S38 {
  font-size: 38px;
}
.fon-S40 {
  font-size: 40px;
}
.fon-S42 {
  font-size: 42px;
}
.fon-S44 {
  font-size: 44px;
}
.fon-S46 {
  font-size: 46px;
}
.fon-S48 {
  font-size: 48px;
}
.fon-S50 {
  font-size: 50px;
}
.fon-S52 {
  font-size: 52px;
}

.fon-W500 {
  font-weight: 500;
}
.fon-W600 {
  font-weight: 600;
}
.fon-W700 {
  font-weight: 700;
}

.back-transparent {
  background-color: transparent;
}
.back-white {
  background-color: #fff;
}
.back-f5f5f5 {
  background-color: #f5f5f5;
}
.back-eee {
  background-color: #eee;
}
.back-f0f0f0 {
  background-color: #f0f0f0;
}
.back-ccc {
  background-color: #ccc;
}
.back-999 {
  background-color: #999;
}
.back-666 {
  background-color: #666;
}
.back-333 {
  background-color: #333;
}
.back-000 {
  background-color: #000;
}
.back-pink {
  background-color: pink;
}
.back-f6 {
  background-color: #f6f7f9;
}

.pointer {
  cursor: pointer;
}

.color-white {
  color: #fff;
}
.color-333 {
  color: #333;
}
.color-666 {
  color: #666;
}
.color-999 {
  color: #999;
}
.color-ccc {
  color: #ccc;
}

.border-R4 {
  border-radius: 4px;
}
.border-R6 {
  border-radius: 6px;
}
.border-R8 {
  border-radius: 8px;
}
.border-R10 {
  border-radius: 10px;
}
.border-R12 {
  border-radius: 12px;
}
.border-R16 {
  border-radius: 16px;
}
.border-R20 {
  border-radius: 20px;
}
.border-Rall {
  border-radius: 50%;
}

.pad-4 {
  padding: 4px;
}
.pad-6 {
  padding: 6px;
}
.pad-8 {
  padding: 8px;
}
.pad-10 {
  padding: 10px;
}
.pad-12 {
  padding: 12px;
}
.pad-14 {
  padding: 14px;
}
.pad-16 {
  padding: 16px;
}
.pad-18 {
  padding: 18px;
}
.pad-20 {
  padding: 20px;
}
.pad-22 {
  padding: 22px;
}
.pad-24 {
  padding: 24px;
}
.pad-26 {
  padding: 26px;
}
.pad-28 {
  padding: 28px;
}
.pad-30 {
  padding: 30px;
}
.pad-32 {
  padding: 32px;
}
.pad-L6 {
  padding-left: 6px;
}
.pad-L8 {
  padding-left: 8px;
}
.pad-L10 {
  padding-left: 10px;
}
.pad-L12 {
  padding-left: 12px;
}
.pad-L14 {
  padding-left: 14px;
}
.pad-L16 {
  padding-left: 16px;
}
.pad-L18 {
  padding-left: 18px;
}
.pad-L20 {
  padding-left: 20px;
}
.pad-L22 {
  padding-left: 22px;
}
.pad-L24 {
  padding-left: 24px;
}
.pad-L26 {
  padding-left: 26px;
}
.pad-L28 {
  padding-left: 28px;
}
.pad-L30 {
  padding-left: 30px;
}
.pad-L32 {
  padding-left: 32px;
}
.pad-L34 {
  padding-left: 34px;
}
.pad-L36 {
  padding-left: 36px;
}
.pad-R6 {
  padding-right: 6px;
}
.pad-R8 {
  padding-right: 8px;
}
.pad-R10 {
  padding-right: 10px;
}
.pad-R12 {
  padding-right: 12px;
}
.pad-R14 {
  padding-right: 14px;
}
.pad-R16 {
  padding-right: 16px;
}
.pad-R18 {
  padding-right: 18px;
}
.pad-R20 {
  padding-right: 20px;
}
.pad-R22 {
  padding-right: 22px;
}
.pad-R24 {
  padding-right: 24px;
}
.pad-R26 {
  padding-right: 26px;
}
.pad-R28 {
  padding-right: 28px;
}
.pad-R30 {
  padding-right: 30px;
}
.pad-R32 {
  padding-right: 32px;
}
.pad-R34 {
  padding-right: 34px;
}
.pad-R36 {
  padding-right: 36px;
}
.pad-T4 {
  padding-top: 4px;
}
.pad-T6 {
  padding-top: 6px;
}
.pad-T8 {
  padding-top: 8px;
}
.pad-T10 {
  padding-top: 10;
}
.pad-T12 {
  padding-top: 12px;
}
.pad-T14 {
  padding-top: 14px;
}
.pad-T16 {
  padding-top: 16px;
}
.pad-T18 {
  padding-top: 18px;
}
.pad-T20 {
  padding-top: 20px;
}
.pad-T22 {
  padding-top: 22px;
}
.pad-T24 {
  padding-top: 24px;
}
.pad-T26 {
  padding-top: 26px;
}
.pad-T28 {
  padding-top: 28px;
}
.pad-T30 {
  padding-top: 30px;
}
.pad-T32 {
  padding-top: 32px;
}
.pad-T34 {
  padding-top: 34px;
}
.pad-T36 {
  padding-top: 36px;
}
.pad-B4 {
  padding-bottom: 4px;
}
.pad-B6 {
  padding-bottom: 6px;
}
.pad-B8 {
  padding-bottom: 8px;
}
.pad-B10 {
  padding-bottom: 10;
}
.pad-B12 {
  padding-bottom: 12px;
}
.pad-B14 {
  padding-bottom: 14px;
}
.pad-B16 {
  padding-bottom: 16px;
}
.pad-B18 {
  padding-bottom: 18px;
}
.pad-B20 {
  padding-bottom: 20px;
}
.pad-B22 {
  padding-bottom: 22px;
}
.pad-B24 {
  padding-bottom: 24px;
}
.pad-B26 {
  padding-bottom: 26px;
}
.pad-B28 {
  padding-bottom: 28px;
}
.pad-B30 {
  padding-bottom: 30px;
}
.pad-B32 {
  padding-bottom: 32px;
}
.pad-B34 {
  padding-bottom: 34px;
}
.pad-B36 {
  padding-bottom: 36px;
}

.pad-Y4 {
  padding: 4px 0;
}
.pad-Y6 {
  padding: 6px 0;
}
.pad-Y8 {
  padding: 8px 0;
}
.pad-Y10 {
  padding: 10px 0;
}
.pad-Y12 {
  padding: 12px 0;
}
.pad-Y14 {
  padding: 14px 0;
}
.pad-Y16 {
  padding: 16px 0;
}
.pad-Y18 {
}
.pad-Y20 {
  padding: 20px 0;
}
.pad-Y22 {
  padding: 22px 0;
}
.pad-Y24 {
  padding: 24px 0;
}
.pad-Y26 {
  padding: 26px 0;
}
.pad-Y28 {
  padding: 28px 0;
}
.pad-Y30 {
  padding: 30px 0;
}
.pad-Y32 {
  padding: 32px 0;
}
.pad-Y34 {
  padding: 34px 0;
}
.pad-Y36 {
  padding: 36px 0;
}
.pad-Y48 {
  padding: 48px 0;
}

.pad-X4 {
  padding: 0 4px;
}
.pad-X6 {
  padding: 0 6px;
}
.pad-X8 {
  padding: 0 8px;
}
.pad-X10 {
  padding: 0 10px;
}
.pad-X12 {
  padding: 0 12px;
}
.pad-X14 {
  padding: 0 14px;
}
.pad-X16 {
  padding: 0 16px;
}
.pad-X18 {
  padding: 0 18px;
}
.pad-X20 {
  padding: 0 20px;
}
.pad-X22 {
  padding: 0 22px;
}
.pad-X24 {
  padding: 0 24px;
}
.pad-X26 {
  padding: 0 26px;
}
.pad-X28 {
  padding: 0 28px;
}
.pad-X30 {
  padding: 0 30px;
}
.pad-X32 {
  padding: 0 32px;
}
.pad-X34 {
  padding: 0 34px;
}
.pad-X36 {
  padding: 0 36px;
}
.pad-X38 {
  padding: 0 38px;
}
.pad-X40 {
  padding: 0 40px;
}
.pad-X42 {
  padding: 0 42px;
}

.pad-X48 {
  padding: 0 48px;
}
.mar-4 {
  margin: 4px;
}
.mar-6 {
  margin: 6px;
}
.mar-8 {
  margin: 8px;
}
.mar-10 {
  margin: 10px;
}
.mar-12 {
  margin: 12px;
}
.mar-14 {
  margin: 14px;
}
.mar-16 {
  margin: 16px;
}
.mar-18 {
  margin: 18px;
}
.mar-20 {
  margin: 20px;
}
.mar-22 {
  margin: 22px;
}
.mar-24 {
  margin: 24px;
}
.mar-26 {
  margin: 26px;
}
.mar-28 {
  margin: 28px;
}
.mar-30 {
  margin: 30px;
}
.mar-T4 {
  margin-top: 4px;
}
.mar-T6 {
  margin-top: 6px;
}
.mar-T8 {
  margin-top: 8px;
}
.mar-T10 {
  margin-top: 10px;
}
.mar-T12 {
  margin-top: 12px;
}
.mar-T14 {
  margin-top: 14px;
}
.mar-T16 {
  margin-top: 16px;
}
.mar-T18 {
  margin-top: 18px;
}
.mar-T20 {
  margin-top: 20px;
}
.mar-T22 {
  margin-top: 22px;
}

.mar-T24 {
  margin-top: 24px;
}
.mar-T26 {
  margin-top: 26px;
}
.mar-T28 {
  margin-top: 28px;
}
.mar-T30 {
  margin-top: 30px;
}
.mar-B4 {
  margin-bottom: 4px;
}
.mar-B6 {
  margin-bottom: 6px;
}
.mar-B8 {
  margin-bottom: 8px;
}
.mar-B10 {
  margin-bottom: 10px;
}
.mar-B12 {
  margin-bottom: 12px;
}
.mar-B14 {
  margin-bottom: 14px;
}
.mar-B16 {
  margin-bottom: 16px;
}
.mar-B18 {
  margin-bottom: 18px;
}
.mar-B20 {
  margin-bottom: 20px;
}
.mar-B22 {
  margin-bottom: 22px;
}
.mar-B24 {
  margin-bottom: 24px;
}
.mar-B26 {
  margin-bottom: 26px;
}
.mar-B28 {
  margin-bottom: 28px;
}
.mar-B30 {
  margin-bottom: 30px;
}
.mar-L4 {
  margin-left: 4px;
}
.mar-L6 {
  margin-left: 6px;
}
.mar-L8 {
  margin-left: 8px;
}
.mar-L10 {
  margin-left: 10px;
}
.mar-L12 {
  margin-left: 12px;
}
.mar-L14 {
  margin-left: 14px;
}
.mar-L16 {
  margin-left: 16px;
}
.mar-L18 {
  margin-left: 18px;
}
.mar-L20 {
  margin-left: 20px;
}
.mar-L22 {
  margin-left: 22px;
}
.mar-L24 {
  margin-left: 24px;
}
.mar-L26 {
  margin-left: 26px;
}
.mar-L28 {
  margin-left: 28px;
}
.mar-L30 {
  margin-left: 30px;
}
.mar-R4 {
  margin-right: 4px;
}
.mar-R6 {
  margin-right: 6px;
}
.mar-R8 {
  margin-right: 8px;
}
.mar-R10 {
  margin-right: 10px;
}
.mar-R12 {
  margin-right: 12px;
}
.mar-R14 {
  margin-right: 14px;
}
.mar-R16 {
  margin-right: 16px;
}
.mar-R18 {
  margin-right: 18px;
}
.mar-R20 {
  margin-right: 20px;
}
.mar-R22 {
  margin-right: 22px;
}
.mar-R24 {
  margin-right: 24px;
}
.mar-R26 {
  margin-right: 26px;
}
.mar-R28 {
  margin-right: 28px;
}
.mar-R30 {
  margin-right: 30px;
}
.mar-X4 {
  margin: 0 4px;
}
.mar-X6 {
  margin: 0 6px;
}
.mar-X8 {
  margin: 0 8px;
}
.mar-X10 {
  margin: 0 10px;
}
.mar-X12 {
  margin: 0 12px;
}
.mar-X14 {
  margin: 0 14px;
}
.mar-X16 {
  margin: 0 16px;
}
.mar-X18 {
  margin: 0 18px;
}
.mar-X20 {
  margin: 0 20px;
}
.mar-X22 {
  margin: 0 22px;
}
.mar-X24 {
  margin: 0 24px;
}
.mar-X26 {
  margin: 0 26px;
}
.mar-X28 {
  margin: 0 28px;
}
.mar-X30 {
  margin: 0 30px;
}
.mar-Y4 {
  margin: 4px 0;
}
.mar-Y6 {
  margin: 6px 0;
}
.mar-Y8 {
  margin: 8px 0;
}
.mar-Y10 {
  margin: 10px 0;
}
.mar-Y12 {
  margin: 12px 0;
}
.mar-Y14 {
  margin: 14px 0;
}
.mar-Y16 {
  margin: 16px 0;
}
.mar-Y18 {
  margin: 18px 0;
}
.mar-Y20 {
  margin: 20px 0;
}
.mar-Y22 {
  margin: 22px 0;
}
.mar-Y24 {
  margin: 24px 0;
}
.mar-Y26 {
  margin: 26px 0;
}
.mar-Y28 {
  margin: 28px 0;
}
.mar-Y30 {
  margin: 30px 0;
}
