html,
body {
  box-sizing: border-box;
}
#app,
.app,
.page {
  height: 100%;
  width: 100%;
}

.mapboxgl-ctrl-bottom-left,
.mapboxgl-ctrl-bottom-right,
.mapboxgl-ctrl-top-left,
.mapboxgl-ctrl-top-right {
  pointer-events: auto !important;
}

div {
  box-sizing: border-box;
}

::-webkit-scrollbar {
  width: 6px;
}

::-webkit-scrollbar-thumb {
  background-color: #ccc;
}

::-webkit-scrollbar-track {
  background-color: #f1f1f1;
}

::-webkit-scrollbar-thumb {
  border-radius: 10px;
}

.mapboxgl-ctrl-bottom-left .mapboxgl-ctrl {
  display: none !important;
}

.mapboxgl-popup-content {
  padding: 0 !important;
}

.merkerwarp {
  width: 35px;
  height: 35px;
  display: flex;
  justify-content: center;
  padding-top: 2px;
  cursor: pointer;

  .img_warp {
    width: 22px;
    height: 22px;
    background-color: #fff;
    border-radius: 50%;
    border: 1px solid #999;
    display: flex;
    justify-content: center;
    align-items: center;
  }
}

div[aria-hidden='true'] {
  display: none !important;
}
button[aria-hidden='true'] {
  display: none !important;
}

@media (prefers-contrast: high) {
  /* 高对比度样式 */
  body {
    background-color: black;
    color: white;
  }
}
