<template>
  <div class="wrap">
    <template v-for="item in data" :key="item.label">
      <div class="item" @click="emit('choose', item)">
        <div v-if="item.color" :style="`background-color: ${item.color}`"></div>
        <img class="img" v-else :src="item.url" />
        <span>{{ item.label }}</span>
      </div>
    </template>
  </div>
</template>

<script setup>
defineProps({
  data: {
    type: Array,
    default: [
      { color: '#ee0000', label: '福中水务所' },
      { color: '#bdfb9b', label: '福东水务所' },
      { color: '#1677ff', label: '梅林水务所' },
      { color: '#fa9600', label: '香蜜水务所' },
      { color: '#999999', label: '空地' }
    ]
  }
})
const emit = defineEmits(['choose'])
</script>

<style lang="less" scoped>
.wrap {
  position: absolute;
  bottom: 20px;
  left: 12px;
  z-index: 100;
  padding: 6px 10px;
  background: #ffffff55;
  border-radius: 10px;
  border: 1px solid #ffffff;
  .item {
    display: flex;
    padding: 6px 12px;
    cursor: pointer;
    & > div {
      width: 20px;
      height: 20px;
      margin-right: 12px;
      border-radius: 5px;
    }
    .img {
      width: 20px;
      height: 20px;
      margin-right: 12px;
      border-radius: 5px;
    }
    & > span {
      font-size: 14px;
      font-weight: 600;
      color: #333;
    }
    &:hover {
      border-radius: 12px;
      background: #ffffff55;
    }
  }
}
</style>

<style lang="less">
.wrap {
  .item {
    &:hover {
      background: #ffffff33;
    }
  }
}
</style>

<style lang="less">
.wrap {
  .item {
    &:hover {
      background: #ffffff33;
    }
  }
}
</style>
