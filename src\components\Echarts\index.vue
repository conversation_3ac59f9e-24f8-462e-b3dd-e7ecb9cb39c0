<template>
  <div class="all" :id="Id"></div>
</template>

<script setup>
import { onMounted, watch, onBeforeUnmount, ref } from 'vue'
import echarts from './index.js'

const props = defineProps({ option: { type: Object, default: () => {} }, events: { type: Object, default: () => ({}) } })
const generateRandomId = () => Math.floor(10000000 + Math.random() * 90000000).toString()
const Id = generateRandomId()
let MyEchart
onMounted(() => initEchart())
onBeforeUnmount(unmountCallback)

watch(props, (newVal) => updateData(newVal.option), { deep: true })
function initEchart() {
  MyEchart = echarts.init(document.getElementById(Id))
  MyEchart.setOption(props.option)
  window.addEventListener('resize', resize)
  observeElement()
  addEvents(props.events)
}
function resize() {
  MyEchart.resize()
}
function updateData(options) {
  MyEchart.setOption(options)
}
function unmountCallback() {
  window.removeEventListener('resize', resize)
  MyEchart.dispose()
}
function observeElement() {
  const resizeObserver = new ResizeObserver(() => MyEchart?.resize())
  resizeObserver.observe(document.getElementById(Id))
}

function addEvents(events) {
  const entries = Object.entries(events)
  for (const [eventName, callback] of entries) {
    MyEchart.on(eventName, callback)
  }
}

defineExpose({ MyEchart })
</script>
