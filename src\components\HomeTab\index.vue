<template>
  <div class="home_tab">
    <template v-for="item in tabs" :key="item.key">
      <div class="button" @click="handerClick(item)">{{ item.key }}</div>
    </template>
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue'
import { useRouter, useRoute } from 'vue-router'

const tabs = reactive([
  {
    key: '首页',
    path: '/main/home'
  },
  {
    key: '工程进度',
    path: '/main/schedule/fineWater'
  },
  {
    key: '考核评分',
    path: '/main/assess/dailyOperation'
  },
  {
    key: 'BI相关',
    path: '/main/bi'
  }
])
const router = useRouter()
const route = useRoute()

function handerClick(e) {
  if (e.path) return router.push(e.path)
}
</script>

<style lang="less" scoped>
.home_tab {
  width: 500px;
  display: flex;
  justify-content: center;
  .button {
    height: 40px;
    cursor: pointer;

    margin: 0 6px;
    background-color: rgba(16, 120, 224, 0.3); /* Dark blue background */
    color: #ffffff; /* White text */
    padding: 10px 20px; /* Padding for text */
    text-align: center; /* Center text */
    text-decoration: none; /* Remove default text decoration */
    display: inline-block; /* Display as inline block */
    font-size: 16px; /* Font size */
    border-radius: 5px; /* Rounded corners */
    box-shadow: 0px 2px 5px #000000; /* Shadow effect */
    transition: all 1s ease-in-out;
    box-shadow: 0px 0px 12px #00ffff00 inset;
    &:hover {
      box-shadow: 0px 0px 12px #00ffff inset;
    }
  }
}
</style>
