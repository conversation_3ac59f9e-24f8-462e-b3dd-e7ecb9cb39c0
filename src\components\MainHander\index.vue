<template>
  <div class="hander">
    <div class="title">福田智能供排水决策支持系统</div>
    <a-menu forceSubMenuRender class="hander_menu" v-model:selectedKeys="current" mode="horizontal" @click="handerClick" :items="items" />
    <div class="right relative">
      <!-- <SearchOutlined class="left20" />
      <BellOutlined class="left20" /> -->
      <div class="user" @click="toAppVersion">
        <a-avatar :src="userInfo?.path ?? '#'" />
        <div class="user_name">{{ userInfo.name }}</div>
      </div>
      <img src="@/assets/images/exit.png" @click="open = true" class="absolute pointer" style="width: 30px; right: 20px; top: 20px" />
    </div>
  </div>

  <a-modal v-model:open="open" width="260px" title="退出登录" centered @ok="handerExitClick">
    <div class="">确认退出登录？</div>
  </a-modal>
</template>

<script setup>
import { ref, watch } from 'vue'
import { SearchOutlined, BellOutlined } from '@ant-design/icons-vue'
import { useRouter, useRoute } from 'vue-router'
import Cache from '@/utils/cache'
import { message } from 'ant-design-vue'

const router = useRouter()
const route = useRoute()
const current = ref([])

const userInfo = ref({ name: '未登录' })
const open = ref(false)

// 根据route.name设置current
watch(
  route,
  ({ name }) => {
    current.value = [name]
    userInfo.value = Cache.get('userInfo')
  },
  { immediate: true }
)

const items = [
  { key: '首页', label: '首页', path: '/main/home' },
  {
    label: '工程进度',
    children: [
      { key: '优饮小区', label: '优饮小区', path: '/main/schedule/fineWater' },
      { key: '二供泵房', label: '二供泵房', path: '/main/schedule/twiceProvide' },
      { key: '新建项目', label: '新建项目', path: '/main/schedule/constructionSite' },
      { key: '总表核对', label: '总表核对', path: '/main/schedule/examine' },
      { key: '小区档案', label: '小区档案', path: '/main/schedule/menu-map' },
      { key: '小额工程', label: '小额工程', path: '/main/schedule/small-works' }
    ]
  },
  {
    key: '考核评分',
    label: '考核评分',
    children: [
      { key: '日常运维', label: '日常运维', path: '/main/assess/dailyOperation' },
      { key: '外业验收', label: '外业验收', empty: true, path: '/main/assess/workAcceptance' },
      { key: '集团考核', label: '集团考核', empty: true, path: '/main/assess/groupAssessment' },
      { key: '管网自评', label: '管网自评', empty: true, path: '/main/assess/selfReview' }
      // { key: '数字验收', label: '数字验收', path: '/main/assess/digitalAcceptance' }
    ]
  },
  {
    key: 'bi',
    label: 'BI相关',
    children: [
      { key: '污水 | 积水点', label: '污水 | 积水点', path: '/main/bi/sewage' },
      {
        key: '供水',
        label: '供水',
        path: '#',
        onClick: () => window.open('http://qbi.cloud.sz-water.com.cn/token3rd/dashboard/view/pc.htm?pageId=8c22f997-19e6-4b2e-8a74-d6654aaae6d5&accessToken=8130bf105fae8f056ca2093948cd2c5a&dd_orientation=auto', '_blank')
      },
      {
        key: '排水',
        label: '排水',
        path: '#',
        onClick: () => window.open('http://qbi.cloud.sz-water.com.cn/token3rd/dashboard/view/pc.htm?pageId=af2cccb5-af40-47f9-88c3-60e2de1458ef&accessToken=755c4c3297c4597fb0e0ff4851339798&dd_orientation=auto', '_blank')
      }
    ]
  },
  {
    key: '综合分析',
    label: '综合分析',
    children: [{ key: '排水管道充盈度', label: '排水管道充盈度', path: '/main/synthesize/RainSludgeLevelPoint' }]
  }
  // {
  //   key: 'pandect',
  //   label: '领导驾驶舱',
  //   path: '/pandect'
  // }
]

// 导航栏点击事件处理
function handerClick(e) {
  if (e.item.empty) return message.warning('页面开发中，敬请期待！', 3)
  if (e.item.path) return router.push(e.item.path)
}

function handerExitClick() {
  Cache.clear()
  router.push('/login')
}

function toAppVersion() {
  const name = Cache.get('userInfo')?.name
  if (name === '开发账号') router.push('/appVersion')
}
</script>

<style lang="less" scoped>
.hander {
  padding-left: 280px;
  height: 66px;
  background: #fff;
  display: flex;
  align-items: center;
  box-shadow: 3px 3px 6px 1px rgba(0, 0, 0, 0.1);
  .title {
    font-size: 24px;
    font-weight: 600;
    color: #999;
    margin-right: 30px;
  }
  .hander_menu {
    height: 100%;
    line-height: 66px;
    flex: 1;
  }
  .right {
    padding-right: 120px;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;

    .user {
      display: flex;
      align-items: center;
      margin-left: 20px;
      .user_name {
        margin-left: 10px;
      }
    }
  }
  .left20 {
    margin-left: 20px;
    cursor: pointer;
  }

  //   :deep(.ant-menu-item-selected) {
  //     padding: 0 40px;
  //   }
  //   :deep(.ant-menu-light) {
  //     background: #2a7ef5;
  //   }
  :deep(.ant-menu-item) {
    padding: 0 40px;
  }
  :deep(.ant-menu-title-content) {
    font-weight: 700;
  }
}
</style>
