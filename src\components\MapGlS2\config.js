// 基础默认配置
const basicsConfig = {
  map: {
    accessToken: 'pk.eyJ1Ijoibm9ydGh2aSIsImEiOiJjbGVydjM1OXYwMnpkM3BxZGw5Ynlrbm13In0.queCazXHMgl8WHfZ1lF4xg',
    center: [114.05528061331722, 22.54140197444606], //中心点
    zoom: 13, //默认缩放
    minZoom: 10, //最小缩放
    maxZoom: 24, //最大缩放
    projection: 'globe', //球形地图
    antialias: true, //抗锯齿
    maxPitch: 60,
    style: {
      version: 8,
      sources: {
        'osm-tiles1': { type: 'raster', tiles: ['https://t4.tianditu.gov.cn/DataServer?T=vec_w&x={x}&y={y}&l={z}&tk=c4422fec9d5e394411da10d3f1838c84'], tileSize: 256, maxzoom: 18 },
        'osm-tiles2': { type: 'raster', tiles: ['https://t4.tianditu.gov.cn/DataServer?T=cva_w&x={x}&y={y}&l={z}&tk=c4422fec9d5e394411da10d3f1838c84'], tileSize: 256, maxzoom: 18 }
      },
      glyphs: 'mapbox://fonts/mapbox/{fontstack}/{range}.pbf',
      layers: [
        { id: 'simple-tiles1', type: 'raster', source: 'osm-tiles1' },
        { id: 'simple-tiles2', type: 'raster', source: 'osm-tiles2' }
      ]
    }
  }
}

export default basicsConfig
