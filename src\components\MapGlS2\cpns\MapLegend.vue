<template>
  <div class="legend absolute pointer">
    <slot name="top"></slot>
    <slot>
      <template v-for="(item, index) in legendData" :key="item.label">
        <div :class="{ item: true }" @click="choose(item, index)">
          <div v-if="item.color" :style="`background-color: ${item.color}`"></div>
          <img class="img" v-else :src="item.url" />
          <span>{{ item.label }}</span>
          <span class="mar-L12">{{ item.value }}</span>
        </div>
      </template>
    </slot>
    <slot name="bottom"></slot>
  </div>
</template>

<script setup>
defineProps({ legendData: { type: Array, default: () => [] } })
const emit = defineEmits(['choose'])

function choose(item, index) {
  emit('choose', item, index)
}
</script>

<style lang="less" scoped>
.legend {
  position: absolute;
  bottom: 20px;
  left: 24px;
  z-index: 100;
  padding: 6px 10px;
  background: #ffffff55;
  border-radius: 10px;
  border: 1px solid #ffffff;
  box-shadow: 0px 4px 4px rgba(0, 0, 0, 0.4);
  &:empty {
    display: none;
  }

  .item {
    display: flex;
    padding: 6px 12px;
    cursor: pointer;
    & > div {
      width: 20px;
      height: 20px;
      margin-right: 12px;
      border-radius: 5px;
    }
    .img {
      width: 20px;
      height: 20px;
      margin-right: 12px;
      border-radius: 5px;
    }
    & > span {
      font-size: 14px;
      font-weight: 600;
      color: #333;
    }
    &:hover {
      border-radius: 8px;
      background: #ffffff55;
    }
  }
}
.active {
  border-radius: 12px;
  background: #ffffff55;
}
</style>
