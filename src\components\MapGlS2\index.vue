<template>
  <div class="all relative">
    <div :id style="height: 100%; width: 100%"></div>
    <slot></slot>
    <div class="left0 top0 absolute z-index-100"><slot name="leftTop"></slot></div>
    <div class="left0 bottom0 absolute z-index-100"><slot name="leftBottom"></slot></div>
    <div class="right0 top0 absolute z-index-100"><slot name="rightTop"></slot></div>
    <div class="right0 bottom0 absolute z-index-100"><slot name="rightBottom"></slot></div>

    <MapLegend :legendData="legend" @choose="(value, index) => emit('choose', value, index)">
      <template #top>
        <slot name="legendTop"></slot>
      </template>
      <template #bottom>
        <slot name="legendBottom"></slot>
      </template>
    </MapLegend>
  </div>
</template>

<script setup lang="ts">
import { onMounted, onBeforeUnmount } from 'vue'
import 'mapbox-gl/dist/mapbox-gl.css'
import mapboxgl from 'mapbox-gl'
import basicsConfig from './config'
import MapLegend from './cpns/MapLegend.vue'
import type { Map as MapType } from 'mapbox-gl'

import type { IProps } from './types'

const props = defineProps<IProps>()

const emit = defineEmits(['choose', 'load'])
const id = Math.random().toString(36)

let Map: MapType

onMounted(mountedCallBack)

onBeforeUnmount(() => {
  Map.remove()
  Map = null as any
})
async function mountedCallBack() {
  Map = await initMap()
  Map.on('load', mapLoadCallBack)
  Map.updatedData = updatedData
}
function mapLoadCallBack() {
  loadingLayer(props?.options?.sources!) //添加区块图层
  emit('load', Map)
}

// 返回地图实例
const getMapInstance = () => Map

// 地图初始化
async function initMap() {
  const accessToken = props?.options?.map?.accessToken ?? basicsConfig.map.accessToken
  const mapConfig = props?.options?.map ?? {}
  mapboxgl.accessToken = accessToken
  const options = { container: id, ...basicsConfig.map, ...mapConfig }
  return new mapboxgl.Map(options as any)
}

const sourceTypeHandlerFns: any = {
  vector: (Options: any) => ({ type: 'vector', tiles: Options.tiles, scheme: Options.scheme, ...Options.options }),
  raster: (Options: any) => ({ type: 'raster', tiles: Options.tiles, ...Options.options }),
  geojson: (Options: any) => ({ type: 'geojson', data: Options.data ?? { type: 'FeatureCollection', features: [] }, ...Options.options }),
  image: () => {},
  video: () => {},
  canvas: () => {}
}

// 添加图层
function loadingLayer(Options: any) {
  Object.entries(Options ?? {}).forEach(forEachSourceCallBack)
  function forEachSourceCallBack([sourceName, sourceOptions]: any) {
    const { type } = sourceOptions

    Map.addSource(sourceName, sourceTypeHandlerFns[type](sourceOptions))

    Object.entries(sourceOptions.layers ?? {}).forEach(forEachLayerCallBack)
    function forEachLayerCallBack([layerName, layerOptions]: any) {
      const layerType = layerName.split('_')[1]
      Map.addLayer({ id: layerName, type: layerType, source: sourceName, ...layerOptions.options }, layerOptions.above ?? null)
      Object.entries(layerOptions.events ?? {}).forEach(forEachEventCallBack)
      Object.entries(layerOptions.images ?? {}).forEach(forEachImageCallBack)

      function forEachEventCallBack([eventName, eventFn]: any) {
        Map.on(eventName, layerName, primaryEventCallback)
        function primaryEventCallback(e: any) {
          const [feature] = Map.queryRenderedFeatures(e.point, { layers: [layerName] })
          return eventFn(e, feature, Map)
        }
      }

      function forEachImageCallBack([imageName, imageUrl]: any) {
        Map.loadImage(imageUrl, (error, image) => Map.addImage(imageName, image!))
      }
    }
  }
}

/** 更新某个数据源
 * @param {String} sourceName 数据源名称
 * @param {Array} data 数据
 * @param {Function} beforeTreatment 更新前调用函数
 */
function updatedData(sourceName: string, data: any, beforeTreatment: (data: any) => void) {
  if (beforeTreatment) beforeTreatment(data)
  const source = Map.getSource(sourceName) as any
  const updatedGeojsonData = { type: 'FeatureCollection', features: data }
  // 设置更新后的 GeoJSON 数据
  source.setData(updatedGeojsonData)
}

defineExpose({ getMapInstance, loadingLayer, updatedData })
</script>

<style lang="less" scoped>
.top0 {
  top: 0;
}
.left0 {
  left: 0;
}
.bottom0 {
  bottom: 0;
}
.right0 {
  right: 0;
}
</style>

<style>
.mapboxgl-ctrl-bottom-left {
  display: none;
}

.mapboxgl-popup-close-button {
  font-size: 28px !important;
  font-weight: 700;
  color: #535353 !important;
}
</style>
