import { MapboxOptions } from 'mapbox-gl'

export interface IProps {
  options?: MapOptions
  legend?: any
}

export interface MapOptions {
  sources: SourcesOptions
  map?: MapConfig
}

export interface MapConfig extends MapboxOptions {
  accessToken?: string
}

export interface SourcesOptions {
  type: string
  source: string
  data: any
  layers: Layer[]
  sourceOptions?: any
}

export interface Layer {
  id: string
  options?: any
  type: any
  events?: any
}

export {}
