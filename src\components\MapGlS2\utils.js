import { createApp } from 'vue'

/**
 * 使用动画效果将地图飞向指定的中心点和缩放级别。
 * @param {Object} map 地图对象，执行飞向操作的目标地图。
 * @param {Array} center 飞向目标的中心点坐标，格式为 [经度, 纬度]。
 * @param {number} zoom 飞向目标的缩放级别。
 */
export function flyTo(map, center, zoom) {
  // 使用地图的flyTo方法进行平滑过渡到指定的中心点和缩放级别
  map.flyTo({ center, zoom, curve: 1.5, duration: 2000 })
}

/**
 * 根据给定的条件改变地图图层的可见性。
 * @param {Object} map 地图对象，必须具有setLayoutProperty方法。
 * @param {string} layerId 图层的唯一标识符，用于指定要改变可见性的图层。
 * @param {boolean} type 一个布尔值，决定图层是否应显示。true表示显示，false表示隐藏。
 */
export function changeShowLayer(map, layerId, type) {
  // 根据type的值设置图层的可见性为'visible'或'none'
  type
    ? map.setLayoutProperty(layerId, 'visibility', 'visible') // 显示图层
    : map.setLayoutProperty(layerId, 'visibility', 'none') // 隐藏图层
}

/**
 * 添加图层到地图的辅助函数
 * @param {Map} map 地图对象，向其添加图层
 * @param {string} id 图层的唯一标识符
 * @param {'circle'|'line'|'fill'|'symbol'|'raster'|'heatmap'|'fill-extrusion'} type 图层的类型，支持多种图层类型
 * @param {Object} options 图层的配置选项，可选参数，用于扩展或覆盖初始图层设置
 * @returns {void} 无返回值
 */
export function addLayer(map, id, type, source, options) {
  // 使用给定的参数向地图添加图层
  map.addLayer({ id, type, source, ...options })
}

// 添加图片
export function addimage(map, url) {
  return new Promise((resolve, reject) => {
    map.loadImage(url, (err, image) => {
      if (err) reject(err)
      resolve(image)
    })
  })
}

/**
 * 将数据转化成geojson格式数据数据
 * @param {*} val
 * @param {*} X
 * @param {*} Y
 * @returns {geojson}
 */
export function transitionData(val, X, Y) {
  const features = val.map((item, index) => {
    return {
      type: 'Feature',
      id: index,
      properties: item,
      geometry: {
        type: 'Point',
        coordinates: [item[X], item[Y]]
      }
    }
  })

  return { type: 'FeatureCollection', features }
}

/**
 * 创建一个过渡组件。
 *
 * @param {Component} component - 要创建的组件。
 * @returns {Function} 返回一个函数，这个函数接收props作为参数，然后返回一个Mounted的DOM元素。
 */
export const transitionComponent = (component) => (props) => {
  // 创建一个div元素作为容器
  const dom = document.createElement('div')
  // 使用传入的组件和属性创建一个应用实例，并挂载到dom上
  const { $el } = createApp(component, props).mount(dom)
  // 返回挂载后的元素
  return $el
}
