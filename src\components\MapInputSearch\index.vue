<template>
  <div class="inpout_search box-shadow">
    <a-input-search v-model:value="searchD.value" placeholder="区块搜索" @focus="emit('focus')" bordered enter-button size="large" @change="throttleSearch" @search="onSearch">
      <template #suffix>
        <a-tooltip title="清除搜索"> <CloseOutlined v-show="searchD.value" @click="handlerClean" /> </a-tooltip>
      </template>
    </a-input-search>

    <!-- 下拉列表 -->
    <div class="list">
      <template v-for="item in searchD.list" :key="item.Zone_Code">
        <div class="item" @click="handlerZoneClick(item)">
          <div class="text-nowrap">{{ item.Zone_Name }}</div>
        </div>
      </template>
    </div>
  </div>
</template>

<script setup>
import { reactive } from 'vue'
import { CloseOutlined } from '@ant-design/icons-vue'
import { getZoneDataLike } from '@/services/modules/map'
import { message } from 'ant-design-vue'
import throttle from 'lodash/throttle'

const emit = defineEmits(['handlerZoneClick', 'focus'])
const searchD = reactive({ value: '', list: [] })

// 模糊查询区块
async function onSearch() {
  if (!searchD.value) return message.warning('请输入区块名称')
  const result = await getZoneDataLike(searchD.value)
  searchD.list = JSON.parse(result.data)
  if (!searchD.list.length) return message.warning('未查询到有效区块')
}
// 置空搜索框与搜索列表数据
function handlerClean() {
  searchD.value = ''
  searchD.list = []
}

function handlerZoneClick(data) {
  emit('handlerZoneClick', data)
  handlerClean()
}

const throttleSearch = throttle(onSearch, 500)
</script>

<style lang="less" scoped>
.inpout_search {
  background: #fff;
  border-radius: 8px;
  overflow: hidden;
  .list {
    max-height: 400px;
    overflow-y: auto;
    width: 299px;
    transition: all 0.5s;
    .item {
      padding: 10px 16px;
      border-bottom: 1px solid #f0f0f0;
      transition: all 0.5s;
      font-size: 12px;
      cursor: pointer;
      display: flex;
      justify-content: space-between;
      align-items: center;
      &:hover {
        padding-left: 20px;
        background: #eeeeee;
        border-bottom: 1px solid #999;
      }
      .item_name {
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }
    }
  }
}
</style>
