<template>
  <div class="map-operate">
    <div class="map-operate-left">
      <slot name="left"></slot>
    </div>
    <div class="map-operate-center">
      <slot name="center"></slot>
    </div>
    <div class="map-operate-right">
      <slot name="right"></slot>
      <slot></slot>
    </div>
  </div>
</template>

<script setup></script>

<style lang="less" scoped>
.map-operate {
  padding: 8px 24px;
  background-color: white;
  border: 1px solid #eee;
  display: flex;

  &-left {
    flex: 1;
  }
  &-center {
    text-align: center;
  }
  &-right {
    flex: 1;
    text-align: right;
  }
}
</style>
