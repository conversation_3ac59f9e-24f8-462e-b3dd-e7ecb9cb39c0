<template>
  <a-card title="添加数据" :bordered="false">
    <div class="content">
      <div class="flex mar-Y12">
        <span>所属类型：</span>
        <a-select v-model:value="type" style="width: 120px" :options="options" @change="(e) => (type = e)" />
      </div>
    </div>
    <div class="f-x-center">
      <a-button class="W100" type="primary" @click="handlerClick">提交</a-button>
    </div>
  </a-card>
</template>

<script setup>
import { ref, onMounted, watch } from 'vue'
import locale from 'ant-design-vue/es/date-picker/locale/zh_CN'
import { message } from 'ant-design-vue'
import { useBiStore } from '@/store/bi'

const props = defineProps({ data: Object, time: String })

const type = ref('')
const { addBiDotData } = useBiStore()

const options = [
  { value: '积水点', label: '积水点' },
  { value: '污水外冒点', label: '污水外冒点' }
]

async function handlerClick() {
  if (!type.value) return message.error('参数缺失')
  const data = {
    Type: type.value,
    ManagerName: props.data.SWS,
    Grid: props.data.name,
    Time: props.time + '-01',
    Y: props.data.lat,
    X: props.data.lng
  }

  await addBiDotData(data)
}
</script>
