<template>
  <a-card :title="data.Zone_Name" :bordered="false" style="max-width: 450px">
    <div class="content">
      <div class="item">
        <span>所属水务：</span><span>{{ data.ManagerName ?? '--' }}</span>
      </div>
      <div class="item">
        <span>所属街道：</span><span>{{ data.Subdistrict ?? '--' }}</span>
      </div>
      <div class="item">
        <span>所属社区：</span><span>{{ data.Community ?? '--' }}</span>
      </div>
      <div class="item">
        <span>区块类别：</span><span>{{ data.Zone_Type ?? '--' }}</span>
      </div>
      <div class="item">
        <span>所属路段：</span><span>{{ data.Road ?? '--' }}</span>
      </div>
      <div class="item">
        <span>优饮批次:</span><span>{{ data.GoodDrinkBatch ?? '--' }}</span>
      </div>
      <div class="item">
        <span>优饮完成时间:</span><span>{{ data.GoodDrinkingFinishTime ?? '--' }}</span>
      </div>
      <div class="item">
        <span>二供批次:</span><span>{{ data.SecondarySupplylot ?? '--' }}</span>
      </div>
      <div class="item">
        <span>二供情况:</span><span>{{ data.SecondarySupplyCondition ?? '--' }}</span>
      </div>
      <div class="item">
        <span>管网风险分级:</span><span>{{ data.PipeNetworkRiskClassification ?? '--' }}</span>
      </div>
      <div class="item">
        <span>二供风险分级:</span><span>{{ data.SecondaryRiskClassification ?? '--' }}</span>
      </div>
      <div class="item">
        <span>水质投诉分级:</span><span>{{ data.WaterQualityComplaint ?? '--' }}</span>
      </div>
      <div v-if="data.Client_Name"></div>
      <div v-if="data.Client_Name" class="flex f-row-reverse"><a-button type="primary" @click="handlerClick(data.Client_Name)">小区档案</a-button></div>
    </div>
  </a-card>
</template>

<script setup>
import useHomeStore from '@/store/home'

const { getMenu } = useHomeStore()
defineProps({ data: Object })

const handlerClick = (code) => {
  getMenu(code)
}
</script>

<style lang="less" scoped>
.content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  grid-gap: 10px;
}

.item {
  font-size: 14px;
  & > span:nth-child(1) {
    color: #535353;
    font-weight: 600;
  }
}
</style>

<style>
.mapboxgl-popup-content {
  padding: 0;
  box-shadow: 3px 3px 10px rgba(0, 0, 0, 0.5);
  border-radius: 5px;
  border: none;
  font-family: 'Microsoft YaHei', sans-serif;
}

.mapboxgl-popup-close-button {
  font-size: 26px;
  color: #535353;
}
</style>
