<template>
  <a-upload v-model:file-list="images" :headers :action="url" list-type="picture-card" @preview="handlePreview">
    <div v-if="images.length < length">
      <plus-outlined />
      <div style="margin-top: 8px">upload</div>
    </div>
  </a-upload>
  <a-modal :open="previewVisible" style="width: 800px" :title="previewTitle" :footer="null" @cancel="handleCancel">
    <img alt="example" style="width: 100%" :src="previewImage" />
  </a-modal>
</template>

<script setup>
import { ref, watch, computed } from 'vue'
import { PlusOutlined } from '@ant-design/icons-vue'
import Cache from '@/utils/cache'

const props = defineProps({ url: String, length: { type: Number, default: 2 } })
const emit = defineEmits(['update'])
const images = defineModel({ default: () => [] })

watch(images, (val) => {
  const url = val.filter((i) => i.response).map((i) => i.response.data)
  if (url.length) emit('update', url)
})
const headers = computed(() => ({ Authorization: `Bearer ${Cache.get('userInfo')?.token}` }))
function getBase64(file) {
  return new Promise((resolve, reject) => {
    const reader = new FileReader()
    reader.readAsDataURL(file)
    reader.onload = () => resolve(reader.result)
    reader.onerror = (error) => reject(error)
  })
}
const previewVisible = ref(false)
const previewImage = ref('')
const previewTitle = ref('')
const handleCancel = () => {
  previewVisible.value = false
  previewTitle.value = ''
}
const handlePreview = async (file) => {
  if (!file.url && !file.preview) {
    file.preview = await getBase64(file.originFileObj)
  }
  previewImage.value = file.url || file.preview
  previewVisible.value = true
  previewTitle.value = file.name || file.url.substring(file.url.lastIndexOf('/') + 1)
}

function reset() {
  images.value = []
}

defineExpose({ reset })
</script>

<style lang="less" scoped></style>
