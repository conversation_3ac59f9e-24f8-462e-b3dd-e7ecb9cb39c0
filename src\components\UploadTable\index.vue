<template>
  <!-- 上传表个数据 -->
  <div class="code">
    <a-input class="inputA" type="file" accept=".xlsx" @change="handleChange" />
    <div class="iconbox">
      <FileTextTwoTone class="icon" v-if="step === 0" />
      <LoadingOutlined class="icon" v-else />
      <div class="label">{{ hint[step] }}</div>
    </div>
    <div class="cover" v-if="isShow">
      <a-progress :stroke-color="{ '0%': '#108ee9', '100%': '#87d068' }" :percent="schedule" />
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import axios from 'axios'
import XLSX from 'xlsx'
import JSZip from 'jszip'

import { FileTextTwoTone, LoadingOutlined } from '@ant-design/icons-vue'
import { message } from 'ant-design-vue'

const schedule = ref(0)
const isShow = ref(false)
const step = ref(0)
const hint = ['选择文件', '正在解析文件', '文件上传中']

async function handleChange(evt) {
  const file = evt.target.files[0]
  if (!file) return
  isShow.value = true
  schedule.value = 0
  // 文件上传数组
  const imgUpdateFns = await extractImgIdentifier(file)
  /* 读取文件 */
  const data = await readXlxsFile(file)
  // 去除表头
  data.shift()
  const keys = ['a', 'b', 'c', 'd', 'e', 'f', 'g', 'h', 'i', 'j', 'k', 'l', 'm', 'n', 'o', 'p', 'q', 'r', 's', 't', 'u']

  // 数据上传
  const length = data.length
  step.value = 2
  for (let i = 0; i < length; i++) {
    const item = data[i]
    const obj = {}
    // const result = await imgUpdateFns[i]()
    // item[13] = result.data
    // keys.forEach((key, i) => (obj[key] = item[i]))

    // 上传操作
    schedule.value = Math.floor(((i + 1) / length) * 100)
  }

  //   重置状态
  setTimeout(() => (isShow.value = false), 1500)
  step.value = 0
  message.success('上传成功')
}

// 图片上传处理
async function extractImgIdentifier(file) {
  const zip = new JSZip()
  const { files } = await zip.loadAsync(file)
  const imageFileKeys = Object.keys(files).filter((key) => key.indexOf('media/image') != -1 && !key.dir)
  const imageFilePromises = imageFileKeys.map((key) => {
    return async () => {
      const blob = await zip.file(files[key].name).async('blob')
      const formData = new FormData()
      formData.append('file', blob, files[key].name)
      const { data } = await axios.post('https://www.szwgft.cn:5000/api/UpWrite/upload?Zone_Code=xlsx_img', formData, {
        headers: { 'Content-Type': 'multipart/form-data' }
      })
      return data
    }
  })
  return imageFilePromises
}

// 读取表格数据
function readXlxsFile(file) {
  return new Promise((resolve) => {
    const reader = new FileReader()
    step.value = 1
    reader.readAsArrayBuffer(file)
    reader.onload = (e) => {
      const binaryStr = e.target.result
      const workbook = XLSX.read(binaryStr, { type: 'binary' })
      const sheetName = workbook.SheetNames[0] // 假设只读取第一个工作表
      const worksheet = workbook.Sheets[sheetName]
      const data = XLSX.utils.sheet_to_json(worksheet, { header: 1 }) // 转换为JSON数组
      resolve(data)
    }
  })
}
</script>

<style lang="less" scoped>
.code {
  background: #fafafa;
  display: flex;
  width: 200px;
  height: 200px;
  border-radius: 8px;
  border: 1px solid #ccc;
  overflow: hidden;
  position: relative;
  flex-direction: column;
  padding: 14px 14px 6px 14px;

  .inputA {
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    right: 0;
    opacity: 0;
    z-index: 9;
  }
  .iconbox {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    flex: 1;
    .icon {
      font-size: 40px;
      font-weight: 700;
      color: #1890ff;
    }
    .label {
      font-size: 14px;
      margin-top: 10px;
      color: #666;
    }
  }
  .cover {
    background: #eeeeee55;
    position: absolute;
    left: 0;
    top: 0;
    right: 0;
    bottom: 0;
    z-index: 10;
    padding: 6px;
    display: flex;
    flex-direction: column-reverse;
  }
}
</style>
