const DRAG_DEFAULT_POSITION = { x: 200, y: 200 } // 定义默认位置常量

export const dragBox = {
  created(el, binding) {
    // 从绑定值中解构出 x 和 y，使用默认值
    const [x = DRAG_DEFAULT_POSITION.x, y = DRAG_DEFAULT_POSITION.y] = binding.value || []
    el.style.position = 'fixed' // 设置元素为相对定位
    el.style.left = `${x}px` // 设置初始左侧位置
    el.style.top = `${y}px` // 设置初始顶部位置

    const handle = binding.value?.handle || el // 获取鼠标拖动的手柄

    handle.addEventListener('mousedown', onMouseDown, true) // 添加鼠标按下事件监听
    handle.addEventListener('touchstart', onMouseDown, true) // 支持触控设备

    function onMouseDown(e) {
      const startX = e.clientX || e.touches[0].clientX // 获取按下时的 X 坐标
      const startY = e.clientY || e.touches[0].clientY // 获取按下时的 Y 坐标
      const offsetX = startX - parseInt(el.style.left, 10) // 计算 X 轴偏移量
      const offsetY = startY - parseInt(el.style.top, 10) // 计算 Y 轴偏移量

      // 添加全局鼠标移动和释放事件监听
      document.addEventListener('mousemove', onMouseMove)
      document.addEventListener('mouseup', onMouseUp)
      document.addEventListener('touchmove', onMouseMove) // 支持触控设备
      document.addEventListener('touchend', onMouseUp) // 支持触控设备

      function onMouseMove(e) {
        e.preventDefault() // 阻止默认事件

        // 计算新的位置
        const x = (e.clientX || e.touches[0].clientX) - offsetX // 新的 X 坐标
        const y = (e.clientY || e.touches[0].clientY) - offsetY // 新的 Y 坐标
        requestAnimationFrame(() => {
          el.style.left = `${x}px` // 更新左侧位置
          el.style.top = `${y}px` // 更新顶部位置
        })
      }

      function onMouseUp() {
        // 移除事件监听
        document.removeEventListener('mousemove', onMouseMove)
        document.removeEventListener('mouseup', onMouseUp)
        document.removeEventListener('touchmove', onMouseMove)
        document.removeEventListener('touchend', onMouseUp)
      }
    }
  }
}

export default dragBox
