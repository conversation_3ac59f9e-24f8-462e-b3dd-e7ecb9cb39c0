import { createApp } from 'vue'
import { createPinia } from 'pinia'
import 'normalize.css'

import App from './App.vue'
import router from '@/router'
import '@/assets/css/index.less'
import directive from '@/directive'

const pinia = createPinia()
const app = createApp(App).use(router).use(pinia).use(directive)

fetch('https://ft-oss-image.oss-cn-shenzhen.aliyuncs.com/oss/bengfang.glb')
// fetch('https://www.szwgft.cn/nodeServer/file/bengfang.glb')

app.mount('#app')
