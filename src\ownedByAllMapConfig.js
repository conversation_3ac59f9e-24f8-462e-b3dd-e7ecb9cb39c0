import { flyTo } from './components/MapGlS2/utils'
import { transitionArr } from '@/utils'

const zoneColor = { 梅林片区: '#1677ff', 景田片区: 'red', 香蜜片区: '#fa9600', 福东南片区: '#0000FF', 福东北片区: '#ffd447', 莲花片区: '#bdfb9b', 中心城片区: '#ffb4a6', 福保片区: '#9f4fff', 福民片区: '#00ffff', 新洲片区: '#00ff00' }
const tenGridColor = { '福中-1': '#999999', '福中-2': '#947dd2', '福中-3': '#38056c', '福东-1': '#ffc300', '福东-2': '#1677ff', '梅林-2': '#8d0e8d', '梅林-1': '#cfc00e', '香蜜-3': '#37840b', '香蜜-2': '#900C3F', '香蜜-1': '#0B7784' }
const streetColor = {
  梅林街道办: '#999999',
  莲花街道办: '#947dd2',
  香蜜湖街道办: '#38056c',
  沙头街道办: '#ffc300',
  福保街道办: '#1677ff',
  福田街道办: '#8d0e8d',
  南园街道办: '#cfc00e',
  华强北街道办: '#37840b',
  华富街道办: '#900C3F',
  园岭街道办: '#0B7784'
}

export const FT_ZONE = {
  type: 'vector',
  scheme: 'tms',
  tiles: ['https://www.szwgft.cn/mapServer/geoserver/gwc/service/tms/1.0.0/Code%3AFT_ZONE@EPSG%3A3857@pbf/{z}/{x}/{y}.pbf'],
  layers: {
    zone_fill: {
      options: {
        'source-layer': 'FT_ZONE',
        paint: {
          'fill-opacity': 0.35,
          'fill-color': [
            'case',
            ['==', ['get', 'Type'], '市政路'],
            '#999',
            ['==', ['get', 'Type'], '其他'],
            '#888',
            [
              'match',
              ['get', 'ManagerNam'], // type 等于 小区，根据 ManagerNam 设置颜色
              ['福中水务所'],
              '#ee0000',
              ['福东水务所'],
              '#bdfb9b',
              ['梅林水务所'],
              '#1677ff',
              ['香蜜水务所'],
              '#fa9600',
              '#999' // 默认颜色，如果 ManagerNam 不匹配以上任何值
            ]
          ]
        }
      }
    },
    zone_line: {
      options: { minzoom: 15, 'source-layer': 'FT_ZONE', paint: { 'line-width': 1, 'line-color': '#000' } }
    },
    zone2_line: {
      options: { 'source-layer': 'FT_ZONE', paint: { 'line-width': 3, 'line-color': '#07c160' }, filter: ['==', ['get', 'Zone_Name'], ''] }
    },
    zone_symbol: {
      options: { 'source-layer': 'FT_ZONE', minzoom: 15, layout: { 'text-field': ['get', 'Zone_Name'], 'text-size': 12 }, paint: { 'text-color': 'black', 'text-halo-color': 'white', 'text-halo-width': 1 } }
    }
  }
}
// 片区图层
export const FT_ModificationWorks = {
  type: 'vector',
  scheme: 'tms',
  tiles: ['https://www.szwgft.cn/mapServer/geoserver/gwc/service/tms/1.0.0/Code%3AFT_ModificationWorks@EPSG%3A3857@pbf/{z}/{x}/{y}.pbf'],
  layers: {
    FTModificationWorks_fill: {
      options: {
        'source-layer': 'FT_ModificationWorks',
        layout: { visibility: 'none' },
        paint: {
          'fill-opacity': 0.35,
          'fill-outline-color': '#000000',
          'fill-color': ['match', ['get', 'AreaName'], ...transitionArr(zoneColor, 'pink')]
        }
      }
    },
    FTModificationWorks_line: {
      options: { 'source-layer': 'FT_ModificationWorks', paint: { 'line-width': 1, 'line-color': '#000' }, layout: { visibility: 'none' } }
    },
    FTModificationWorks_symbol: {
      options: {
        'source-layer': 'FT_ModificationWorks',
        layout: { 'text-field': ['get', 'AreaName'], 'text-anchor': 'center', 'text-size': 22, visibility: 'none' },
        paint: { 'text-color': '#c500ff', 'text-halo-color': 'white', 'text-halo-width': 1 }
      }
    }
  }
}
// 十网格图层
export const tenGrid = {
  type: 'vector',
  scheme: 'tms',
  tiles: ['https://www.szwgft.cn/mapServer/geoserver/gwc/service/tms/1.0.0/Code%3AtenGrid@EPSG%3A3857@pbf/{z}/{x}/{y}.pbf'],
  layers: {
    tenGrid_fill: {
      options: {
        'source-layer': 'tenGrid',
        layout: { visibility: 'none' },
        paint: {
          'fill-opacity': 0.5,
          'fill-outline-color': '#000000',
          'fill-color': ['match', ['get', 'name'], ...transitionArr(tenGridColor, 'pink')]
        }
      }
    },
    tenGrid_line: {
      options: { 'source-layer': 'tenGrid', layout: { visibility: 'none' }, paint: { 'line-width': 1, 'line-color': '#000' } }
    },
    tenGrid_symbol: {
      options: {
        'source-layer': 'tenGrid',
        layout: { 'text-field': ['get', 'name'], 'text-anchor': 'center', 'text-size': 22, visibility: 'none' },
        paint: { 'text-color': '#c500ff', 'text-halo-color': 'white', 'text-halo-width': 1 }
      }
    }
  }
}
// 街道图层
export const FT_street = {
  type: 'vector',
  scheme: 'tms',
  tiles: ['https://www.szwgft.cn/mapServer/geoserver/gwc/service/tms/1.0.0/Code%3AFT_street@EPSG%3A3857@pbf/{z}/{x}/{y}.pbf'],
  layers: {
    FTstreet_fill: {
      options: {
        'source-layer': 'FT_street',
        paint: {
          'fill-opacity': 0.5,
          'fill-outline-color': '#000000',
          'fill-color': ['match', ['get', 'name'], ...transitionArr(streetColor, 'pink')]
        },
        layout: { visibility: 'none' }
      }
    },
    FTstreet_line: {
      options: { 'source-layer': 'FT_street', paint: { 'line-width': 1, 'line-color': '#000' }, layout: { visibility: 'none' } }
    },
    FTstreet_symbol: {
      options: {
        'source-layer': 'FT_street',
        layout: { 'text-field': ['get', 'name'], 'text-anchor': 'center', 'text-size': 22, visibility: 'none' },
        paint: { 'text-color': '#c500ff', 'text-halo-color': 'white', 'text-halo-width': 1 }
      }
    }
  }
}

export const ZONE_FILL_LAYER = 'zone_fill'
export const ZONE_LINE_LAYER = 'zone_line'
export const ZONE_SYMBOL_LAYER = 'zone_symbol'
export const ZONE2_LINE_LAYER = 'zone2_line'

export function highlightZone(Map) {
  Map.on('click', ZONE_FILL_LAYER, (e) => {
    const [feature] = Map.queryRenderedFeatures(e.point, { layers: [ZONE_FILL_LAYER] })
    const { properties } = feature
    const center = Object.values(e.lngLat)
    flyTo(Map, center, 16)
    Map.setFilter(ZONE2_LINE_LAYER, ['match', ['get', 'Zone_Name'], properties.Zone_Name, true, false])
  })
}

export default { sources: { FT_ModificationWorks, tenGrid, FT_street, FT_ZONE } }
