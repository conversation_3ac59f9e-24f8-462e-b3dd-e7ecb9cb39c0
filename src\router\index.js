import { createRouter, createWebHistory } from 'vue-router'
import Cache from '@/utils/cache'

const routes = [
  {
    path: '/',
    redirect: '/main/home'
  },
  {
    path: '/login',
    name: '登录',
    component: () => import('@/views/login/index.vue')
  },
  {
    path: '/main',
    component: () => import('@/views/main/index.vue'),
    redirect: '/main/home',
    children: [
      {
        path: '/main/home',
        name: '首页',
        component: () => import('@/views/main/children/home/<USER>')
      },

      {
        path: '/main/schedule',
        component: () => import('@/views/main/children/schedule/index.vue'),
        name: '工程进度',
        redirect: '/main/schedule/constructionSite',
        children: [
          {
            path: '/main/schedule/constructionSite',
            name: '新建工地',
            component: () => import('@/views/main/children/schedule/children/constructionSite/index.vue')
          },
          {
            path: '/main/schedule/examine',
            name: '总表核对',
            component: () => import('@/views/main/children/schedule/children/examine/index.vue')
          },
          {
            path: '/main/schedule/fineWater',
            name: '优饮',
            component: () => import('@/views/main/children/schedule/children/fineWater/index.vue')
          },
          {
            path: '/main/schedule/twiceProvide',
            name: '二供',
            component: () => import('@/views/main/children/schedule/children/twiceProvide/index.vue')
          },
          {
            path: '/main/schedule/menu-map',
            name: '小区档案地图',
            component: () => import('@/views/main/children/schedule/children/menu-map/index.vue')
          },
          {
            path: '/main/schedule/menu',
            name: '小区列表',
            component: () => import('../views/main/children/schedule/children/menu-map/children/menu/index.vue')
          },
          {
            path: '/main/schedule/small-works',
            name: '小额工程',
            component: () => import('../views/main/children/schedule/children/small-works/index.vue')
          }
        ]
      },
      {
        path: '/main/assess',
        component: () => import('@/views/main/children/assess/index.vue'),
        name: '考核评分',
        redirect: '/main/assess/dailyOperation',
        children: [
          {
            name: '日常运维',
            component: () => import('@/views/main/children/assess/children/dailyOperation/index.vue'),
            path: '/main/assess/dailyOperation'
          },
          {
            name: '集团考核',
            component: () => import('@/views/main/children/assess/children/groupAssessment/index.vue'),
            path: '/main/assess/groupAssessment'
          },
          {
            name: '外业验收',
            component: () => import('@/views/main/children/assess/children/workAcceptance/index.vue'),
            path: '/main/assess/workAcceptance'
          },
          {
            name: '管网自评',
            component: () => import('@/views/main/children/assess/children/selfReview/index.vue'),
            path: '/main/assess/selfReview'
          },
          {
            name: '数字验收',
            component: () => import('@/views/main/children/assess/children/digitalAcceptance/index.vue'),
            path: '/main/assess/digitalAcceptance'
          }
        ]
      },
      {
        path: '/main/bi',
        name: 'bi',
        component: () => import('@/views/main/children/bi/index.vue'),
        redirect: '/main/bi/sewage',
        children: [
          {
            name: '污水| 积水点',
            path: '/main/bi/sewage',
            component: () => import('@/views/main/children/bi/children/sewage/index.vue')
          }
        ]
      },
      {
        path: '/main/synthesize',
        component: () => import('@/views/main/children/synthesize/index.vue'),
        redirect: '/main/synthesize/RainSludgeLevelPoint',
        children: [
          {
            name: '管道充盈度',
            path: '/main/synthesize/RainSludgeLevelPoint',
            component: () => import('@/views/main/children/synthesize/children/RainSludgeLevelPoint/index.vue')
          }
        ]
      }
    ]
  },

  {
    path: '/MoreMaterials',
    name: '二供表',
    component: () => import('@/views/main/children/schedule/children/twiceProvide/children/MoreMaterials/index.vue')
  },
  {
    path: '/code',
    name: '测试页面',
    component: () => import('@/views/code/index.vue')
  },
  {
    path: '/popup-demo',
    name: '弹窗演示',
    component: () => import('@/views/code/popup-demo.vue')
  }
  // {
  //   path: '/pandect',
  //   name: '领导驾驶舱',
  //   component: () => import('@/views/pandect/index.vue')
  // }
]

const router = createRouter({ history: createWebHistory(), routes })
const name = Cache.get('userInfo')?.name

if (name === '开发账号')
  router.addRoute({
    path: '/appVersion',
    name: 'app版本控制',
    component: () => import('@/views/version/index.vue')
  })

router.beforeEach((to, from, next) => {
  const userInfo = Cache.get('userInfo')
  if (to.path === '/login') return next()
  if (!userInfo) return next('/login')
  if (to.path === '/main/schedule/small-works') return next()
  if (userInfo.Jurisdiction == '小额工程') return next('/main/schedule/small-works')
  return next()
})

export default router
