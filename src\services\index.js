import Request from './request'
import { message } from 'ant-design-vue'
import Cache from '@/utils/cache'

const request = new Request({
  // baseURL: 'https://www.szwgft.cn:5002', //测试地址
  // baseURL: 'http://47.106.81.163:5002', //测试地址
  baseURL: '/api', //生产地址
  timeout: 30000,
  interceptors: {
    requestInterceptor: (config) => {
      const token = Cache.get('userInfo')?.token

      if (config.url !== '/api/User/login') {
        if (!token) window.location.href = '/login'
      }

      config.headers.Authorization = 'Bearer ' + token ?? ''
      return config
    },
    requestInterceptorCatch: (err) => err,
    responseInterceptor: (res) => res,
    responseInterceptorCatch: (err) => {
      if (err.config.url == '/api/User/login') {
        return err
      }

      if (err.request.status === 404) {
        if (err.request.responseText) message.error(err.request.responseText)
      }
      if (err.code === 'ERR_NETWORK') {
        message.error('登录过期，请重新登录')
        window.location.href = '/login'
      }
      if (err.request.status === 401) {
        message.error('登录过期，请重新登录')
        window.location.href = '/login'
      }
      return err
    }
  }
})
const request2 = new Request({
  baseURL: '/api2',
  timeout: 30000,
  interceptors: {
    requestInterceptor: (config) => {
      const token = Cache.get('userInfo')?.token
      if (!token) window.location.href = '/login'
      config.headers.Authorization = 'Bearer ' + token ?? ''
      return config
    },
    requestInterceptorCatch: (err) => {
      return err
    },
    responseInterceptor: (res) => {
      return res
    },
    responseInterceptorCatch: (err) => {
      const { status } = err.response
      if (status === 401) {
        message.error('登录过期，请重新登录')
        window.location.href = '/login'
      }

      return err
    }
  }
})
const request3 = new Request({
  baseURL: '/nodeServer',
  // baseURL: 'http://47.106.81.163:3003', //服务器测试node服务
  // baseURL: 'http://10.1.60.61:3001', //本地node服务
  timeout: 30000,
  interceptors: {
    requestInterceptor: (config) => {
      const token = Cache.get('userInfo')?.token
      if (!token) window.location.href = '/login'
      config.headers.Authorization = 'Bearer ' + token ?? ''
      return config
    },
    requestInterceptorCatch: (err) => {
      return err
    },
    responseInterceptor: (res) => {
      if (res.data.code === 401) {
        message.error('登录过期，请重新登录')
        setTimeout(() => {
          window.location.href = '/login'
        }, 1000)
      }

      return res
    },
    responseInterceptorCatch: (err) => {
      const { status } = err.response
      if (status === 401) {
        message.error('登录过期，请重新登录')
        window.location.href = '/login'
      }
      return err
    }
  }
})

export { request, request2, request3 }
export default request
