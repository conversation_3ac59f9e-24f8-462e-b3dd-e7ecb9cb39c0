import { request3 } from '../index'

export const getActiveRecordList = () => request3.get({ url: '/activeRecord/list' })
export const getActiveRecordLine = (time) => request3.get({ url: `/activeRecord/line${time ? `?time=${time}` : ''}` })
export const getActiveUserList = (time) => request3.get({ url: `/activeRecord/user${time ? `?time=${time}` : ''}` })
export const getActiveUserLine = (userName) => request3.get({ url: `/activeRecord/user/line?userName=${userName}` })
