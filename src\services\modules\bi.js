import { request2 } from '../index'

export const addBiDot = (data) => request2.post({ url: '/1809160547388755968/insertobject/waterloggingpoint/1809166742371569664', data })

export const getBiDot = (time) => request2.get({ url: `/1809160547388755968/querycommon/waterloggingpoint/1809168022410235904?Time=${time}` })

// 删除
export const deleteDot = (id) => request2.post({ url: `/1809160547388755968/deleteobject/waterloggingpoint/1813040811462496256`, data: { ID: id } })
