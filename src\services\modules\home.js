import { request2, request } from '../index.js'

export const getHomeMultidata = () => request.get({ url: '/record/list' })

export const alterRecordApi = (data) => request.put({ url: `/api/OperationFeedback?id=${data.id}`, data })

// 大文件上传
export const uploadmaxFile = (FolderPath) => request.post({ url: `/api/UpWrite/uploadmax?FolderPath=${FolderPath}` })
export const setDocument = (data) => request.post({ url: `/api/Document`, data })

// 外业列表查询
export const getalveVerificationListApi = (Zone_Code) => {
  const url = `/api/ValveVerification/Zone_Code${Zone_Code ? '?Zone_Code=' + Zone_Code : ''}`
  return request.get({ url })
}

//小区档案
export const getDwd_gwyy_xqxttz = (data) => request2.post({ url: '/1809160547388755968/querycommon/dwd_gwyy_xqxttz/1821096626152935424', data })

// 获取点线表数据
export function getPointLineApi(Sta, End, token) {
  return request.get({
    url: `/api/ValveVerification/GetPointLine?End_Time=${End}&Sta_Time=${Sta}&token=${token}`
  })
}

// gis修正提交

export const createRecordApi = (data) => request.post({ url: `/api/OperationFeedback`, data })
