import request from '../index.js'
import { request3 } from '../index'

// 模糊搜索
export const getZoneDataLike = (ZoneName) => {
  return request.get({ url: `/api/Data/ZoneData_like?Zone_Name=${ZoneName}` })
}
// 查询区块信息
export const getZoneData = (ZoneCode) => {
  return request.get({ url: `/api/Data/ZoneData?Zone_Code=${ZoneCode}` })
}

// 查询问题点列表
export const getRecordList = (station) => {
  return request.get({ url: `/api/OperationFeedback/station?station=${station}` })
}
export const getScreenRecordList = (data) => {
  return request.post({ url: `/api/OperationFeedback/parameter`, data })
}

export const getRecordDetails = (id) => {
  return request.get({ url: `/api/OperationFeedback/id?id=${id}` })
}

// 查询二供列表
export const getSecondaryWaterProgress = (data = {}) => {
  return request.post({ url: `/api/SecondaryWaterProgress/parameter`, data })
}

export const getValveVerificationTimeListApi = (sta, end) => {
  return request.get({ url: `/api/ValveVerification/Time?Sta_Time=${sta}&End_Time&=${end}` })
}

// 模糊搜索泵房
export const getPumpHouseNameLikeApi = (PumpHouseName) => {
  return request.get({ url: `/api/SecondaryWaterProgress/PumpHouseName_like?PumpHouseName=${PumpHouseName}` })
}

export const getSecondaryWaterProgressUpdate = (data) => {
  return request.put({ url: `/api/SecondaryWaterProgress?id=${data.id}`, data })
}

// 获取泵房文件列表
export const getPumpHouseFileList = (id) => {
  return request3.get({ url: `/upload/pump_house/${id}` })
}

export const getSQL = (sql) => {
  return request.get({ url: `/api/Data/Get_SQL?SQL_string=${sql}` })
}

export const exportPreserve = (data) => {
  return request3.post({ url: '/export/preserve', data })
}
