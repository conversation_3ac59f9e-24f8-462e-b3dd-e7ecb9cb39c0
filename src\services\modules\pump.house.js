import { request3 } from '../index'

class PumpHouse {
  list(params) {
    return request3.get({ url: '/pumpHouse/list', params })
  }
  update(data) {
    return request3.put({ url: '/pumpHouse', data })
  }
  detail(pumpRoomNumber) {
    return request3.get({ url: `/pumpHouse/detail/${pumpRoomNumber}` })
  }
  createNode(data) {
    return request3.post({ url: '/pumpHouse/node', data })
  }
  updateNode(data) {
    return request3.put({ url: '/pumpHouse/node', data })
  }

  nodeList(pumpRoomNumber) {
    return request3.get({ url: `/pumpHouse/node/list/${pumpRoomNumber}` })
  }
  nodeDetail(pumpRoomNumber, params) {
    return request3.get({ url: `/pumpHouse/node/${pumpRoomNumber}`, params })
  }
  seek(params) {
    return request3.get({ url: '/pumpHouse/seek', params })
  }
  createNodeFile(data) {
    return request3.post({ url: '/pumpHouse/nodeFile', data })
  }
}

export const PumpHouseApi = new PumpHouse()
