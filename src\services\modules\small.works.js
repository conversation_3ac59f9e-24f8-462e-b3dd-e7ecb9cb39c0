import { request3, request } from '../index'
export const analysisPdf = (url) => request3.get({ url: `/analysisPdf?url=${url}` }) //解析pdf文件
export const establishProject = (data) => request.post({ url: '/api/Small_Prj', data }) //创建项目
export const updataProject = (data) => request.put({ url: `/api/Small_Prj?Id=${data.id}`, data }) //修改项目
export const deleteProject = (id) => request.delete({ url: `/api/Small_Prj?Id=${id}` }) //删除项目
export const projectList = () => request.get({ url: '/api/Small_Prj' }) //项目列表
export const projectNameLike = (name) => request.get({ url: `/api/Small_Prj/Prj_Name_like?Prj_Name=${name}` }) //模糊搜索项目列表
export const projectCode = (code) => request.get({ url: `/api/Small_Prj/Prj_No?Prj_No=${code}` }) //根据项目编号查询项目
export const establishSmallProject = (data) => request.post({ url: '/api/SubItem_LX', data }) //创建子项目
export const gainSmallProject = (code) => request.get({ url: `/api/SubItem_LX/Prj_No?Prj_No=${code}` }) //获取子项目
export const updateFile = (data) => request3.post({ url: '/update?fileType=pdf', data, headers: { 'Content-Type': 'multipart/form-data' } }) //上传文件
export const getSteptime = (Department) => request.get({ url: `/api/Small_Prj/steptime${Department && Department != '全部' ? `?Department=${Department}` : ''}` }) //获取平均时间

export const filtrationProject = (data) => request.post({ url: '/api/Small_Prj/parameter', data }) //筛选项目

export const getSmallPrj = (params) => request.get({ url: '/api/Small_Prj/step', params })
export const projectParticulars = () => request.get({ url: '/api/Small_Prj/Subltem_Prj_No' }) // 获取需要导出的项目详情列表
export const abnormalProject = () => request.post({ url: '/api/Small_Prj/abnormal' }) // 获取异常项目列表

export const filtrateProject = (data) => request.post({ url: '/api/Small_Prj/abnormal_parameter', data }) //筛选项目列表
