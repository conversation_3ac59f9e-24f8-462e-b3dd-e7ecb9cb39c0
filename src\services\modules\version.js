import { request3 } from '../index'

// export const createVersion = (data) => request3.post({ url: '/version/create', data }) //新增
// export const getVersionList = () => request3.get({ url: '/version/list' }) //列表
// export const deleteVersion = (version) => request3.delete({ url: `/version/delete?version=${version}` }) //删除
// export const updataVersion = (data) => request3.put({ url: `/version/update`, data }) //更新

export const createVersion = (data) => request3.post({ url: '/versionControl/create', data }) //新增
export const getVersionList = () => request3.get({ url: '/versionControl/list' }) //列表
export const deleteVersion = (id) => request3.delete({ url: `versionControl/delete?id=${id}` }) //删除
export const updataVersion = (data) => request3.put({ url: `/versionControl/update`, data }) //更新
