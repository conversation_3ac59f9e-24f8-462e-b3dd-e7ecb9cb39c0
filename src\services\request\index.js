import axios from 'axios'

const DEFAULT_LOADING = false

class Request {
  constructor(config) {
    this.instance = axios.create(config)
    this.showLoading = DEFAULT_LOADING
    this.interceptors = config.interceptors

    // 外部添加请求拦截器
    this.instance.interceptors.request.use(
      this.interceptors?.requestInterceptor,
      this.interceptors?.requestInterceptorCatch
    )

    //外部添加响应拦截器
    this.instance.interceptors.response.use(
      this.interceptors?.responseInterceptor,
      this.interceptors?.responseInterceptorCatch
    )

    //全局请求拦截器
    this.instance.interceptors.request.use(
      (config) => config,
      (err) => err
    )

    // 全局响应拦截器
    this.instance.interceptors.response.use(
      (res) => res.data,
      (err) => err
    )
  }

  // 独立的请求拦截
  request(config) {
    return new Promise((resolve, reject) => {
      this.instance
        .request(config)
        .then((res) => {
          resolve(res)
        })
        .catch((err) => {
          reject(err)
        })
    })
  }

  get(config) {
    return this.request({ ...config, method: 'GET' })
  }

  post(config) {
    return this.request({ ...config, method: 'POST' })
  }
  put(config) {
    return this.request({ ...config, method: 'PUT' })
  }

  delete(config) {
    return this.request({ ...config, method: 'DELETE' })
  }
}

export default Request
