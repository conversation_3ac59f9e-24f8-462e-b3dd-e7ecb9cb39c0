import { ref } from 'vue'
import { defineStore } from 'pinia'
import { getBiDot, addBiDot } from '@/services/modules/bi'
import { message } from 'ant-design-vue'

export const useBiStore = defineStore('bi', () => {
  const dots = ref([]) //标记数据

  function getPreviousMonth(date) {
    let d = new Date(date)
    let year = d.getFullYear()
    let month = (d.getMonth() + 1).toString().padStart(2, '0')
    return `${year}-${month}`
  }

  const time = ref(getPreviousMonth(new Date()))
  async function getBiDotList() {
    const res = await getBiDot(time.value)
    dots.value = res.map((item) => {
      return {
        type: 'Feature',
        properties: item,
        geometry: { type: 'Point', coordinates: [item.X, item.Y] }
      }
    })
  }

  async function addBiDotData(data) {
    try {
      const res = await addBiDot(data)
      if (res) message.success('添加成功')
      await getBiDotList()
    } catch (error) {
      message.error('添加失败')
    }
  }

  return { dots, time, getBiDotList, addBiDotData }
})

export default useBiStore
