import { ref } from 'vue'
import { defineStore } from 'pinia'
import { getRecordList, getScreenRecordList } from '@/services/modules/map'
import Cache from '@/utils/cache'
import { message } from 'ant-design-vue'

export const useDailyOperation = defineStore('dailyOperation', () => {
  // gis错误数据列表
  const gisFaults = ref([])
  // 获取gis错误数据列表
  async function getGisFaults() {
    // const { station } = Cache.get('userInfo')
    const result = await getRecordList('运营中心')
    const data = result?.data?.operationFeedbacks ?? []
    gisFaults.value = data.map((item, index) => settleData(item, index))
  }

  async function getScreenRecord(data) {
    try {
      const result = await getScreenRecordList(data)
      if (!result.length) return
      const list = result ?? []
      gisFaults.value = list.map((item, index) => settleData(item, index))
    } catch (error) {
      message.error('获取数据失败')
    }
  }

  // 整理数据
  function settleData(value, index) {
    value.state = value.path4.length > 0 ? '已处理' : '未处理'
    return { type: 'Feature', id: index, properties: value, geometry: { type: 'Point', coordinates: [value.x, value.y] } }
  }

  return { gisFaults, getGisFaults, getScreenRecord }
})

export default useDailyOperation
