import { defineStore } from 'pinia'
import { ref } from 'vue'
import { getDwd_gwyy_xqxttz } from '@/services/modules/home'
import { message } from 'ant-design-vue'

export const useHomeStore = defineStore('home', () => {
  // 侧边抽屉
  const open = ref(false)
  const detail = ref(null)
  async function getMenu(code) {
    const [data] = await getDwd_gwyy_xqxttz({ xqmc: null, xqbm: code })
    if (!data) return message.warning('该小区未存在档案')
    open.value = true
    detail.value = data
  }

  return { open, detail, getMenu }
})

export default useHomeStore
