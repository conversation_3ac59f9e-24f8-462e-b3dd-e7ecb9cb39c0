import * as THREE from 'three'
import proj4 from 'proj4'

// 绘制水渠几何体
export class CanalGeometry {
  create(path, parameter) {
    const positions = []
    const { width, height, thickness } = parameter
    const newPath = this.coordinateTransformation(path)
    const p1 = this.calculateRectangleCorners(newPath, width / 2) //底面矩形顶点坐标
    positions.push(this.computedCover(p1)) //底面矩形面
    const p1_s = p1.slice(0, 2)
    const p1_e = p1.slice(2, 4)
    const p2 = this.translation(p1_s, 'z', height)
    positions.push(this.computedCover([...p2, ...p1_s])) //侧面矩形面
    const p3 = this.translation(p1_e, 'z', height)
    positions.push(this.computedCover([...p3, ...p1_e])) //侧面矩形面
    const p4 = this.translation(this.calculateRectangleCorners(newPath, width / 2 - thickness), 'z', thickness) //渠底面
    positions.push(this.computedCover(p4)) //底面矩形面
    const p4_s = p4.slice(0, 2)
    const p4_e = p4.slice(2, 4)
    const p5 = this.translation(p4_s, 'z', height - thickness)
    positions.push(this.computedCover([...p5, ...p4_s])) //内侧面矩形面
    const p6 = this.translation(p4_e, 'z', height - thickness)
    positions.push(this.computedCover([...p6, ...p4_e])) //内侧面矩形面
    positions.push(this.computedCover([...p2, ...p5])) // 顶面
    positions.push(this.computedCover([...p3, ...p6])) // 顶面
    positions.push(this.computedCover([p2[0], p1_s[0], p5[0], p4_s[0]])) //侧面矩形面
    positions.push(this.computedCover([p2[1], p1_s[1], p5[1], p4_s[1]])) //侧面矩形面
    positions.push(this.computedCover([p3[0], p1_e[0], p6[0], p4_e[0]])) //侧面矩形面
    positions.push(this.computedCover([p3[1], p1_e[1], p6[1], p4_e[1]])) //侧面矩形面
    positions.push(this.computedCover([p1[0], p1[2], p4[0], p4[2]])) //侧面矩形面
    positions.push(this.computedCover([p1[1], p1[3], p4[1], p4[3]])) //侧面矩形面
    const canalGeometry = new THREE.BufferGeometry()
    const canalVertices = new Float32Array(positions.flat(2))
    canalGeometry.setAttribute('position', new THREE.BufferAttribute(canalVertices, 3))
    canalGeometry.computeVertexNormals()
    canalGeometry.center()
    canalGeometry.translate(0, 0, height / 2)
    return canalGeometry
  }

  // 线平移
  translation(paths, site, num) {
    return paths.map((i) => {
      i[2] = i[2] ?? 0
      if (site === 'x') return [i[0] + num, i[1], i[2]]
      if (site === 'y') return [i[0], i[1] + num, i[2]]
      if (site === 'z') return [i[0], i[1], i[2] + num]
    })
  }
  // 计算面
  computedCover(paints) {
    const [lb, rb, lt, rt] = paints
    return [lb, rb, rt, lb, rt, lt]
  }
  // 线扩展
  calculateRectangleCorners(points, distance) {
    // 提取点坐标
    const [[x1, y1, z1], [x2, y2, z2]] = points

    // 计算线段的长度
    const length = Math.sqrt((x2 - x1) ** 2 + (y2 - y1) ** 2)

    // 计算线段的单位向量
    const unitX = (x2 - x1) / length
    const unitY = (y2 - y1) / length

    // 计算垂直于线段的单位向量
    const perpendicularX = -unitY // 90度旋转
    const perpendicularY = unitX

    // 计算长方形的四个角坐标
    const corner1 = [x1 + perpendicularX * distance, y1 + perpendicularY * distance, z1]
    const corner2 = [x2 + perpendicularX * distance, y2 + perpendicularY * distance, z2]
    const corner4 = [x1 - perpendicularX * distance, y1 - perpendicularY * distance, z1]
    const corner3 = [x2 - perpendicularX * distance, y2 - perpendicularY * distance, z2]

    return [corner1, corner2, corner4, corner3]
  }
  // 经纬度转xyz坐标
  coordinateTransformation(path) {
    const [start, end] = path.map((i) => {
      const [x, y, z = 0] = i
      return proj4('EPSG:4326', 'EPSG:3857', [x * 0.9283, y * 0.9283, z])
    })
    return [
      [0, 0, start[2]],
      [end[0] - start[0], end[1] - start[1], end[2]]
    ]
  }
}
