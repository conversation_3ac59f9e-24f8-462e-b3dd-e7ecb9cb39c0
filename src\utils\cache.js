class LocalCache {
  // 保存数据
  set(key, value) {
    window.localStorage.setItem(key, JSON.stringify(value))
  }

  // 获取数据
  get(key) {
    const value = window.localStorage.getItem(key)
    if (value) return JSON.parse(value)
  }

  //删除key指定的缓存
  delete(key) {
    window.localStorage.removeItem(key)
  }

  // 清空所有缓存
  clear() {
    window.localStorage.clear()
  }
}

export default new LocalCache()
