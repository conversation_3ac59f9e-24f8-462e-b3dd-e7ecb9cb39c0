import ExcelJS from 'exceljs'
import { message } from 'ant-design-vue'
/**
 * 导出xllsx文件
 * @param {*} data
 */
export async function educeXlsx(data, keys, fileName) {
  const header = Object.keys(keys).map((key) => ({ header: keys[key], key: key, width: 30 }))

  const workbook = new ExcelJS.Workbook()
  const sheet = workbook.addWorksheet('Sheet1')
  sheet.columns = header
  const hide = message.loading('正在导出文件...', 0)
  // 添加数据
  data.forEach((item) => sheet.addRow(item))

  // 文件下载
  const buffer = await workbook.xlsx.writeBuffer()
  const blob = new Blob([buffer], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' })
  const url = window.URL.createObjectURL(blob)
  const a = document.createElement('a')
  a.href = url
  a.download = `${fileName ?? new Date().getTime()}.xlsx`
  a.click()
  window.URL.revokeObjectURL(url)
  hide()
}
