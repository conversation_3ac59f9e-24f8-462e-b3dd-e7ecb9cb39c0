import JSEncrypt from 'jsencrypt'

const publicKey = import.meta.env.VITE_WEB_PUBLICKEY
const privateKey = import.meta.env.VITE_WEB_PRIVATEKEY

// 加密
export function encrypt(data) {
  const encryptor = new JSEncrypt()
  encryptor.setPublicKey(publicKey)
  return encryptor.encrypt(data)
}
// 解密
export function decrypt(data) {
  const decryptor = new JSEncrypt()
  decryptor.setPrivateKey(privateKey)
  return decryptor.decrypt(data)
}
