const resourceBaseUrl = import.meta.env.VITE_WEB_RESOURCE_BASE_URL

export function createHeader() {
  const keys = {
    zone_Name: '小名称区',
    point_Type: '点子类',
    unique_Id: '唯一编号',
    burial_Depth: '埋深/m',
    to_Distance: '管顶离地高度/m',
    valve_Material: '阀体材料',
    pipe_Material: '管材',
    pipe_Diameter: '管径/mm',
    inlet_Waterways: '进水路数',
    type: '生消类型',
    address: '地址',
    coordinates: '坐标',
    notes: '备注',
    site_Photo_no: '现场照片',
    uploader: '上传人',
    created_Time: '创建时间',
    updated_Time: '更新时间',
    updater: '更新人',
    zone_Code: '小区code',
    x: 'x',
    y: 'y'
  }
  const header = Object.keys(keys).map((key) => ({ header: keys[key], key: key, width: 30 }))
  return header
}

// 转base64
export function getBase64Image(url) {
  return new Promise((resolve) => {
    var img = new Image()
    img.crossOrigin = 'Anonymous'
    // 设置远程图片的URL
    img.src = url
    // 等待远程图片加载完成
    img.onload = function () {
      var canvas = document.createElement('canvas')
      var ctx = canvas.getContext('2d')
      canvas.width = img.width
      canvas.height = img.height
      // 在Canvas上绘制图片
      ctx.drawImage(img, 0, 0)
      // 将Canvas上的内容转换为Base64编码
      var base64 = canvas.toDataURL('image/jpeg')
      // 输出Base64编码
      resolve(base64)
    }
  })
}

// 创建地图标记
export function createImage(url, type = true, bg = 'https://www.szwgft.cn:8090/FT01359/606c4308-de24-4186-a9fd-096233da1dba.png') {
  const merkerwarp = document.createElement('div')
  merkerwarp.classList.add('merkerwarp')
  merkerwarp.setAttribute('style', `background:url(${bg});background-size: 35px;`)
  const imgWarp = document.createElement('div')
  imgWarp.classList.add('img_warp')

  if (type) {
    const image = document.createElement('img')
    image.setAttribute('src', url)
    image.setAttribute('style', 'width: 70%; height: 70%;')
    imgWarp.appendChild(image)
  } else {
    const back = document.createElement('div')
    back.setAttribute('style', `width: 50%; height: 50%;background-color:${url};border-radius: 50%;`)
    imgWarp.appendChild(back)
  }
  merkerwarp.appendChild(imgWarp)
  return merkerwarp
}

// 图片地址拼接
export function imageJoin(url) {
  return resourceBaseUrl + '/' + url
}

// 创建点线表表头
export function createPointLineHeader() {
  const keys = {
    s_Point: '本点号',
    e_Point: '上点号',
    subtypE_Line: '线子类',
    subtype: '点子类',
    x: 'X坐标',
    y: 'Y坐标',
    length: '实际管长',
    diameter: '管径',
    material: '管材',
    elevation: '地面标高',
    buR_DEPTH: '埋深',
    toP_H: '管顶标高',
    pivotalfacility: '是否为特殊管段',
    towntype: '管网类型',
    jointtype: '连接方式',
    inprotection: '内部防腐',
    outprotection: '外部防腐',
    pressuretype: '加压类型',
    road: '所属路段',
    boundary: '所属小区',
    address: '详细地址',
    location: '所在位置',
    memgroup: '所属集团',
    subunit: '所属分公司',
    station: '所属水务所',
    region: '所属区域',
    projectname: '工程名称',
    designer: '设计单位',
    managerunit: '管理单位',
    datasource: '数据来源',
    asbuilT_DATE: '竣工日期',
    buildtime: '建设年份',
    valvetype: '阀门类型',
    valvefunc: '阀门功能',
    diameteR_FM: '阀门口径',
    jointtypE_FM: '连接类型',
    laymodal: '埋设方式',
    state: '阀门状态',
    materiaL_FM: '阀体材料',
    fireplugtype: '消火栓类型',
    category: '消火栓安装类型',
    ishavevalve: '是否有栓前阀门',
    constructor: '消火栓生产厂家',
    chargeperson: '责任人',
    caliber: '消火栓口径',
    caliberdiameterlinkpipe: '连接管管径',
    diametermainpipe: '开口主管管径',
    surveyunit: '探测单位',
    inputdate: '探测时间',
    documentID: '档案号',
    buildunit: '施工单位',
    created_Time: '2024-06-03T15:35:34.96',
    wgid: '所属网格',
    remark: '备注'
  }

  const header = Object.keys(keys).map((key) => ({ header: keys[key], key: key, width: 30 }))
  return header
}

export function getCurrentDateTime() {
  const now = new Date()

  const year = now.getFullYear()
  const month = String(now.getMonth() + 1).padStart(2, '0')
  const day = String(now.getDate()).padStart(2, '0')

  const hours = String(now.getHours()).padStart(2, '0')
  const minutes = String(now.getMinutes()).padStart(2, '0')
  const seconds = String(now.getSeconds()).padStart(2, '0')
  const milliseconds = String(now.getMilliseconds()).padStart(3, '0')

  const timezoneOffset = -now.getTimezoneOffset()
  const offsetHours = Math.floor(Math.abs(timezoneOffset) / 60)
    .toString()
    .padStart(2, '0')
  const offsetMinutes = (Math.abs(timezoneOffset) % 60).toString().padStart(2, '0')
  const timezone = (timezoneOffset >= 0 ? '+' : '-') + offsetHours + ':' + offsetMinutes

  return `${year}-${month}-${day}T${hours}:${minutes}:${seconds}.${milliseconds}${timezone}`
}

export function transitionArr(category, rest) {
  const entries = Object.entries(category)
  const entriesArr = entries.flat(1)
  if (rest) entriesArr.push(rest)
  return entriesArr
}

export function createDot(map, color = '#ff6c37', size = 100) {
  const data = {
    width: size,
    height: size,
    data: new Uint8Array(size * size * 4),

    onAdd() {
      const canvas = document.createElement('canvas')
      canvas.width = this.width
      canvas.height = this.height
      this.context = canvas.getContext('2d')
    },

    render() {
      const duration = 1000
      const t = (performance.now() % duration) / duration

      const radius = (size / 2) * 0.3
      const outerRadius = (size / 2) * 0.7 * t + radius
      const context = this.context

      context.clearRect(0, 0, this.width, this.height)
      context.beginPath()
      context.arc(this.width / 2, this.height / 2, outerRadius, 0, Math.PI * 2)
      context.fillStyle = `rgba(255, 255, 200, ${1 - t})`
      context.fill()

      context.beginPath()
      context.arc(this.width / 2, this.height / 2, radius, 0, Math.PI * 2)
      context.fillStyle = color
      context.strokeStyle = 'white'
      context.lineWidth = 2 + 4 * (1 - t)
      context.fill()
      context.stroke()
      this.data = context.getImageData(0, 0, this.width, this.height).data

      map.triggerRepaint()
      return true
    }
  }
  return data
}

// 随机生成一个数字
export function getRandomNumber(min, max) {
  return Math.floor(Math.random() * (max - min + 1)) + min
}
