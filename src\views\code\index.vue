<template>
  <div class="all">
    <MapGlS ref="MapRef" :options @load="load" />
  </div>
</template>

<script setup>
import { ref } from 'vue'
import chunk from 'lodash/chunk'
import MapGlS from '@/components/MapGlS2/index.vue'
import { FT_ZONE } from '@/ownedByAllMapConfig'
import { educeXlsx } from '@/utils/educeXlsx'
import dots from './dots_e.json'
// import dots from './dots.json'
let row = 160
let column = 230
// const dataT = { type: 'FeatureCollection', crs: { type: 'name', properties: { name: 'urn:ogc:def:crs:OGC:1.3:CRS84' } }, features: [] }
// function fn() {
//   for (let i = 0; i < 230; i++) {
//     for (let j = 0; j < 160; j++) {
//       const x = 113.985893074000066 + 0.00051 * i
//       const y = 22.583259136000038 - 0.00051 * j
//       const item = { type: 'Feature', geometry: { type: 'Point', coordinates: [x, y] } }
//       dataT.features.push(item)
//     }
//   }
// }
// fn()

// function fn() {
//   const arr = dots_t.features.map((i, index) => ({ ID: index, x: i.geometry.coordinates[0], y: i.geometry.coordinates[1] }))
//   educeXlsx(arr, { ID: 'ID', x: 'x', y: 'y' }, 'dots')
// }
// fn()

const options = {
  map: {
    pitch: 60,
    bearing: 60,
    style: 'http://webres.cityfun.com.cn/szmap/szmap_dark/3857/map_dark.json'
    // style: {
    //   version: 8,
    //   sources: {
    //     'osm-tiles1': { type: 'raster', tiles: ['https://t4.tianditu.gov.cn/DataServer?T=img_w&x={x}&y={y}&l={z}&tk=c4422fec9d5e394411da10d3f1838c84'], tileSize: 256, maxzoom: 18 }
    //   },
    //   glyphs: 'mapbox://fonts/mapbox/{fontstack}/{range}.pbf',
    //   layers: [{ id: 'simple-tiles1', type: 'raster', source: 'osm-tiles1' }]
    // }
  },
  sources: {
    // Plotoutercontour: {
    //   type: 'vector',
    //   tiles: ['https://www.szwgft.cn/mapServer/geoserver/gwc/service/tms/1.0.0/Code%3APlotoutercontour@EPSG%3A3857@pbf/{z}/{x}/{y}.pbf'],
    //   scheme: 'tms',
    //   layers: {
    //     Plotoutercontour_fill: {
    //       options: { 'source-layer': 'Plotoutercontour', paint: { 'fill-color': '#2e2e2e', 'fill-opacity': 1 } }
    //     }
    //   }
    // }
    // FT_ZONE
    // dots: {
    //   type: 'geojson',
    //   data: dots_t,
    //   layers: {
    //     dot_circle: {
    //       options: {
    //         paint: {
    //           'circle-radius': 2,
    //           'circle-color': 'red'
    //         }
    //       }
    //     }
    //   }
    // }
  }
}

function load(Map) {
  let tb
  Map.addLayer({
    id: 'custom_layer',
    type: 'custom',

    async onAdd(map, mbxContext) {
      let timeInCount = 60
      let timeInterv = 2000
      let scale = 15
      let aniindexArr = new Array(8).fill({ endPointIndex: 0 })
      tb = new Threebox(map, mbxContext, { defaultLights: true })
      let lineGroup = new THREE.Group()
      tb.add(lineGroup)

      let alllines = treatingDots(tb, dots.features)

      alllines.forEach((line, index) => {
        const lineMesh = drawLine(line)
        lineMesh._index = index
        lineMesh._data = line
        lineGroup.add(lineMesh)
      })

      lineGroup.traverse((child) => {
        const { _data, _index } = child
        if (!_index) return

        let tween1 = new TWEEN.Tween(aniindexArr[0])
          .to({ endPointIndex: timeInCount }, timeInterv)
          .onUpdate(function (iii) {
            let vertices = []
            let colors = []
            for (let i = 0; i < _data.length; i++) {
              let item = _data[i]
              let begain = item[2] * scale
              let end = item[3] * scale
              let h = begain + ((end - begain) / timeInCount) * iii.endPointIndex
              vertices.push(new THREE.Vector3(item[0], item[1], h))
              colors.push(getColorByValue(h))
            }
            child.geometry.vertices = vertices
            child.geometry.colors = colors
            child.geometry.verticesNeedUpdate = true
            child.geometry.colorsNeedUpdate = true
          })
          .onComplete(function () {
            aniindexArr[0].endPointIndex = 0
            console.log(0)
          })
        let tween2 = new TWEEN.Tween(aniindexArr[1])
          .to({ endPointIndex: timeInCount }, timeInterv)
          .onUpdate(function (iii) {
            let vertices = []
            let colors = []
            for (let i = 0; i < _data.length; i++) {
              let item = _data[i]
              let begain = item[3] * scale
              let end = item[4] * scale
              let h = begain + ((end - begain) / timeInCount) * iii.endPointIndex
              vertices.push(new THREE.Vector3(item[0], item[1], h))
              colors.push(getColorByValue(h))
            }
            child.geometry.vertices = vertices
            child.geometry.colors = colors
            child.geometry.verticesNeedUpdate = true
            child.geometry.colorsNeedUpdate = true
          })
          .onComplete(function () {
            aniindexArr[1].endPointIndex = 0
            console.log(1)
          })
        let tween3 = new TWEEN.Tween(aniindexArr[2])
          .to({ endPointIndex: timeInCount }, timeInterv)
          .onUpdate(function (iii) {
            let vertices = []
            let colors = []
            for (let i = 0; i < _data.length; i++) {
              let item = _data[i]
              let begain = item[4] * scale
              let end = item[5] * scale
              let h = begain + ((end - begain) / timeInCount) * iii.endPointIndex
              vertices.push(new THREE.Vector3(item[0], item[1], h))
              colors.push(getColorByValue(h))
            }
            child.geometry.vertices = vertices
            child.geometry.colors = colors
            child.geometry.verticesNeedUpdate = true
            child.geometry.colorsNeedUpdate = true
          })
          .onComplete(function () {
            aniindexArr[2].endPointIndex = 0
            console.log(2)
          })
        let tween4 = new TWEEN.Tween(aniindexArr[3])
          .to({ endPointIndex: timeInCount }, timeInterv)
          .onUpdate(function (iii) {
            let vertices = []
            let colors = []
            for (let i = 0; i < _data.length; i++) {
              let item = _data[i]
              let begain = item[5] * scale
              let end = item[6] * scale
              let h = begain + ((end - begain) / timeInCount) * iii.endPointIndex
              vertices.push(new THREE.Vector3(item[0], item[1], h))
              colors.push(getColorByValue(h))
            }
            child.geometry.vertices = vertices
            child.geometry.colors = colors
            child.geometry.verticesNeedUpdate = true
            child.geometry.colorsNeedUpdate = true
          })
          .onComplete(function () {
            aniindexArr[3].endPointIndex = 0
            console.log(3)
          })
        let tween5 = new TWEEN.Tween(aniindexArr[4])
          .to({ endPointIndex: timeInCount }, timeInterv)
          .onUpdate(function (iii) {
            let vertices = []
            let colors = []
            for (let i = 0; i < _data.length; i++) {
              let item = _data[i]
              let begain = item[6] * scale
              let end = item[7] * scale
              let h = begain + ((end - begain) / timeInCount) * iii.endPointIndex
              vertices.push(new THREE.Vector3(item[0], item[1], h))
              colors.push(getColorByValue(h))
            }
            child.geometry.vertices = vertices
            child.geometry.colors = colors
            child.geometry.verticesNeedUpdate = true
            child.geometry.colorsNeedUpdate = true
          })
          .onComplete(function () {
            aniindexArr[4].endPointIndex = 0
            console.log(4)
          })
        let tween6 = new TWEEN.Tween(aniindexArr[5])
          .to({ endPointIndex: timeInCount }, timeInterv)
          .onUpdate(function (iii) {
            let vertices = []
            let colors = []
            for (let i = 0; i < _data.length; i++) {
              let item = _data[i]
              let begain = item[7] * scale
              let end = item[8] * scale
              let h = begain + ((end - begain) / timeInCount) * iii.endPointIndex
              vertices.push(new THREE.Vector3(item[0], item[1], h))
              colors.push(getColorByValue(h))
            }
            child.geometry.vertices = vertices
            child.geometry.colors = colors
            child.geometry.verticesNeedUpdate = true
            child.geometry.colorsNeedUpdate = true
          })
          .onComplete(function () {
            aniindexArr[5].endPointIndex = 0
            console.log(5)
          })
        let tween7 = new TWEEN.Tween(aniindexArr[6])
          .to({ endPointIndex: timeInCount }, timeInterv)
          .onUpdate(function (iii) {
            let vertices = []
            let colors = []
            for (let i = 0; i < _data.length; i++) {
              let item = _data[i]
              let begain = item[8] * scale
              let end = item[9] * scale
              let h = begain + ((end - begain) / timeInCount) * iii.endPointIndex
              vertices.push(new THREE.Vector3(item[0], item[1], h))
              colors.push(getColorByValue(h))
            }
            child.geometry.vertices = vertices
            child.geometry.colors = colors
            child.geometry.verticesNeedUpdate = true
            child.geometry.colorsNeedUpdate = true
          })
          .onComplete(function () {
            aniindexArr[6].endPointIndex = 0
          })

        tween1.chain(tween2)
        tween2.chain(tween3)
        tween3.chain(tween4)
        tween4.chain(tween5)
        tween5.chain(tween6)
        tween6.chain(tween1)
        // tween7.chain(tween1)
        tween1.start()
      })
    },
    render() {
      Map.triggerRepaint()
      tb.update()
      TWEEN.update()
    }
  })
}

// 处理数据
function treatingDots(tb, dotArr) {
  // 坐标转换
  let resArr = dotArr.map((item) => {
    const { geometry, properties } = item
    let { x, y } = tb.projectToWorld(geometry.coordinates)
    return [x, y, properties['00'], properties['04'], properties['08'], properties['12'], properties['16'], properties['20'], properties['24']]
  })

  let tmpColums = []
  for (let i = 0; i < row; i++) {
    for (let j = 0; j < column; j++) {
      let item = resArr[row * j + i]
      tmpColums.push(item)
    }
  }
  return [...chunk(resArr, row), ...chunk(tmpColums, column)]
}

function drawLine(row) {
  let vertices = []
  let colors = []
  let geometry = new THREE.Geometry()

  row.forEach(([x, y, z]) => {
    vertices.push(new THREE.Vector3(x, y, z))
    colors.push(getColorByValue(z))
  })

  let material = new THREE.LineBasicMaterial({ opacity: 1, linewidth: 22, vertexColors: THREE.VertexColors, blending: THREE.AdditiveBlending })
  geometry.vertices = vertices
  geometry.colors = colors
  let lineMesh = new THREE.Line(geometry, material)
  lineMesh.geometry.verticesNeedUpdate = true
  lineMesh.geometry.colorsNeedUpdate = true
  return lineMesh
}

let colors = [
  new THREE.Color(`rgb( 0, 0, 0)`),
  new THREE.Color(`rgb(20, 130, 89)`),
  new THREE.Color(`rgb( 31, 196, 54)`),
  new THREE.Color(`rgb( 171, 190, 52)`),
  new THREE.Color(`rgb( 201, 155, 52)`),
  new THREE.Color(`rgb( 205, 122, 45)`),
  new THREE.Color(`rgb( 160, 0, 0)`),
  new THREE.Color(`rgb( 180, 0, 0)`)
]
function getColorByValue(value) {
  let tvalue = Number(value)
  if (tvalue < 1) {
    return colors[0]
  } else if (tvalue < 2) {
    return colors[1]
  } else if (tvalue <= 4) {
    return colors[2]
  } else if (tvalue < 6) {
    return colors[3]
  } else if (tvalue < 8) {
    return colors[4]
  } else if (tvalue < 10) {
    return colors[5]
  } else if (tvalue < 12) {
    return colors[6]
  } else {
    return colors[7]
  }
}
</script>
