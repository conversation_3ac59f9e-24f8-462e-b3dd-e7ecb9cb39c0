<template>
  <div class="demo-page">
    <div class="demo-header">
      <h1>档案选择弹窗 UI 设计演示</h1>
      <p>这是重新设计的档案选择弹窗组件演示页面</p>
    </div>

    <div class="demo-content">
      <div class="demo-section">
        <h2>弹窗预览</h2>
        <div class="popup-container">
          <RecordSelectPopup :data="mockData" />
        </div>
      </div>

      <div class="demo-section">
        <h2>设计特点</h2>
        <div class="features-grid">
          <div class="feature-card">
            <div class="feature-icon">🎨</div>
            <h3>现代化设计</h3>
            <p>采用渐变背景、圆角边框和阴影效果，提升视觉体验</p>
          </div>
          <div class="feature-card">
            <div class="feature-icon">📱</div>
            <h3>响应式布局</h3>
            <p>适配不同屏幕尺寸，在移动端和桌面端都有良好表现</p>
          </div>
          <div class="feature-card">
            <div class="feature-icon">⚡</div>
            <h3>交互体验</h3>
            <p>悬停效果、点击反馈和平滑过渡动画</p>
          </div>
          <div class="feature-card">
            <div class="feature-icon">🔧</div>
            <h3>组件化设计</h3>
            <p>使用 Ant Design Vue 图标，保持项目风格一致</p>
          </div>
        </div>
      </div>

      <div class="demo-section">
        <h2>不同数据量演示</h2>
        <div class="demo-buttons">
          <a-button @click="setMockData(1)" type="primary">单个档案</a-button>
          <a-button @click="setMockData(3)" type="default">3个档案</a-button>
          <a-button @click="setMockData(5)" type="default">5个档案</a-button>
          <a-button @click="setMockData(0)" type="dashed">空数据</a-button>
        </div>
      </div>

      <div class="demo-section">
        <h2>技术实现</h2>
        <div class="tech-details">
          <ul>
            <li><strong>Vue 3 Composition API</strong> - 使用最新的 Vue 3 语法</li>
            <li><strong>Ant Design Vue</strong> - 使用项目已有的 UI 组件库</li>
            <li><strong>Less 预处理器</strong> - 支持嵌套样式和变量</li>
            <li><strong>响应式设计</strong> - 使用媒体查询适配移动端</li>
            <li><strong>自定义滚动条</strong> - 美化滚动条样式</li>
            <li><strong>CSS 动画</strong> - 平滑的过渡效果和悬停动画</li>
          </ul>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import RecordSelectPopup from '@/views/main/children/schedule/children/menu-map/components/recordSelectPopup.vue'

const mockData = ref([
  {
    xqmc: '福田花园小区',
    selectfn: (item) => {
      console.log('选择了档案:', item.xqmc)
      alert(`已选择档案: ${item.xqmc}`)
    }
  },
  {
    xqmc: '深圳湾壹号',
    selectfn: (item) => {
      console.log('选择了档案:', item.xqmc)
      alert(`已选择档案: ${item.xqmc}`)
    }
  },
  {
    xqmc: '华润城润府',
    selectfn: (item) => {
      console.log('选择了档案:', item.xqmc)
      alert(`已选择档案: ${item.xqmc}`)
    }
  },
  {
    xqmc: '卓越世纪中心',
    selectfn: (item) => {
      console.log('选择了档案:', item.xqmc)
      alert(`已选择档案: ${item.xqmc}`)
    }
  },
  {
    xqmc: '平安金融中心',
    selectfn: (item) => {
      console.log('选择了档案:', item.xqmc)
      alert(`已选择档案: ${item.xqmc}`)
    }
  }
])

const setMockData = (count) => {
  const allData = [
    { xqmc: '福田花园小区', selectfn: (item) => alert(`已选择档案: ${item.xqmc}`) },
    { xqmc: '深圳湾壹号', selectfn: (item) => alert(`已选择档案: ${item.xqmc}`) },
    { xqmc: '华润城润府', selectfn: (item) => alert(`已选择档案: ${item.xqmc}`) },
    { xqmc: '卓越世纪中心', selectfn: (item) => alert(`已选择档案: ${item.xqmc}`) },
    { xqmc: '平安金融中心', selectfn: (item) => alert(`已选择档案: ${item.xqmc}`) }
  ]
  
  mockData.value = allData.slice(0, count)
}
</script>

<style lang="less" scoped>
.demo-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  padding: 40px 20px;
}

.demo-header {
  text-align: center;
  margin-bottom: 40px;
  
  h1 {
    font-size: 32px;
    color: #2c3e50;
    margin-bottom: 10px;
    font-weight: 600;
  }
  
  p {
    font-size: 16px;
    color: #7f8c8d;
    margin: 0;
  }
}

.demo-content {
  max-width: 1200px;
  margin: 0 auto;
}

.demo-section {
  background: #fff;
  border-radius: 12px;
  padding: 30px;
  margin-bottom: 30px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  
  h2 {
    font-size: 24px;
    color: #2c3e50;
    margin-bottom: 20px;
    font-weight: 600;
  }
}

.popup-container {
  display: flex;
  justify-content: center;
  padding: 40px 20px;
  background: #f8f9fa;
  border-radius: 8px;
  border: 2px dashed #dee2e6;
}

.features-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
}

.feature-card {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 20px;
  text-align: center;
  transition: transform 0.2s ease;
  
  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  }
  
  .feature-icon {
    font-size: 32px;
    margin-bottom: 12px;
  }
  
  h3 {
    font-size: 18px;
    color: #2c3e50;
    margin-bottom: 8px;
    font-weight: 600;
  }
  
  p {
    font-size: 14px;
    color: #6c757d;
    line-height: 1.5;
    margin: 0;
  }
}

.demo-buttons {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
}

.tech-details {
  ul {
    list-style: none;
    padding: 0;
    
    li {
      padding: 8px 0;
      border-bottom: 1px solid #eee;
      font-size: 14px;
      color: #495057;
      
      &:last-child {
        border-bottom: none;
      }
      
      strong {
        color: #2c3e50;
      }
    }
  }
}

@media (max-width: 768px) {
  .demo-page {
    padding: 20px 10px;
  }
  
  .demo-header h1 {
    font-size: 24px;
  }
  
  .demo-section {
    padding: 20px;
  }
  
  .features-grid {
    grid-template-columns: 1fr;
  }
  
  .demo-buttons {
    justify-content: center;
  }
}
</style>
