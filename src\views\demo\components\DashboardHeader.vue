<template>
  <div class="dashboard-header">
    <div class="header-background">
      <div class="bg-gradient"></div>
    </div>

    <div class="header-container">
      <div class="title-area">
        <div class="title-badge">
          <div class="badge-icon">📊</div>
          <span class="badge-text">数据中心</span>
        </div>
        <h1 class="main-title">排水设施数据统计</h1>
        <p class="subtitle">
          <span class="status-dot"></span>
          · 智能分析 · 精准决策
        </p>
      </div>

      <div class="controls-area">
        <div class="date-selector">
          <div class="selector-label">数据周期</div>

          <!-- 区局管辖开关 -->
          <div class="jurisdiction-switch">
            <div class="switch-container">
              <span class="switch-label">包含区局管辖</span>
              <wd-switch v-model="includeDistrict" size="20px" @change="handleDistrictChange" />
            </div>
          </div>

          <!-- 日历选择器 -->
          <div class="calendar-container">
            <wd-calendar type="month" custom-class="compact-calendar" v-model="selectedDate" @confirm="handleDateConfirm" />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, watch } from 'vue'

// Props
const props = defineProps({
  // 是否包含区局管辖
  modelValue: {
    type: Boolean,
    default: true
  },
  // 选中的日期
  dateValue: {
    type: String,
    default: null
  }
})

// Emits
const emit = defineEmits(['update:modelValue', 'update:dateValue', 'dateChange', 'districtChange'])

// 响应式数据
const includeDistrict = ref(props.modelValue)
const selectedDate = ref(props.dateValue)

// 监听 props 变化
watch(
  () => props.modelValue,
  (newVal) => {
    includeDistrict.value = newVal
  }
)

watch(
  () => props.dateValue,
  (newVal) => {
    selectedDate.value = newVal
  }
)

// 监听内部状态变化并向外传递
watch(includeDistrict, (newVal) => {
  emit('update:modelValue', newVal)
})

watch(selectedDate, (newVal) => {
  emit('update:dateValue', newVal)
})

// 事件处理
const handleDistrictChange = (value) => {
  emit('districtChange', value)
}

const handleDateConfirm = ({ value }) => {
  // 将时间戳转换为 YYYY-MM 格式
  const date = new Date(value)
  const year = date.getFullYear()
  const month = String(date.getMonth() + 1).padStart(2, '0')
  const formattedDate = `${year}-${month}`

  selectedDate.value = formattedDate
  emit('dateChange', formattedDate)
}
</script>

<style lang="less" scoped>
// 优化后的仪表板头部设计
.dashboard-header {
  position: relative;
  z-index: 9;
  border-radius: 0 0 24rpx 24rpx;
  overflow: visible; // 改为 visible 让弹窗能够显示

  .header-background {
    position: absolute;
    inset: 0;
    z-index: 0;
    border-radius: 0 0 24rpx 24rpx;
    overflow: hidden; // 背景容器保持 overflow: hidden

    .bg-gradient {
      position: absolute;
      inset: 0;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    }
  }

  .header-container {
    position: relative;
    z-index: 1;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 32rpx 32rpx 28rpx 32rpx;

    .title-area {
      flex: 1;

      .title-badge {
        display: inline-flex;
        align-items: center;
        gap: 8rpx;
        background: rgba(255, 255, 255, 0.15);
        border: 1rpx solid rgba(255, 255, 255, 0.2);
        border-radius: 16rpx;
        padding: 6rpx 12rpx;
        margin-bottom: 12rpx;

        .badge-icon {
          font-size: 16rpx;
        }

        .badge-text {
          font-size: 20rpx;
          font-weight: 500;
          color: rgba(255, 255, 255, 0.9);
          letter-spacing: 0.5rpx;
        }
      }

      .main-title {
        font-size: 40rpx;
        font-weight: 700;
        color: white;
        line-height: 1.2;
        margin: 0 0 8rpx 0;
        text-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.2);
      }

      .subtitle {
        display: flex;
        align-items: center;
        gap: 8rpx;
        font-size: 24rpx;
        color: rgba(255, 255, 255, 0.8);
        margin: 0;
        font-weight: 400;

        .status-dot {
          width: 6rpx;
          height: 6rpx;
          background: #00ff88;
          border-radius: 50%;
          animation: statusPulse 2s infinite;
          box-shadow: 0 0 6rpx #00ff88;
        }
      }
    }

    .controls-area {
      .date-selector {
        text-align: right;

        .selector-label {
          font-size: 20rpx;
          color: rgba(255, 255, 255, 0.7);
          margin-bottom: 12rpx;
          font-weight: 500;
        }

        // 区局管辖开关样式
        .jurisdiction-switch {
          margin-bottom: 16rpx;

          .switch-container {
            display: flex;
            align-items: center;
            justify-content: flex-end;
            gap: 12rpx;
            background: rgba(255, 255, 255, 0.08);
            border: 1rpx solid rgba(255, 255, 255, 0.15);
            border-radius: 20rpx;
            padding: 8rpx 16rpx;
            transition: all 0.3s ease;

            .switch-label {
              font-size: 22rpx;
              color: rgba(255, 255, 255, 0.85);
              font-weight: 500;
              letter-spacing: 0.5rpx;
            }
          }
        }

        .calendar-container {
          position: relative;
          background: rgba(255, 255, 255, 0.1);
          border: 1rpx solid rgba(255, 255, 255, 0.2);
          border-radius: 12rpx;
          padding: 8rpx 12rpx;
          transition: all 0.3s ease;
          z-index: 10; // 确保日历容器有足够的层级
        }
      }
    }
  }
}

// 动画效果
@keyframes statusPulse {
  0%,
  100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.7;
    transform: scale(1.2);
  }
}
</style>

<!-- 简化的全局样式 - 确保微信小程序兼容性 -->
<style>
/* 日历弹窗层级 - 全局样式 */
.wd-calendar {
  z-index: 9999 !important;
}

.wd-calendar__popup {
  z-index: 9999 !important;
  position: fixed !important;
}

.wd-calendar__popup-mask {
  z-index: 9998 !important;
}
</style>
