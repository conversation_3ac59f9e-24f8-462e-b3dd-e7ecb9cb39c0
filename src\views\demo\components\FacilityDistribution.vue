<template>
  <div class="data-section">
    <div class="section-header" @click="toggleCollapse">
      <div class="header-left">
        <div class="section-icon">{{ icon }}</div>
        <div class="section-info">
          <div class="section-title">{{ title }}</div>
          <div class="section-subtitle">{{ subtitle }}</div>
        </div>
        <div class="section-count">{{ data.length }} 个</div>
      </div>
      <div class="collapse-btn" :class="{ collapsed: isCollapsed }">
        <div class="collapse-icon">
          <div class="icon-line line-1"></div>
          <div class="icon-line line-2"></div>
        </div>
      </div>
    </div>
    <div class="data-list-wrapper" :class="{ 'wrapper-collapsed': isCollapsed }">
      <div class="data-list">
        <template v-for="(item, listIndex) in data" :key="item.key">
          <div class="list-item" @click="emit('trigger', item)" :class="{ 'item-even': listIndex % 2 === 0 }">
            <div class="item-content">
              <div class="item-left">
                <div class="item-icon">{{ icon }}</div>
                <div class="item-name">{{ item.key }}</div>
              </div>
              <div class="item-center">
                <div class="stat-group">
                  <span class="stat-label">市政</span>
                  <span class="stat-value municipal">{{ formatValue(item.municipal) }}</span>
                </div>
                <div class="stat-divider">|</div>
                <div class="stat-group">
                  <span class="stat-label">小区</span>
                  <span class="stat-value plot">{{ formatValue(item.plot) }}</span>
                </div>
              </div>
              <div class="item-right">
                <div class="item-total">
                  <span class="total-value">{{ formatValue(item.plot + item.municipal) }}</span>
                </div>
              </div>
            </div>
          </div>
        </template>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'

const props = defineProps({
  data: {
    type: Array,
    default: () => []
  },
  title: {
    type: String,
    required: true
  },
  subtitle: {
    type: String,
    required: true
  },
  icon: {
    type: String,
    required: true
  },
  unit: {
    type: String,
    default: ''
  },
  defaultCollapsed: {
    type: Boolean,
    default: true
  }
})

const emit = defineEmits(['toggle', 'trigger'])

const isCollapsed = ref(props.defaultCollapsed)

const toggleCollapse = () => {
  isCollapsed.value = !isCollapsed.value
  emit('toggle', isCollapsed.value)
}

const formatValue = (value) => {
  if (props.unit === 'km') {
    return value.toFixed(2)
  }
  return Math.round(value)
}
</script>

<style lang="less" scoped>
.data-section {
  margin-bottom: 32rpx;

  .section-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 16rpx;
    padding: 24rpx;
    background: linear-gradient(135deg, #f8fbff 0%, #f0f8ff 100%);
    border-radius: 16rpx;
    border: 1rpx solid #e8f4fd;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;

    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: -100%;
      width: 100%;
      height: 100%;
      background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
      animation: headerShine 3s infinite;
    }

    &:hover {
      background: linear-gradient(135deg, #f0f8ff 0%, #e8f4fd 100%);
      transform: translateY(-2rpx);
      box-shadow: 0 4rpx 12rpx rgba(73, 162, 222, 0.15);
    }

    .header-left {
      display: flex;
      align-items: center;
      gap: 16rpx;
      flex: 1;

      .section-icon {
        font-size: 32rpx;
        filter: drop-shadow(0 2rpx 4rpx rgba(0, 0, 0, 0.1));
      }

      .section-info {
        flex: 1;

        .section-title {
          font-size: 28rpx;
          font-weight: 700;
          color: #2c3e50;
          margin-bottom: 4rpx;
          line-height: 1.2;
        }

        .section-subtitle {
          font-size: 22rpx;
          color: #7f8c8d;
          line-height: 1.2;
        }
      }

      .section-count {
        background: linear-gradient(135deg, #e8f5e8 0%, #f0f9ff 100%);
        color: #27ae60;
        font-size: 20rpx;
        font-weight: 600;
        padding: 8rpx 12rpx;
        border-radius: 12rpx;
        border: 1rpx solid #d4edda;
        box-shadow: 0 2rpx 6rpx rgba(39, 174, 96, 0.1);
      }
    }

    .collapse-btn {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 56rpx;
      height: 56rpx;
      background: linear-gradient(135deg, rgba(73, 162, 222, 0.1) 0%, rgba(102, 126, 234, 0.1) 100%);
      border: 2rpx solid rgba(73, 162, 222, 0.2);
      border-radius: 50%;
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
      backdrop-filter: blur(10rpx);
      cursor: pointer;
      position: relative;
      overflow: hidden;

      &::before {
        content: '';
        position: absolute;
        inset: 0;
        background: linear-gradient(135deg, rgba(73, 162, 222, 0.2), rgba(102, 126, 234, 0.2));
        border-radius: 50%;
        opacity: 0;
        transition: opacity 0.3s ease;
      }

      &:hover {
        background: linear-gradient(135deg, rgba(73, 162, 222, 0.15) 0%, rgba(102, 126, 234, 0.15) 100%);
        border-color: rgba(73, 162, 222, 0.3);
        transform: scale(1.05);
        box-shadow: 0 4rpx 12rpx rgba(73, 162, 222, 0.2);

        &::before {
          opacity: 1;
        }
      }

      .collapse-icon {
        position: relative;
        width: 24rpx;
        height: 24rpx;

        .icon-line {
          position: absolute;
          width: 16rpx;
          height: 3rpx;
          background: linear-gradient(135deg, #49a2de 0%, #667eea 100%);
          border-radius: 2rpx;
          transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
          box-shadow: 0 1rpx 3rpx rgba(73, 162, 222, 0.3);

          &.line-1 {
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%) rotate(0deg);
            transform-origin: center;
          }

          &.line-2 {
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%) rotate(90deg);
            transform-origin: center;
          }
        }
      }

      &.collapsed {
        .collapse-icon {
          .icon-line {
            &.line-2 {
              transform: translate(-50%, -50%) rotate(0deg);
            }
          }
        }
      }
    }
  }

  .data-list-wrapper {
    max-height: 1000rpx;
    overflow: hidden;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);

    &.wrapper-collapsed {
      max-height: 0;
      margin-top: -16rpx;
    }

    .data-list {
      background: white;
      border-radius: 16rpx;
      overflow: hidden;
      box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.08);
      border: 1rpx solid #f0f8ff;

      .list-item {
        border-bottom: 1rpx solid #f8fbff;
        transition: all 0.3s ease;

        &:last-child {
          border-bottom: none;
        }

        &:hover {
          background: linear-gradient(135deg, #f8fbff 0%, #f0f8ff 100%);
          transform: translateY(-1rpx);
          box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
        }

        &.item-even {
          background: linear-gradient(135deg, #fafbff 0%, #f8fbff 100%);
        }

        .item-content {
          display: flex;
          align-items: center;
          padding: 20rpx 24rpx;
          gap: 16rpx;

          .item-left {
            display: flex;
            align-items: center;
            gap: 12rpx;
            min-width: 160rpx;

            .item-icon {
              font-size: 24rpx;
              filter: drop-shadow(0 1rpx 2rpx rgba(0, 0, 0, 0.1));
            }

            .item-name {
              font-size: 26rpx;
              font-weight: 600;
              color: #2c3e50;
              line-height: 1.2;
            }
          }

          .item-center {
            flex: 1;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 12rpx;

            .stat-group {
              display: flex;
              align-items: baseline;
              gap: 4rpx;

              .stat-label {
                font-size: 18rpx;
                color: #6c757d;
                font-weight: 500;
              }

              .stat-value {
                font-size: 28rpx;
                font-weight: 600;
                font-family: 'Courier New', monospace;

                &.municipal {
                  color: #1976d2;
                }

                &.plot {
                  color: #6c80e8;
                }
              }
            }

            .stat-divider {
              color: #e8f4fd;
              font-size: 16rpx;
              font-weight: 300;
            }
          }

          .item-right {
            min-width: 80rpx;
            text-align: right;

            .item-total {
              .total-value {
                font-size: 30rpx;
                font-weight: 700;
                color: #28a745;
                font-family: 'Courier New', monospace;
              }
            }
          }
        }
      }
    }
  }
}

@keyframes headerShine {
  0% {
    left: -100%;
  }
  100% {
    left: 100%;
  }
}
</style>
