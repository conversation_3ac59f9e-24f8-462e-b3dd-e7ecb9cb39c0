<template>
  <div class="pipe-fitting-section" v-if="pipeFittingData">
    <!-- 管件统计卡片头部 - 统一设施卡片样式 -->
    <div class="card-header" @click="toggleCollapse">
      <div class="header-left">
        <div class="facility-icon">
          <image class="icon-img" src="/static/img/drainage-fitting.svg"></image>
        </div>
        <div class="facility-info">
          <h3 class="facility-title">{{ pipeFittingData.key }}</h3>
          <p class="facility-subtitle">管件统计数据</p>
        </div>
      </div>
      <div class="header-right">
        <div class="total-badge">
          <span class="total-number">{{ formatTotal(pipeFittingData.total) }}</span>
          <span class="total-unit">{{ pipeFittingData.unit == 'km' ? 'km' : '个' }}</span>
        </div>
        <div class="collapse-btn" :class="{ collapsed: isCollapsed }">
          <div class="collapse-icon">
            <div class="icon-line line-1"></div>
            <div class="icon-line line-2"></div>
          </div>
        </div>
      </div>
    </div>

    <!-- 图表展示区域 -->
    <div class="card-content-wrapper" :class="{ 'wrapper-collapsed': isCollapsed }">
      <div class="card-content">
        <!-- 水务所划分图表 -->
        <div class="chart-section">
          <div class="chart-header">
            <div class="chart-icon">🏢</div>
            <div class="chart-title">水务所划分</div>
            <div class="chart-subtitle">按水务所统计管件分布</div>
          </div>
          <div class="chart-wrapper">
            <qiun-data-charts
              v-if="shouldShowChart && pipeFittingData && pipeFittingData.waterdept"
              :key="`waterdept-${chartRefreshKey}`"
              type="column"
              :opts="columnChartOpts"
              :chartData="pipeFittingData.waterdept"
              :canvas2d="true"
              :canvasId="`waterdept-chart-${chartRefreshKey}`"
            />
            <div v-else-if="!hasValidData" class="chart-loading">
              <text>{{ loadingText }}</text>
            </div>
            <!-- 备用数据表格显示 -->
            <div v-else class="chart-fallback">
              <div class="fallback-title">水务所划分数据</div>
              <div v-if="pipeFittingData.waterdept && pipeFittingData.waterdept.categories" class="data-list">
                <div v-for="(item, index) in pipeFittingData.waterdept.categories" :key="index" class="data-item">
                  <span class="data-label">{{ item }}</span>
                  <span class="data-value">{{ pipeFittingData.waterdept.series[0]?.data[index] || 0 }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 网格划分图表 -->
        <div class="chart-section">
          <div class="chart-header">
            <div class="chart-icon">🗂️</div>
            <div class="chart-title">网格划分</div>
            <div class="chart-subtitle">按网格区域统计管件分布</div>
          </div>
          <div class="chart-wrapper">
            <qiun-data-charts
              v-if="shouldShowChart && pipeFittingData && pipeFittingData.grid"
              :key="`grid-${chartRefreshKey}`"
              type="bar"
              :opts="barChartOpts"
              :chartData="pipeFittingData.grid"
              :canvas2d="true"
              :canvasId="`grid-chart-${chartRefreshKey}`"
            />
            <div v-else-if="!hasValidData" class="chart-loading">
              <text>{{ loadingText }}</text>
            </div>
            <!-- 备用数据表格显示 -->
            <div v-else class="chart-fallback">
              <div class="fallback-title">网格划分数据</div>
              <div v-if="pipeFittingData.grid && pipeFittingData.grid.categories" class="data-list">
                <div v-for="(item, index) in pipeFittingData.grid.categories" :key="index" class="data-item">
                  <span class="data-label">{{ item }}</span>
                  <span class="data-value">{{ pipeFittingData.grid.series[0]?.data[index] || 0 }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, nextTick, onMounted } from 'vue'

// Props 定义
const props = defineProps({
  pipeFittingData: {
    type: Object,
    default: () => null
  },
  defaultCollapsed: {
    type: Boolean,
    default: true
  }
})

// 折叠状态管理
const isCollapsed = ref(props.defaultCollapsed)
const chartRefreshKey = ref(0)
const isChartReady = ref(false)

// 检测是否为微信小程序环境
const isWechatMiniProgram = ref(false)

// 检测运行环境
onMounted(() => {
  // #ifdef MP-WEIXIN
  isWechatMiniProgram.value = true
  // #endif

  // 也可以通过uni对象检测
  if (typeof uni !== 'undefined' && uni.getSystemInfoSync) {
    const systemInfo = uni.getSystemInfoSync()
    if (systemInfo.platform === 'devtools' || systemInfo.environment === 'miniprogram') {
      isWechatMiniProgram.value = true
    }
  }
})

// 简化的数据验证
const hasValidData = computed(() => {
  return !!(props.pipeFittingData && props.pipeFittingData.waterdept && props.pipeFittingData.grid)
})

// 图表显示条件
const shouldShowChart = computed(() => {
  return isChartReady.value && hasValidData.value
})

// 加载文本
const loadingText = computed(() => {
  if (!hasValidData.value) {
    return '暂无数据'
  }
  if (!isChartReady.value) {
    return '图表加载中...'
  }
  return '图表准备中...'
})

// 组件挂载后初始化
onMounted(() => {
  // 微信小程序需要更长的延迟时间确保DOM完全渲染
  const delay = isWechatMiniProgram.value ? 300 : 100
  setTimeout(() => {
    isChartReady.value = true
  }, delay)
})

// 监听数据变化，确保图表在数据更新时重新渲染
// watch(
//   () => props.pipeFittingData,
//   (newData) => {
//     console.log('PipeFittingChart: 数据变化', {
//       hasData: !!newData,
//       hasWaterdept: !!(newData && newData.waterdept),
//       hasGrid: !!(newData && newData.grid),
//       isChartReady: isChartReady.value,
//       shouldShow: shouldShowChart.value,
//       isWechat: isWechatMiniProgram.value
//     })

//     if (newData && isChartReady.value) {
//       // 数据更新时强制刷新图表
//       nextTick(() => {
//         chartRefreshKey.value++
//       })
//     }
//   },
//   { deep: true, immediate: true }
// )

// 格式化总数显示
const formatTotal = (total, precision = 0) => {
  if (!total && total !== 0) return '0'

  const num = Number(total)
  if (isNaN(num)) return '0'

  if (num >= 10000) {
    return (num / 10000).toFixed(precision) + '万'
  } else if (num >= 1000) {
    return (num / 1000).toFixed(precision) + 'k'
  }

  return num.toLocaleString()
}

// 切换折叠状态
const toggleCollapse = () => {
  isCollapsed.value = !isCollapsed.value
  uni.vibrateShort({ type: 'medium' })
  // 如果是展开状态，延迟更新图表key来强制重新渲染
  if (!isCollapsed.value) {
    nextTick(() => {
      // 微信小程序需要更长的延迟时间
      const delay = isWechatMiniProgram.value ? 800 : 500
      setTimeout(() => {
        chartRefreshKey.value++
      }, delay) // 等待折叠动画完成
    })
  }
}

// 柱状图配置 - 针对微信小程序优化
const columnChartOpts = computed(() => {
  const baseOpts = {
    color: ['#4facfe', '#00f2fe', '#43e97b', '#38f9d7', '#667eea', '#764ba2'],
    padding: [20, 20, 10, 10],
    enableScroll: false,
    animation: false, // 微信小程序关闭动画
    legend: {
      show: true,
      position: 'top',
      fontSize: 12,
      margin: 5
    },
    xAxis: {
      disableGrid: true,
      fontSize: 10,
      fontColor: '#666666'
    },
    yAxis: {
      data: [{ min: 0 }],
      fontSize: 11,
      fontColor: '#666666'
    },
    extra: {
      column: {
        type: 'group',
        width: 25,
        activeBgColor: '#4facfe',
        activeBgOpacity: 0.1,
        borderRadius: 4
      }
    }
  }

  // 微信小程序特殊配置
  if (isWechatMiniProgram.value) {
    baseOpts.pixelRatio = 2
    baseOpts.enableMarkLine = false
    baseOpts.enableScroll = false
  }

  return baseOpts
})

// 条形图配置 - 针对微信小程序优化
const barChartOpts = computed(() => {
  const baseOpts = {
    color: ['#667eea', '#764ba2', '#f093fb', '#f5576c', '#4facfe', '#00f2fe'],
    padding: [15, 50, 15, 10],
    enableScroll: false,
    animation: false, // 微信小程序关闭动画
    legend: {
      show: true,
      position: 'top',
      fontSize: 12,
      margin: 5
    },
    xAxis: {
      boundaryGap: 'justify',
      disabled: true,
      disableGrid: false,
      min: 0,
      axisLine: false,
      fontSize: 11,
      fontColor: '#666666'
    },
    yAxis: {
      fontSize: 11,
      fontColor: '#666666'
    },
    extra: {
      bar: {
        type: 'stack',
        width: 35,
        meterBorder: 1,
        meterFillColor: '#FFFFFF',
        activeBgColor: '#667eea',
        activeBgOpacity: 0.1,
        categoryGap: 3,
        borderRadius: 4
      }
    }
  }

  // 微信小程序特殊配置
  if (isWechatMiniProgram.value) {
    baseOpts.pixelRatio = 2
    baseOpts.enableMarkLine = false
    baseOpts.enableScroll = false
  }

  return baseOpts
})
</script>

<style lang="less" scoped>
// 排水管件统计区域样式 - 统一设施卡片样式
.pipe-fitting-section {
  background: linear-gradient(135deg, #ffffff 0%, #f8fbff 100%);
  border-radius: 20rpx;
  margin-top: 36rpx;
  overflow: hidden;
  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.08), 0 2rpx 8rpx rgba(0, 0, 0, 0.04);
  border: 1rpx solid #e8f4fd;
  position: relative;

  &::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    width: 6rpx;
    background: linear-gradient(135deg, #49a2de 0%, #667eea 100%);
  }

  // 卡片头部 - 统一设施卡片样式
  .card-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 20rpx 32rpx;
    background: linear-gradient(135deg, #f8fbff 0%, #ffffff 100%);
    border-bottom: 1rpx solid #e8f4fd;
    position: relative;
    overflow: hidden;
    cursor: pointer;

    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: -100%;
      width: 100%;
      height: 100%;
      background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
      animation: headerShine 3s infinite;
    }

    .header-left {
      display: flex;
      align-items: center;

      .facility-icon {
        width: 64rpx;
        height: 64rpx;
        // background: linear-gradient(135deg, #49a2de 0%, #667eea 100%);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        // box-shadow: 0 6rpx 16rpx rgba(73, 162, 222, 0.4);
        position: relative;
        transition: all 0.3s ease;
        margin-right: 20rpx;

        &::before {
          content: '';
          position: absolute;
          top: -2rpx;
          left: -2rpx;
          right: -2rpx;
          bottom: -2rpx;
          border-radius: 50%;
          background: linear-gradient(135deg, #49a2de, #667eea, #764ba2);
          z-index: -1;
          opacity: 0;
          transition: opacity 0.3s ease;
        }

        &:hover {
          transform: scale(1.05) rotate(5deg);
          box-shadow: 0 8rpx 20rpx rgba(73, 162, 222, 0.5);

          &::before {
            opacity: 1;
          }
        }

        .icon-img {
          // width: 32rpx;
          // height: 32rpx;
          // filter: brightness(0) invert(1);
          // transition: transform 0.3s ease;
        }

        &:hover .icon-img {
          transform: scale(1.1);
        }
      }

      .facility-info {
        .facility-title {
          font-size: 36rpx;
          font-weight: 700;
          color: #2c3e50;
          margin: 0 0 6rpx 0;
          line-height: 1.2;
          background: linear-gradient(135deg, #2c3e50, #49a2de);
          -webkit-background-clip: text;
          -webkit-text-fill-color: transparent;
          background-clip: text;
        }

        .facility-subtitle {
          font-size: 24rpx;
          color: #7f8c8d;
          margin: 0;
          line-height: 1.2;
          position: relative;

          &::before {
            content: '●';
            color: #49a2de;
            margin-right: 8rpx;
            font-size: 12rpx;
            animation: pulse 2s infinite;
          }
        }
      }
    }

    .header-right {
      display: flex;
      align-items: center;
      gap: 16rpx;

      .total-badge {
        background: linear-gradient(135deg, #e8f5e8 0%, #f0f9ff 100%);
        border: 2rpx solid #d4edda;
        border-radius: 16rpx;
        padding: 16rpx 20rpx;
        display: flex;
        align-items: baseline;
        gap: 6rpx;
        box-shadow: 0 4rpx 12rpx rgba(40, 167, 69, 0.2);
        position: relative;
        overflow: hidden;
        transition: all 0.3s ease;

        &::before {
          content: '';
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          height: 2rpx;
          background: linear-gradient(90deg, #28a745, #20c997, #28a745);
          animation: badgeGlow 2s infinite;
        }

        &:hover {
          transform: translateY(-2rpx);
          box-shadow: 0 6rpx 16rpx rgba(40, 167, 69, 0.3);
        }

        .total-number {
          font-size: 32rpx;
          font-weight: 700;
          color: #28a745;
          font-family: 'Courier New', monospace;
          text-shadow: 0 1rpx 2rpx rgba(40, 167, 69, 0.2);
        }

        .total-unit {
          font-size: 22rpx;
          font-weight: 600;
          color: #6c757d;
          background: rgba(40, 167, 69, 0.1);
          padding: 2rpx 8rpx;
          border-radius: 8rpx;
        }
      }

      // 折叠按钮样式 - 统一为 FacilityCard 样式
      .collapse-btn {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 56rpx;
        height: 56rpx;
        background: linear-gradient(135deg, rgba(73, 162, 222, 0.1) 0%, rgba(102, 126, 234, 0.1) 100%);
        border: 2rpx solid rgba(73, 162, 222, 0.2);
        border-radius: 50%;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        backdrop-filter: blur(10rpx);
        cursor: pointer;
        position: relative;
        overflow: hidden;

        &::before {
          content: '';
          position: absolute;
          inset: 0;
          background: linear-gradient(135deg, rgba(73, 162, 222, 0.2), rgba(102, 126, 234, 0.2));
          border-radius: 50%;
          opacity: 0;
          transition: opacity 0.3s ease;
        }

        &:hover {
          background: linear-gradient(135deg, rgba(73, 162, 222, 0.15) 0%, rgba(102, 126, 234, 0.15) 100%);
          border-color: rgba(73, 162, 222, 0.3);
          transform: scale(1.05);
          box-shadow: 0 4rpx 12rpx rgba(73, 162, 222, 0.2);

          &::before {
            opacity: 1;
          }
        }

        .collapse-icon {
          position: relative;
          width: 24rpx;
          height: 24rpx;

          .icon-line {
            position: absolute;
            width: 16rpx;
            height: 3rpx;
            background: linear-gradient(135deg, #49a2de 0%, #667eea 100%);
            border-radius: 2rpx;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            box-shadow: 0 1rpx 3rpx rgba(73, 162, 222, 0.3);

            &.line-1 {
              top: 50%;
              left: 50%;
              transform: translate(-50%, -50%) rotate(0deg);
              transform-origin: center;
            }

            &.line-2 {
              top: 50%;
              left: 50%;
              transform: translate(-50%, -50%) rotate(90deg);
              transform-origin: center;
            }
          }
        }

        &.collapsed {
          .collapse-icon {
            .icon-line {
              &.line-2 {
                transform: translate(-50%, -50%) rotate(0deg);
              }
            }
          }
        }
      }
    }
  }

  // 卡片内容包装器
  .card-content-wrapper {
    max-height: 2000rpx;
    overflow: hidden;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);

    &.wrapper-collapsed {
      max-height: 0;
    }

    .card-content {
      padding: 16rpx;
    }
  }
}

// 动画效果
@keyframes headerShine {
  0% {
    left: -100%;
  }
  100% {
    left: 100%;
  }
}

@keyframes pulse {
  0%,
  100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

@keyframes badgeGlow {
  0%,
  100% {
    opacity: 1;
  }
  50% {
    opacity: 0.7;
  }
}

// 图表区域样式 - 保持在卡片内容中
.chart-section {
  margin-bottom: 40rpx;

  &:last-child {
    margin-bottom: 0;
  }

  .chart-header {
    display: flex;
    align-items: center;
    margin-bottom: 20rpx;
    padding: 16rpx 20rpx;
    background: linear-gradient(135deg, #f8fbff 0%, #f0f8ff 100%);
    border-radius: 12rpx;
    border: 1rpx solid #e8f4fd;

    .chart-icon {
      font-size: 24rpx;
      margin-right: 12rpx;
    }

    .chart-title {
      font-size: 28rpx;
      font-weight: 600;
      color: #2c3e50;
      margin-right: 16rpx;
    }

    .chart-subtitle {
      font-size: 22rpx;
      color: #7f8c8d;
    }
  }

  .chart-icon {
    font-size: 24rpx;
    filter: drop-shadow(0 2rpx 4rpx rgba(0, 0, 0, 0.1));
  }

  .chart-title {
    font-size: 28rpx;
    font-weight: 600;
    color: #2c3e50;
    flex: 1;
  }

  .chart-subtitle {
    font-size: 22rpx;
    color: #7f8c8d;
    opacity: 0.8;
  }
}

.chart-wrapper {
  height: 400rpx;
  width: 640rpx;
  background: #fafbfc;
  border-radius: 12rpx;
  border: 1rpx solid #e8f4fd;
  overflow: hidden;
  position: relative;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4rpx;
    background: linear-gradient(90deg, #4facfe 0%, #00f2fe 100%);
    z-index: 1;
  }

  .chart-loading {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100%;
    color: #999;
    font-size: 24rpx;
    background: #fafbfc;

    text {
      animation: loadingPulse 1.5s ease-in-out infinite;
    }
  }

  .chart-fallback {
    padding: 20rpx;
    background: #fafbfc;
    height: 100%;
    overflow-y: auto;

    .fallback-title {
      font-size: 24rpx;
      font-weight: 600;
      color: #333;
      margin-bottom: 16rpx;
      text-align: center;
    }

    .data-list {
      .data-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 12rpx 16rpx;
        margin-bottom: 8rpx;
        background: white;
        border-radius: 8rpx;
        border: 1rpx solid #e8f4fd;

        .data-label {
          font-size: 22rpx;
          color: #666;
          flex: 1;
        }

        .data-value {
          font-size: 24rpx;
          font-weight: 600;
          color: #4facfe;
          font-family: 'Courier New', monospace;
        }
      }
    }
  }
}

.chart-wrapper {
  position: relative;
  min-height: 300rpx;
  background: white;
  border-radius: 12rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);

  .chart-loading {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 300rpx;
    color: #7f8c8d;
    font-size: 24rpx;
  }

  .chart-fallback {
    padding: 20rpx;

    .fallback-title {
      font-size: 24rpx;
      font-weight: 600;
      color: #2c3e50;
      margin-bottom: 16rpx;
      text-align: center;
    }

    .data-list {
      .data-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 12rpx 16rpx;
        border-bottom: 1rpx solid #f0f0f0;

        &:last-child {
          border-bottom: none;
        }

        .data-label {
          font-size: 24rpx;
          color: #2c3e50;
        }

        .data-value {
          font-size: 24rpx;
          font-weight: 600;
          color: #4facfe;
          font-family: 'Courier New', monospace;
        }
      }
    }
  }
}

// 动画效果
@keyframes headerShine {
  0% {
    left: -100%;
  }
  100% {
    left: 100%;
  }
}

@keyframes pulse {
  0%,
  100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

@keyframes badgeGlow {
  0%,
  100% {
    opacity: 1;
  }
  50% {
    opacity: 0.7;
  }
}
</style>
