<template>
  <div class="faceplate absolute-center overflow-hidden flex border-R12">
    <div class="W100" style="padding: 30px 30px 0 30px">
      <view class="fon-S24 fon-W600 color-333 f-xy-center">福田智能供排水决策支持系统</view>
      <div class="mar-T30" style="margin-top: 40px">
        <div class="mar-T16">
          <span class="fon-S16 fon-W600" style="color: #00000099">账号</span>
          <div class="f-y-center mar-T6" style="border-bottom: 1px solid #fff">
            <UserOutlined style="color: #999" />
            <a-input v-model:value="formState.username" ref="usernameRef" @pressEnter="passwordRef.focus()" :bordered="false" placeholder="请输入账号" />
            <CloseOutlined style="color: #666; margin-right: 5px" v-if="formState.username" @click="formState.username = ''" />
          </div>
        </div>
        <div class="mar-T16">
          <span class="fon-S16 fon-W600" style="color: #00000099">密码</span>
          <div class="f-y-center mar-T6" style="border-bottom: 1px solid #fff">
            <LockOutlined style="color: #999" />
            <a-input-password ref="passwordRef" class="input" @pressEnter="onFinish" v-model:value="formState.password" :bordered="false" placeholder="请输入密码" />
            <CloseOutlined style="color: #666; margin-right: 5px" v-if="formState.password" @click="formState.password = ''" />
          </div>
        </div>
      </div>
      <div class="checked f-y-center f-between" style="margin-top: 12px">
        <a-checkbox style="" v-model:checked="formState.remember"><span class="fon-S12 color-999">记住密码</span></a-checkbox>
        <span class="fon-S12 color-999" style="color: #62d1e1; cursor: pointer">忘记密码？</span>
      </div>

      <div>
        <a-button style="width: 100%; margin-top: 30px; background-color: #62d1e1" type="primary" shape="round" @click="onFinish">
          <span style="font-weight: 600">登录</span>
        </a-button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { reactive, onMounted, computed, ref } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { UserOutlined, LockOutlined, CloseOutlined } from '@ant-design/icons-vue'
import { message } from 'ant-design-vue'
import { login } from '@/services/modules/login'
import Cache from '@/utils/cache'
import { encrypt, decrypt } from '@/utils/encrypt'

const router = useRouter()
const route = useRoute()
const open = defineModel()
const passwordRef = ref(null)
const usernameRef = ref(null)
const res = Cache.get('formState')

let formData = { username: '', password: '', remember: true }
if (res) {
  const password = decrypt(res.password)
  res.password = password
  formData = res
}
const formState = reactive(formData)
const disabled = computed(() => !(formState.username && formState.password))

onMounted(() => usernameRef.value.focus())

const onFinish = async () => {
  if (disabled.value) return message.warning('账号或密码为空')
  open.value = true
  try {
    const { token, data } = await login(formState)
    const [userInfo] = JSON.parse(data)
    userInfo.token = token

    if (userInfo) {
      router.push('/')
      Cache.set('userInfo', userInfo)
    } else {
      message.error('账号或密码错误')
    }
    // 记住密码
    if (formState.remember) {
      const password = encrypt(formState.password)
      formState.password = password
      Cache.set('formState', formState)
    } else {
      Cache.delete('formState')
    }
  } catch (error) {
    console.log(error)
    message.error('账号或密码错误')
    open.value = false
  }
}
</script>

<style lang="less" scoped>
.faceplate {
  width: 400px;
  height: 400px;
  box-shadow: 4px 4px 40px rgba(0, 0, 0, 0.12), -4px -4px 40px rgba(97, 204, 224, 0.8);
  background: rgba(255, 255, 255, 0.7);
}

.checked {
  :deep(:where(.css-dev-only-do-not-override-mb15du).ant-checkbox-checked .ant-checkbox-inner) {
    background: #62d1e1;
    border-color: #38d6ee;
  }
}
</style>
