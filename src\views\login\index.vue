<template>
  <a-spin style="max-height: none" tip="登录中..." :indicator :spinning>
    <div class="login">
      <div class="all absolute overflow-hidden f-xy-center f-wrap z-index-10" id="loginPage">
        <LoginFaceplate v-model="spinning" />
      </div>
    </div>
  </a-spin>
</template>

<script setup>
import { onMounted, ref } from 'vue'
import LoginFaceplate from './components/LoginFaceplate.vue'
import particlesJSOptions from '@/assets/geojson/particlesJSOptions.json'

import { LoadingOutlined } from '@ant-design/icons-vue'
import { h } from 'vue'
const indicator = h(LoadingOutlined, { style: { fontSize: '24px' }, spin: true })
const spinning = ref(false)

onMounted(() => particlesJS('loginPage', particlesJSOptions))
</script>

<style lang="less" scoped>
.login {
  height: 1080px;
  background: url('@/assets/images/bg.gif') no-repeat center center;
  background-size: cover;
  top: 0;
  left: 0;
}
</style>
