<template>
  <div class="barEchar box-shadow border-R10"><Echert :option /></div>
</template>

<script setup>
import { computed, watch } from 'vue'
import Echert from '@/components/Echarts/index.vue'
const props = defineProps({ Proportion: Number })

const option = computed(() => ({
  title: { text: '修正占比', top: '4%', left: '3%' },
  color: ['#51c31a', '#FF5722'],
  legend: { data: ['已处理', '未处理'], top: '6%' },
  tooltip: { trigger: 'item' },
  xAxis: { data: [], type: 'value', show: false, axisTick: { show: false } },
  yAxis: { type: 'category', show: false, axisTick: { show: false } },
  series: [
    {
      type: 'bar',
      name: '已处理',
      data: [props.Proportion],
      stack: 'income',
      barWidth: 30,
      label: { show: true, position: 'inside', formatter: (obj) => obj.value + '%' }
    },
    {
      type: 'bar',
      name: '未处理',
      data: [100 - props.Proportion],
      stack: 'income',
      barWidth: 30,
      label: {
        show: true,
        position: 'inside',
        formatter: (obj) => obj.value + '%'
      }
    }
  ]
}))
</script>

<style lang="less" scoped>
.barEchar {
  width: 100%;
  height: 200px;
  margin-top: 12px;
  background-color: white;
}
</style>
