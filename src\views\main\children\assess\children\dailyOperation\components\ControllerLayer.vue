<template>
  <div class="wrapper">
    <div class="mar-B10 fon-S18 fon-W600">图层控制</div>
    <a-tree v-model:checkedKeys="checkedKeys" defaultExpandAll checkable @check="onCheck" :tree-data="treeData" />
  </div>
</template>

<script setup>
import { ref } from 'vue'
const emit = defineEmits(['change'])

const treeData = [
  {
    title: '基础底图',
    key: 'baseLayer',
    disabled: true,
    children: [
      {
        title: '片区图层',
        key: 'FTModificationWorks_fill',
        layerId: 'FTModificationWorks_fill',
        source: 'FT_ModificationWorks'
      },
      {
        title: '网格图层',
        key: 'tenGrid_fill',
        layerId: 'tenGrid_fill',
        source: 'tenGrid'
      },
      {
        title: '街道图层',
        key: 'FTstreet_fill',
        layerId: 'FTstreet_fill',
        source: 'FT_street'
      }
    ]
  },
  {
    title: '2014排水',
    key: '2014Layer',
    disabled: true,
    children: [
      {
        title: '2014污水管',
        key: 'Comparison2004WS_line',
        layerId: 'Comparison2004WS_line'
      },
      {
        title: '2014污水井',
        key: 'Comparison2004Manhole_symbol',
        layerId: 'Comparison2004Manhole_symbol'
      }
    ]
  },
  {
    title: '2022排水',
    key: '2022Layer',
    disabled: true,
    children: [
      {
        title: '2022排水管',
        key: 'tubeLayer_line',
        layerId: 'tubeLayer_line'
      },
      {
        title: '2022排水渠',
        key: 'canalLayer_line',
        layerId: 'canalLayer_line'
      },
      {
        title: '2022排水井',
        key: 'well_symbol',
        layerId: 'well_symbol'
      },
      {
        title: '2022雨水箅子',
        key: 'grate_symbol',
        layerId: 'grate_symbol'
      }
    ]
  }
]
const checkedKeys = ref([])

function onCheck(checkedKeys, info) {
  const { checked, node } = info
  emit('change', { checked, checkedKeys, nodes: treeData, ...node.dataRef })
}
</script>

<style lang="less" scoped>
.wrapper {
  margin: 24px;
  background-color: #fff;
  padding: 10px 18px;
  border-radius: 4px;
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.5);
  :deep(.ant-tree-treenode-disabled .ant-tree-node-content-wrapper) {
    color: rgba(0, 0, 0, 0.9);
    font-weight: 600;
    cursor: pointer;
  }
  :deep(.ant-tree-checkbox-disabled) {
    display: none;
  }
}
</style>
