<template>
  <div class="wrap">
    <div class="all back-white border-R12 box-shadow pad-12">
      <div class="mar-B10">
        <a-tooltip title="供排水设施类型筛选">
          <a-select v-if="selectOption.preserveFacility" v-model:value="selectOption.preserveFacilityValue" style="width: 140px" @change="handleChange('facility_Type')">
            <a-select-option value="全部">全部</a-select-option>
            <template v-for="item in selectOption.preserveFacility" :key="item.ID">
              <a-select-option :value="item.DictValue">{{ item.DictValue }}</a-select-option>
            </template>
          </a-select>
        </a-tooltip>
        <a-tooltip title="任务类型筛选">
          <a-select class="mar-L16" v-model:value="selectOption.preserveTaskValue" style="width: 140px" @change="handleChange('task_Type')">
            <a-select-option value="全部">全部</a-select-option>
            <template v-for="item in selectOption.preserveTask" :key="item.ID">
              <a-select-option :value="item.DictValue">{{ item.DictValue }}</a-select-option>
            </template>
          </a-select>
        </a-tooltip>
      </div>
      <barEchart :count />
      <PieEchart :count />
      <BarProportion :Proportion />
    </div>
  </div>
</template>

<script setup>
import { computed, watch, ref, reactive } from 'vue'
import barEchart from './BarEchart.vue'
import PieEchart from './PieEchart.vue'
import BarProportion from './BarProportion.vue'
import { storeToRefs } from 'pinia'
import useDailyOperation from '@/store/dailyOperation'
import { CommonApi } from '@/services/modules/common'

const { gisFaults } = storeToRefs(useDailyOperation())
const props = defineProps({ subdivide: { type: Boolean, default: () => false }, Map: Object })

watch(gisFaults, (val) => {
  selectOption.preserveFacilityValue = '全部'
  selectOption.preserveTaskValue = '全部'
  props.Map.setFilter('dot_symbol', null)
  console.log(val)
})

const count = computed(() => {
  const D = {}
  const list =
    gisFaults.value.filter((item) => {
      if (selectOption.preserveFacilityValue !== '全部' && selectOption.preserveTaskValue !== '全部') {
        return item.properties.facility_Type === selectOption.preserveFacilityValue && item.properties.task_Type === selectOption.preserveTaskValue
      }
      if (selectOption.preserveFacilityValue === '全部' && selectOption.preserveTaskValue === '全部') {
        return true
      }
      if (selectOption.preserveFacilityValue !== '全部') {
        return item.properties.facility_Type === selectOption.preserveFacilityValue
      }
      return item.properties.task_Type === selectOption.preserveTaskValue
    }) ?? []
  const key = !props.subdivide ? 'station' : 'inputstaff'
  for (let i = 0; i < list.length; i++) {
    const item = list[i].properties
    D[item[key]] ? ++D[item[key]] : (D[item[key]] = 1)
  }

  const val = Object.entries(D)
    .sort((a, b) => b[1] - a[1])
    .reverse()
  return { key: val.map((item) => item[0]), value: val.map((item) => item[1]) }
})

const selectOption = reactive({ preserveFacilityValue: '全部', preserveFacility: [], preserveTaskValue: '全部', preserveTask: [] })
;(async () => {
  const preserveFacility = await queryDictionaries('preserveFacility')
  selectOption.preserveFacility = preserveFacility
  const preserveTask = await queryDictionaries('preserveTask')
  selectOption.preserveTask = preserveTask
})()

const Proportion = computed(() => {
  const list = gisFaults.value ?? []
  const le = list.filter((i) => i.properties.state === '已处理').length
  const l = le ? Math.ceil((le / list.length) * 100) : 100
  return l
})

async function queryDictionaries(type) {
  const { data } = await CommonApi.queryDictionaries(type)
  return data
}

function handleChange(key) {
  const filter = []
  if (selectOption.preserveFacilityValue !== '全部') {
    filter.push(['==', ['get', 'facility_Type'], selectOption.preserveFacilityValue])
  }
  if (selectOption.preserveTaskValue !== '全部') {
    filter.push(['==', ['get', 'task_Type'], selectOption.preserveTaskValue])
  }
  props.Map.setFilter('dot_symbol', ['all', ...filter])
}
</script>

<style lang="less" scoped>
.wrap {
  position: absolute;
  top: 110px;
  bottom: 0;
  right: 0;
  width: 500px;
  padding: 24px 12px;
}
</style>
