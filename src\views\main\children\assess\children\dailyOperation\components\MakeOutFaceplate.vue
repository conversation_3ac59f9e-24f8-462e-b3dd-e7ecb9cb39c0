<template>
  <a-modal :ok-button-props="{ disabled: data?.state === '已处理' }" :maskClosable="false" v-model:open="open" centered width="700px" @ok="submit">
    <div class="fon-S20 fon-W600 f-xy-center mar-B16">GIS 修正信息</div>
    <div class="pad-X12">
      <div class="row f-y-center">
        <div class="item f-1 mar-Y4 f-y-center">
          <div class="fon-S14 fon-W600 color-666">供排水设施类型：</div>
          <div class="fon-S14 fon-W600">{{ data.facility_Type }}</div>
        </div>
        <div class="item f-1 mar-Y4 f-y-center">
          <div class="fon-S14 fon-W600 color-666">任务类型：</div>
          <div class="fon-S14 fon-W600">{{ data.task_Type }}</div>
        </div>
        <div class="item f-1 mar-Y4 f-y-center">
          <div class="fon-S14 fon-W600 color-666">X：</div>
          <div class="fon-S14 fon-W600">{{ data.x }}</div>
        </div>
      </div>
      <div class="row f-y-center">
        <div class="item f-1 mar-Y4 f-y-center">
          <div class="fon-S14 fon-W600 color-666">上报组织：</div>
          <div class="fon-S14 fon-W600">{{ data.station }}</div>
        </div>
        <div class="item f-1 mar-Y4 f-y-center">
          <div class="fon-S14 fon-W600 color-666">所属道路：</div>
          <div class="fon-S14 fon-W600">{{ data.subdistric }}</div>
        </div>
        <div class="item f-1 mar-Y4 f-y-center">
          <div class="fon-S14 fon-W600 color-666">Y：</div>
          <div class="fon-S14 fon-W600">{{ data.y }}</div>
        </div>
      </div>
      <div class="row f-y-center">
        <div class="item f-1 mar-Y4 f-y-center">
          <div class="fon-S14 fon-W600 color-666">上报人：</div>
          <div class="fon-S14 fon-W600">{{ data.inputstaff }}</div>
        </div>
        <div class="item f-1 mar-Y4 f-y-center">
          <div class="fon-S14 fon-W600 color-666">上报时间：</div>
          <div class="fon-S14 fon-W600">{{ data.inputdate.slice(0, 16).replace('T', ' ') }}</div>
        </div>
      </div>
      <div class="row f-y-center" v-if="data.path4.length && data.inputstaff !== data.veruserkey">
        <div class="item f-1 mar-Y4 f-y-center">
          <div class="fon-S14 fon-W600 color-666">修改人：</div>
          <div class="fon-S14 fon-W600">{{ data.veruserkey }}</div>
        </div>
        <div class="item f-1 mar-Y4 f-y-center">
          <div class="fon-S14 fon-W600 color-666">修改时间：</div>
          <div class="fon-S14 fon-W600">{{ data.verdate.slice(0, 16).replace('T', ' ') }}</div>
        </div>
      </div>
    </div>

    <div class="flex">
      <div class="pad-X12 f-1 mar-B8" v-if="data.path1.length">
        <div class="fon-S14 fon-W600 color-666 mar-B6">近景照片：</div>
        <div class="flex">
          <template v-for="item in handlerImgList(data.path1)" :key="item">
            <a-image class="mar-X4" style="width: 100px; height: 100px; border: 1px solid #ccc" v-if="item" :src="item" />
          </template>
        </div>
      </div>
      <div class="pad-X12 f-1 mar-B8" v-if="data.path2.length">
        <div class="fon-S14 fon-W600 color-666 mar-B6">远景照片：</div>
        <div class="flex">
          <template v-for="item in handlerImgList(data.path2)" :key="item">
            <a-image class="mar-X4" style="width: 100px; height: 100px; border: 1px solid #ccc" v-if="item" :src="item" />
          </template>
        </div>
      </div>
    </div>

    <div class="pad-X12 mar-B8">
      <div class="fon-S14 fon-W600 color-666 mar-B6">修改描述:</div>
      <a-textarea disabled v-model:value="data.remark1" />
    </div>

    <div class="flex">
      <div class="pad-X12 f-1 mar-B8" v-if="data.path3.length">
        <div class="fon-S14 fon-W600 color-666 mar-B6">问题GIS图片:</div>
        <div class="flex">
          <template v-for="item in handlerImgList(data.path3)" :key="item">
            <a-image class="mar-X4" style="width: 100px; height: 100px; border: 1px solid #ccc" v-if="item" :src="item" />
          </template>
        </div>
      </div>
      <div class="pad-X12 f-1" v-if="data.state != '已处理'">
        <div class="fon-S14 fon-W600 color-666 mar-B6">上传修改截图:</div>
        <a-upload v-model:file-list="fileList" :headers :action="`https://www.szwgft.cn:5000/api/UpWrite/upload?Zone_Code=OperationFeedbacks/${data.zone_Code}`" list-type="picture-card" @preview="handlePreview">
          <div v-if="fileList.length < 2">
            <plus-outlined />
            <div style="margin-top: 8px">Upload</div>
          </div>
        </a-upload>
        <a-modal :open="previewVisible" :title="previewTitle" :footer="null" @cancel="handleCancel">
          <img alt="example" style="width: 100%" :src="previewImage" />
        </a-modal>
      </div>

      <div class="pad-X12 f-1 mar-B8" v-else>
        <div class="fon-S14 fon-W600 color-666 mar-B6">修正后图片:</div>
        <div class="flex">
          <template v-for="item in handlerImgList(data.path4)" :key="item">
            <a-image class="mar-X4" style="width: 100px; height: 100px; border: 1px solid #ccc" v-if="item" :src="item" />
          </template>
        </div>
      </div>
    </div>

    <div class="pad-X12 mar-B8">
      <div class="fon-S14 fon-W600 color-666 mar-B6">备注:</div>
      <a-textarea v-model:value="data.remark2" :disabled="data.state === '已处理'" placeholder="请输入备注" />
    </div>
  </a-modal>
</template>

<script setup>
import { alterRecordApi } from '@/services/modules/home'
import { message } from 'ant-design-vue'
import { ref } from 'vue'
import Cache from '@/utils/cache'
import { getCurrentDateTime } from '@/utils'

import useDailyOperation from '@/store/dailyOperation'

const resourceBaseUrl = import.meta.env.VITE_WEB_RESOURCE_BASE_URL

const { getGisFaults } = useDailyOperation()

const open = defineModel()
const props = defineProps({ data: Object })

function getBase64(file) {
  return new Promise((resolve, reject) => {
    const reader = new FileReader()
    reader.readAsDataURL(file)
    reader.onload = () => resolve(reader.result)
    reader.onerror = (error) => reject(error)
  })
}

const previewVisible = ref(false)
const previewImage = ref('')
const previewTitle = ref('')
const fileList = ref([])
const handleCancel = () => {
  previewVisible.value = false
  previewTitle.value = ''
}

const headers = { Authorization: `Bearer ${Cache.get('userInfo')?.token}` }
const handlePreview = async (file) => {
  if (!file.url && !file.preview) {
    file.preview = await getBase64(file.originFileObj)
  }
  previewImage.value = file.url || file.preview
  previewVisible.value = true
  previewTitle.value = file.name || file.url.substring(file.url.lastIndexOf('/') + 1)
}

const submit = async () => {
  const userInfo = Cache.get('userInfo')
  if (userInfo?.station !== '运营中心') return message.warning('无权限操作')
  const images = fileList.value.map((item) => item.response.data).join(',')
  if (images.length === 0 || props.data.remark2.length === 0) {
    return message.warning('请填写完整信息')
  }

  props.data.veruserkey = userInfo.name
  props.data.verdate = getCurrentDateTime``

  props.data.path4 = images
  await alterRecordApi(props.data)
  message.success('提交成功')
  getGisFaults()
  open.value = false
}

// 图片分割
function handlerImgList(images) {
  return images.split(',').map((item) => resourceBaseUrl + '/' + item)
}
</script>

<style lang="less" scoped>
.image_list {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr;
  grid-gap: 10px;
}
</style>
