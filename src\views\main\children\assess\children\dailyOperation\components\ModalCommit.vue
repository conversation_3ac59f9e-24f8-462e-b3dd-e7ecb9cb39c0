<template>
  <a-modal v-if="open" :maskClosable="false" title="GIS修正" v-model:open="open" centered width="600px" @ok="submit">
    <div class="flex mar-Y6">
      <div class="label">供排水设施类型：</div>
      <a-radio-group v-model:value="materials.facility_Type">
        <a-radio value="供水">供水</a-radio>
        <a-radio value="雨水">雨水</a-radio>
        <a-radio value="污水">污水</a-radio>
      </a-radio-group>
    </div>
    <div class="flex mar-B6">
      <div class="label" style="margin-left: 42px">任务类型：</div>
      <a-radio-group v-model:value="materials.task_Type">
        <a-radio value="清疏">清疏</a-radio>
        <a-radio value="溯源">溯源</a-radio>
        <a-radio value="维抢修">维抢修</a-radio>
        <a-radio value="DN800专项">DN800专项</a-radio>
      </a-radio-group>
    </div>
    <div class="flex mar-B6">
      <div class="f-y-center mar-R10"><span class="label">上报组织：</span> <a-input disabled v-model:value="materials.station" style="width: 190px" /></div>
      <div class="f-y-center"><span class="label">所属街道：</span> <a-input v-model:value="materials.subdistric" style="width: 190px" /></div>
    </div>
    <div class="flex mar-B6">
      <div class="f-y-center mar-R30">
        <div class="label" style="margin-left: 48px">X：</div>
        <a-input disabled v-model:value="materials.x" style="width: 190px" />
      </div>
      <div class="f-y-center">
        <div class="label" style="margin-left: 28px">Y：</div>
        <a-input disabled v-model:value="materials.y" style="width: 190px" />
      </div>
    </div>

    <div class="flex">
      <div class="f-1">
        <div class="mar-B6 label">近景照片:</div>
        <UploadImage :url @update="(url) => (materials.path1 = url.join(','))" />
      </div>
      <div class="f-1">
        <div class="mar-B6 label">远景照片:</div>
        <UploadImage :url @update="(url) => (materials.path2 = url.join(','))" />
      </div>
    </div>
    <div class="mar-B6">
      <div class="mar-B6 label">修改描述：</div>
      <a-textarea v-model:value="materials.remark1" placeholder="请输入备注" />
    </div>
    <div>
      <div class="mar-B6 label">GIS截图（问题处）:</div>
      <UploadImage :url @update="(url) => (materials.path3 = url.join(','))" />
    </div>
  </a-modal>
</template>

<script setup>
import { watch, reactive, computed } from 'vue'
import { getCurrentDateTime } from '@/utils'
import Cache from '@/utils/cache'
import { getZoneData } from '@/services/modules/map'
import { message } from 'ant-design-vue'
import { createRecordApi } from '@/services/modules/home'
import useDailyOperation from '@/store/dailyOperation'

import UploadImage from '@/components/UploadImage/index.vue'

const open = defineModel()
const props = defineProps({ data: Object })
const userInfo = Cache.get('userInfo')
const { getGisFaults } = useDailyOperation()

const url = computed(() => `https://www.szwgft.cn:5000/api/UpWrite/upload?Zone_Code=OperationFeedbacks/${props?.data?.Zone_Code ?? 'images'}`) // 上传地址
const materials = reactive({
  facility_Type: '供水', //供排水设施类型
  zone_Code: null, //小区code
  station: null, //上报组织
  subdistric: null, //所属街道
  task_Type: '清疏', //任务类型
  path1: '', //近景照片
  path2: '', //远景照片
  path3: '', //错误截图
  path4: '',
  remark1: null, //修改描述
  remark2: '',
  x: null,
  y: null,
  inputstaff: null, //上报人
  inputdate: null, //上报时间
  veruserkey: null, //最后修改人
  verdate: null //最后修改时间
})

watch(
  () => props.data,
  async (newVal) => {
    materials.station = userInfo.station
    materials.x = newVal.x + ''
    materials.y = newVal.y + ''
    if (newVal.Zone_Code == '超出边界') {
      materials.zone_Code = '超出边界'
      materials.subdistric = '超出边界'
    } else {
      const { data } = await getZoneData(newVal.Zone_Code)
      const [res] = JSON.parse(data)
      materials.subdistric = res.Subdistrict
      materials.zone_Code = res.Zone_Code
    }
  }
)

// 提交
async function submit() {
  try {
    const time = getCurrentDateTime()
    const name = Cache.get('userInfo').name
    materials.inputdate = time
    materials.verdate = time
    materials.inputstaff = name
    materials.veruserkey = name
    for (const key in materials) {
      if (!['path1', 'path2', 'remark2', 'path4'].includes(key) && !materials[key]) {
        return message.warning('请填写完整信息')
      }
    }

    await createRecordApi(materials)
    Object.keys(materials).forEach((key) => (materials[key] = ''))
    message.success('提交成功')
    open.value = false
    getGisFaults()
  } catch (error) {
    message.error('提交失败')
    console.error(error)
  }
}
</script>

<style lang="less" scoped>
.label {
  font-size: 14px;
  color: #666;
  font-weight: 600;
}
</style>

<style lang="less">
.ant-modal .ant-modal-title {
  text-align: center;
}
</style>
