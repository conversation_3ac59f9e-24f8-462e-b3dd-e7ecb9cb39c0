<template>
  <div class="PieEchar box-shadow border-R10"><Echert :option /></div>
</template>

<script setup>
import { computed, watch } from 'vue'
import Echert from '@/components/Echarts/index.vue'
const props = defineProps({ count: Object })

const option = computed(() => ({
  title: { text: '上报占比', top: '4%', left: '3%' },
  tooltip: { trigger: 'item' },
  legend: { type: 'scroll', orient: 'vertical', right: 10, top: 20, bottom: 20, itemWidth: 20, itemHeight: 20 },
  series: [
    {
      name: '占比',
      type: 'pie',
      radius: ['30%', '70%'],
      avoidLabelOverlap: false,
      padAngle: 5,
      itemStyle: {
        borderRadius: 10
      },
      label: {
        show: true,
        position: 'inner',
        formatter: '{d}%',
        distance: 0.5, //标识距离中心点的距离
        align: 'center',
        baseline: 'middle',
        fontSize: 12,
        fontWeight: 'bolder'
      },

      labelLine: { show: false },
      data: props.count.key.map((item, index) => ({ name: item, value: props.count.value[index] }))
    }
  ]
}))
</script>

<style lang="less" scoped>
.PieEchar {
  width: 100%;
  height: 260px;
  margin-top: 12px;
  background-color: white;
}
</style>
