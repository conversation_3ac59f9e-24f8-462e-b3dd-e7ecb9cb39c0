<template>
  <!-- 日常维护 -->
  <!-- <MapOperate> </MapOperate> -->
  <div class="all relative">
    <MapGlS ref="mapComponentRef" :options :legend @choose="choose" @load="mapLoad">
      <template #rightTop>
        <div class="mar-30">
          <div class="flex mar-B10">
            <div class="icon_box box-shadow f-xy-center f-column">
              <a-tooltip title="导出文件"><VerticalAlignBottomOutlined style="font-size: 26px; color: #333" @click="exportFile" /></a-tooltip>
            </div>
            <div class="icon_box box-shadow f-xy-center f-column">
              <a-tooltip title="新增错误"> <PlusOutlined style="font-size: 26px; color: #333" @click="handlerMove" /> </a-tooltip>
            </div>
            <div class="icon_box box-shadow f-xy-center f-column">
              <a-tooltip title="刷新展示全部"><RedoOutlined style="font-size: 26px; color: #333" @click="showAllMarker" /></a-tooltip>
            </div>
            <MapInputSearch @handlerZoneClick="handlerClick" />
          </div>
          <div class="flex f-between">
            <div></div>
            <div class="flex">
              <a-select size="large" v-model:value="selectValue" style="width: 140px" @change="handleChange">
                <a-select-option value="全部">全部</a-select-option>
                <a-select-option value="福中水务所">福中水务所</a-select-option>
                <a-select-option value="梅林水务所">梅林水务所</a-select-option>
                <a-select-option value="运营中心">运营中心</a-select-option>
                <a-select-option value="香蜜水务所">香蜜水务所</a-select-option>
                <a-select-option value="福东水务所">福东水务所</a-select-option>
              </a-select>
              <a-range-picker class="mar-L12" v-model:value="times" style="width: 300px" @change="handleChange" valueFormat="YYYY-MM-DD" size="large" />
            </div>
          </div>
        </div>
      </template>
      <template #leftBottom>
        <div style="margin-bottom: 160px"><ControllerLayer @change="changeLayer" /></div>
      </template>
    </MapGlS>

    <!-- 审核弹窗 -->
    <MakeOutFaceplate v-model="lockerD.open" :data="lockerD.data" />

    <!-- 上报弹窗 -->
    <ModalCommit v-model="commitD.open" :data="commitD.data" />

    <!-- 数据统计概况 -->
    <GeneralSituation :Map :subdivide="selectValue != '全部'" />
  </div>
</template>

<script setup>
import { ref, reactive, computed, watch } from 'vue'
import MakeOutFaceplate from './components/MakeOutFaceplate.vue'
import ModalCommit from './components/ModalCommit.vue'
import GeneralSituation from './components/GeneralSituation.vue'
import MapOperate from '@/components/MapOperate/index.vue'

import MapGlS from '@/components/MapGlS2/index.vue'
import { flyTo } from '@/components/MapGlS2/utils'
import { FT_ZONE, FT_ModificationWorks, FT_street, tenGrid } from '@/ownedByAllMapConfig'
import { PlusOutlined, VerticalAlignBottomOutlined, ProfileOutlined, RedoOutlined } from '@ant-design/icons-vue'
import mapboxgl from 'mapbox-gl'

import useDailyOperation from '@/store/dailyOperation'
import { storeToRefs } from 'pinia'
import icon from '@/assets/images/031.png'
import icon2 from '@/assets/images/011.png'

import { exportPreserve } from '@/services/modules/map'
import { message } from 'ant-design-vue'
import ControllerLayer from './components/ControllerLayer.vue'
import { transitionComponent } from '@/utils/transitionDom.js'
import Popup2022 from './components/Popup2022.vue'

import WSicon from '@/assets/images/WS.png'
import YSicon from '@/assets/images/YS.png'
import BZicon from '@/assets/images/BZ.png'

const { getGisFaults, getScreenRecord } = useDailyOperation()
const { gisFaults } = storeToRefs(useDailyOperation())

const mapComponentRef = ref(null)
const lockerD = reactive({ open: false, data: null })
const times = ref([])

const options = {
  sources: {
    // 福田基础范围划分图层
    // FT_BasicsDivide: {
    //   type: 'vector',
    //   scheme: 'tms',
    //   tiles: ['https://www.szwgft.cn/mapServer/geoserver/gwc/service/tms/1.0.0/Code%3AFT_BasicsDivide@EPSG%3A3857@pbf/{z}/{x}/{y}.pbf'],
    //   layers: {
    //     FTModificationWorks_fill: {
    //       options: {
    //         'source-layer': 'FT_ModificationWorks',
    //         layout: { visibility: 'none' },
    //         paint: {
    //           'fill-opacity': 0.35,
    //           'fill-outline-color': '#000000',
    //           'fill-color': ['match', ['get', 'AreaName'], ...transitionArr(zoneColor, 'pink')]
    //         }
    //       }
    //     },
    //     FTModificationWorks_line: {
    //       options: { 'source-layer': 'FT_ModificationWorks', paint: { 'line-width': 1, 'line-color': '#000' }, layout: { visibility: 'none' } }
    //     },
    //     FTModificationWorks_symbol: {
    //       options: {
    //         'source-layer': 'FT_ModificationWorks',
    //         layout: { 'text-field': ['get', 'AreaName'], 'text-anchor': 'center', 'text-size': 22, visibility: 'none' },
    //         paint: { 'text-color': '#c500ff', 'text-halo-color': 'white', 'text-halo-width': 1 }
    //       }
    //     },
    //     tenGrid_fill: {
    //       options: {
    //         'source-layer': 'tenGrid',
    //         layout: { visibility: 'none' },
    //         paint: {
    //           'fill-opacity': 0.5,

    //           'fill-outline-color': '#000000',
    //           'fill-color': ['match', ['get', 'name'], ...transitionArr(tenGridColor, 'pink')]
    //         }
    //       }
    //     },
    //     tenGrid_line: {
    //       options: { 'source-layer': 'tenGrid', layout: { visibility: 'none' }, paint: { 'line-width': 1, 'line-color': '#000' } }
    //     },
    //     tenGrid_symbol: {
    //       options: {
    //         'source-layer': 'tenGrid',
    //         layout: { 'text-field': ['get', 'name'], 'text-anchor': 'center', 'text-size': 22, visibility: 'none' },
    //         paint: { 'text-color': '#c500ff', 'text-halo-color': 'white', 'text-halo-width': 1 }
    //       }
    //     },
    //     FTstreet_fill: {
    //       options: {
    //         'source-layer': 'FT_street',
    //         paint: {
    //           'fill-opacity': 0.5,
    //           'fill-outline-color': '#000000',
    //           'fill-color': ['match', ['get', 'name'], ...transitionArr(streetColor, 'pink')]
    //         },
    //         layout: { visibility: 'none' }
    //       }
    //     },
    //     FTstreet_line: {
    //       options: { 'source-layer': 'FT_street', paint: { 'line-width': 1, 'line-color': '#000' }, layout: { visibility: 'none' } }
    //     },
    //     FTstreet_symbol: {
    //       options: {
    //         'source-layer': 'FT_street',
    //         layout: { 'text-field': ['get', 'name'], 'text-anchor': 'center', 'text-size': 22, visibility: 'none' },
    //         paint: { 'text-color': '#c500ff', 'text-halo-color': 'white', 'text-halo-width': 1 }
    //       }
    //     }
    //   }
    // },

    // 片区图层
    FT_ModificationWorks,
    // 网格图层
    tenGrid,
    // 街道图层
    FT_street,
    Polyline_FT: {
      type: 'vector',
      scheme: 'tms',
      tiles: ['https://www.szwgft.cn/mapServer/geoserver/gwc/service/tms/1.0.0/Code%3APolyline_FT@EPSG%3A3857@pbf/{z}/{x}/{y}.pbf'],
      layers: {
        PolylineFT_line: {
          options: { minzoom: 16.5, 'source-layer': 'Polyline_FT', paint: { 'line-width': 2, 'line-color': '#666' } }
        }
      }
    },
    FT_ZONE,
    Comparison: {
      type: 'vector',
      scheme: 'tms',
      tiles: ['https://www.szwgft.cn/mapServer/geoserver/gwc/service/tms/1.0.0/Code%3AFT_2022_drain_away_water@EPSG%3A3857@pbf/{z}/{x}/{y}.pbf'],
      layers: {
        // 2004污水管
        Comparison2004WS_line: {
          options: {
            minzoom: 15,
            'source-layer': 'Pipe_2004',
            layout: { visibility: 'none' },
            paint: { 'line-width': 4, 'line-opacity': 0.8, 'line-color': '#1ba035' }
          }
        },
        // 2004污水井
        Comparison2004Manhole_symbol: {
          images: { 雨水井: YSicon, 污水井: WSicon, 箅子: BZicon },
          options: {
            minzoom: 15,
            'source-layer': 'Manhole_2004',
            layout: {
              visibility: 'none',
              'icon-image': '污水井',
              'icon-size': 0.015,
              'icon-rotate': -90,
              'icon-allow-overlap': true
            },
            paint: {
              'icon-opacity': 0.6 // 正确使用 paint 属性
            }
          }
        },
        // 2022雨污管
        tubeLayer_line: {
          options: {
            minzoom: 15,
            'source-layer': 'PS_PIPE',
            layout: { visibility: 'none' },
            paint: {
              'line-color': ['case', ['==', ['get', 'SUBTYPE'], '雨水管'], '#0080ff', '#ff0000'],
              'line-width': 6,
              'line-opacity': 1
            }
          }
        },
        // 2022排水渠
        canalLayer_line: {
          options: {
            minzoom: 15,
            'source-layer': 'PS_CONDUIT',
            layout: { visibility: 'none' },
            paint: {
              'line-color': ['case', ['==', ['get', 'SUBTYPE'], '雨水渠'], '#0080ff', '#ff0000'],
              'line-width': 6,
              'line-opacity': 1
            }
          }
        },
        // 2022雨污井
        well_symbol: {
          options: {
            minzoom: 15,
            'source-layer': 'WELL',
            layout: { visibility: 'none', 'icon-image': ['match', ['get', 'SUBTYPE'], ['YS'], '雨水井', '污水井'], 'icon-rotate': -90, 'icon-size': 0.015, 'icon-allow-overlap': true }
          }
        },
        // 2022雨水箅子
        grate_symbol: {
          options: {
            minzoom: 15,
            'source-layer': 'RAINSTR',
            layout: { visibility: 'none', 'icon-image': '箅子', 'icon-rotate': -90, 'icon-size': 0.015, 'icon-allow-overlap': true }
          }
        }
      }
    },
    dot: {
      type: 'geojson',
      data: { type: 'FeatureCollection', features: [] },
      layers: {
        dot_symbol: {
          images: { 已处理: icon, 未处理: icon2 },
          events: {
            click: (e, feature) => {
              const { properties } = feature
              lockerD.open = true
              lockerD.data = properties
            }
          },
          options: {
            layout: {
              'icon-image': ['get', 'state'],
              'icon-size': 0.12,
              'icon-offset': [0, -100],
              'icon-allow-overlap': true
            }
          }
        }
      }
    }
  }
}

let Map = null
const selectValue = ref('全部')

const legend = computed(() => {
  return [
    { url: icon, value: gisFaults.value.length, label: '全部' },
    { url: icon, value: gisFaults.value.filter((item) => item.properties.state === '已处理').length, label: '已处理' },
    { url: icon2, value: gisFaults.value.filter((item) => item.properties.state === '未处理').length, label: '未处理' }
  ]
})

watch(gisFaults, (newVal) => mapComponentRef.value.updatedData('dot', newVal))

// 跳转区块与区块高亮
function handlerClick(e) {
  flyTo(Map, e.Center_Point.split(','), 16)
  Map.setFilter('zone2_line', ['match', ['get', 'Zone_Name'], e.Zone_Name, true, false])
}

// 地图渲染完成回调
function mapLoad(map) {
  Map = map
  getGisFaults()

  // 为2022年排水图层添加弹窗事件
  // map.on('mouseenter', ['tubeLayer_line', 'canalLayer_line', 'well_symbol', 'grate_symbol'], layerMouseEnter)
  // map.on('mouseleave', ['tubeLayer_line', 'canalLayer_line', 'well_symbol', 'grate_symbol'], layerMouseLeave)
}

const Marker = ref(null)
const commitD = reactive({ open: false, data: null })
// 中心点添加标记
function handlerMove() {
  // 监听地图拖动事件
  Map.on('move', handlerMoveEvent)
  // 获取中心点坐标
  let { lng, lat } = Map.getCenter()
  // 添加标记
  if (Marker.value) Marker.value.remove()
  Marker.value = new mapboxgl.Marker().setLngLat([lng, lat]).addTo(Map)
  Marker.value.getElement().addEventListener('click', handlerMarkerClick) // 标记添加点击事件

  // 地图拖动事件处理函数
  function handlerMoveEvent() {
    let { lng, lat } = Map.getCenter() // 获取新的中心点坐标
    Marker.value.setLngLat([lng, lat]) // 跟新标记位置
  }
  // 标记点击事件处理函数
  function handlerMarkerClick(e) {
    Marker.value.getElement().removeEventListener('click', handlerMarkerClick)
    const config = { layers: ['zone_fill'] }
    const feature = Map.queryRenderedFeatures([Marker.value._pos.x, Marker.value._pos.y], config)[0]
    const { Zone_Name, Zone_Code } = feature?.properties ?? { Zone_Name: '超出边界', Zone_Code: '超出边界' }
    const { lng, lat } = Marker.value._lngLat

    commitD.open = true
    commitD.data = { Zone_Name, Zone_Code, x: lng, y: lat }
    Map.off('move', handlerMoveEvent)
    e.stopPropagation()
    Marker.value.remove()
  }
}

// 时间筛选
async function handleChange() {
  const station = selectValue.value == '全部' ? '' : selectValue.value
  const startDate = times.value && times.value[0] ? new Date(times.value[0]) : null
  const endDate = times.value && times.value[1] ? new Date(new Date(times.value[1] + ' 23:59:59').getTime() + 1000 * 60 * 60 * 8) : null
  getScreenRecord({ station, startDate, endDate })
  Map.setFilter('dot_symbol', null)
}

// 文件导出
async function exportFile() {
  const hide = message.loading('正在导出', 0)
  const { data } = await exportPreserve({ list: gisFaults.value.map((i) => i.properties) })
  const a = document.createElement('a')
  a.href = data.https
  document.body.appendChild(a)
  a.click()
  document.body.removeChild(a)
  hide()
}

// 展示全部标记
async function showAllMarker() {
  await getGisFaults()
  selectValue.value = '全部'
  Map.setFilter('dot_symbol', null)
}

function choose({ label }) {
  if (label === '全部') return Map.setFilter('dot_symbol', null)
  Map.setFilter('dot_symbol', ['match', ['get', 'state'], label, true, false])
}

function changeLayer(val) {
  const { checked, data, key, nodes, checkedKeys } = val
  if (data) {
    let condition = []
    const children = nodes.find(({ key }) => key === data.primary).children
    children.forEach(({ key, data }) => (checkedKeys.includes(key) ? condition.push(['==', ['get', data.key], data.value]) : null))
    if (condition.length > 0) {
      Map.setFilter(data.primary, ['any', ...condition])
      Map.setLayoutProperty(data.primary, 'visibility', 'visible')
    } else {
      Map.setLayoutProperty(data.primary, 'visibility', 'none')
    }
    setTimeout(() => Map.fire('moveend'), 50)
    return
  }
  if (checked) {
    if (val.source) {
      const layers = Map.getStyle().layers
      layers.forEach((layer) => {
        if (layer.source === val.source) {
          Map.setLayoutProperty(layer.id, 'visibility', 'visible')
        }
      })
    }

    Map.setFilter(key, null)
    Map.setLayoutProperty(key, 'visibility', 'visible')
  } else {
    Map.setLayoutProperty(key, 'visibility', 'none')
    if (val.source) {
      const layers = Map.getStyle().layers
      layers.forEach((layer) => {
        if (layer.source === val.source) {
          Map.setLayoutProperty(layer.id, 'visibility', 'none')
        }
      })
    }
  }
}

// const Popup2022Component = transitionComponent(Popup2022)

// // 鼠标移入回调
// function layerMouseEnter(e) {
//   // 查询当前鼠标悬停的要素
//   const [feature] = Map.queryRenderedFeatures(e.point, { layers: ['tubeLayer_line', 'canalLayer_line', 'well_symbol', 'grate_symbol'] })
//   const data = feature.properties
//   data.layerId = feature.layer.id
//   Map.popup2022 = new mapboxgl.Popup({ maxWidth: 'none', offset: [0, -20] }).setLngLat([e.lngLat.lng, e.lngLat.lat]).setDOMContent(Popup2022Component({ data })).addTo(Map)
//   console.log(data)
// }
// // 鼠标移出回调
// function layerMouseLeave(e) {
//   Map.popup2022.remove()
// }
</script>

<style lang="less" scoped>
.icon_box {
  right: 30px;
  top: 160px;
  width: 40px;
  height: 40px;
  padding: 5px 3px;
  background-color: white;
  margin-right: 12px;
  border-radius: 6px;
  cursor: pointer;
}
</style>
