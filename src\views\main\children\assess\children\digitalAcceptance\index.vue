<template>
  <div class="all">
    <MapGlS :options @load="mapLoad">
      <template #rightTop>
        <div class="mar-30 flex">
          <div class="icon_box box-shadow f-xy-center f-column">
            <a-tooltip title="俯视视角"><CompassOutlined style="font-size: 26px" @click="restore" /></a-tooltip>
          </div>
          <MapInputSearch @handlerZoneClick="handlerClick" @focus="restore" />
        </div>
      </template>
    </MapGlS>
  </div>
</template>

<script setup>
import MapGlS from '@/components/MapGlS2/index.vue'
import MapInputSearch from '@/components/MapInputSearch/index.vue'
import { CompassOutlined } from '@ant-design/icons-vue'

const options = {
  sources: {
    // FT_ZONE1: {
    //   type: 'vector',
    //   scheme: 'tms',
    //   tiles: ['https://www.szwgft.cn/mapServer/geoserver/gwc/service/tms/1.0.0/Code%3AFT_ZONE@EPSG%3A3857@pbf/{z}/{x}/{y}.pbf'],
    //   layers: {
    //     zone_fill: {
    //       options: {
    //         'source-layer': 'FT_ZONE',
    //         paint: {
    //           'fill-opacity': 0.35,
    //           'fill-color': ['match', ['get', 'ManagerNam'], '福中水务所', '#ee0000', '福东水务所', '#bdfb9b', '梅林水务所', '#1677ff', '香蜜水务所', '#fa9600', '#999999']
    //         }
    //       }
    //     },
    //     zone_line: {
    //       options: { minzoom: 15, 'source-layer': 'FT_ZONE', paint: { 'line-width': 1, 'line-color': '#000' } }
    //     },
    //     zone_symbol: {
    //       options: { 'source-layer': 'FT_ZONE', minzoom: 16, layout: { 'text-field': ['get', 'Zone_Name'], 'text-size': 12 }, paint: { 'text-color': 'black', 'text-halo-color': 'white', 'text-halo-width': 1 } }
    //     }
    //   }
    // },
    // FT_ZONE: {
    //   type: 'vector',
    //   scheme: 'tms',
    //   tiles: ['https://www.szwgft.cn/mapServer/geoserver/gwc/service/tms/1.0.0/Code%3Afutianjianzhu@EPSG%3A3857@pbf/{z}/{x}/{y}.pbf'],
    //   layers: {
    //     'zone_fill-extrusion': {
    //       above: 'zone_symbol',
    //       events: { click: architectureLayerClick },
    //       options: {
    //         'source-layer': 'futianjianzhu',
    //         paint: {
    //           'fill-extrusion-vertical-gradient': true,
    //           'fill-extrusion-height': ['get', 'Z_Max'],
    //           'fill-extrusion-base': 0,
    //           'fill-extrusion-opacity': 1,
    //           'fill-extrusion-color': '#fff'
    //         }
    //       }
    //     }
    //   }
    // },
    // 给水管道
    // 排水管
    tubeLine: {
      type: 'vector',
      scheme: 'tms',
      tiles: ['https://www.szwgft.cn/mapServer/geoserver/gwc/service/tms/1.0.0/pipeline-group@EPSG%3A3857@pbf/{z}/{x}/{y}.pbf'],
      layers: {
        tubeLayer_line: {
          options: {
            'source-layer': 'PS_PIPE',
            paint: {
              'line-color': '#000000',
              'line-width': 2,
              'line-opacity': 1
            }
          }
        },
        canalLayer_line: {
          options: {
            'source-layer': 'PS_CONDUIT',
            paint: {
              'line-color': '#ff00ff',
              'line-width': 2,
              'line-opacity': 1
            }
          }
        },
        feedwaterLayer_line: {
          options: {
            'source-layer': 'SWAT_PIPE',
            paint: {
              'line-color': '#00ffff',
              'line-width': 2,
              'line-opacity': 1
            }
          }
        }
      }
    }
  }
}

let Map
function mapLoad(map) {
  Map = map
}

// 点击图层事件
function architectureLayerClick(e, f, map) {
  const center = f.geometry.coordinates[0][0]
  map.setPaintProperty('zone_fill-extrusion', 'fill-extrusion-color', ['match', ['get', 'Zone_Code'], f.properties.Zone_Code, '#7fb192', '#fff'])
  MapFlyTo(map, center)
}

function MapFlyTo(map, center) {
  map.flyTo({
    center: center,
    zoom: 17,
    bearing: getRandomNumber(30, 120), // 旋转 45 度
    pitch: 60, // 倾斜 60 度
    duration: 3000,
    speed: 1.8,
    curve: 2
  })
}

// 随机生成一个数字
function getRandomNumber(min, max) {
  return Math.floor(Math.random() * (max - min + 1)) + min
}

// 恢复到初始视角
function restore() {
  Map.flyTo({
    center: [114.05528061331722, 22.54140197444606],
    zoom: 13,
    bearing: 0, // 旋转 45 度
    pitch: 0, // 倾斜 60 度
    duration: 1500,
    speed: 1.8,
    curve: 2
  })
}

// 搜索列表点击处理
function handlerClick(e) {
  Map.setPaintProperty('zone_fill-extrusion', 'fill-extrusion-color', ['match', ['get', 'Zone_Code'], e.Zone_Code, '#7fb192', '#fff'])
  MapFlyTo(Map, e.Center_Point.split(','))
}
</script>

<style lang="less" scoped>
.icon_box {
  right: 30px;
  top: 160px;
  width: 40px;
  height: 40px;
  padding: 5px 3px;
  background-color: white;
  margin-right: 12px;
  border-radius: 6px;
  cursor: pointer;
}
</style>
