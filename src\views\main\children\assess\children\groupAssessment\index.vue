<template>
  <div class="all">
    <!-- <input type="file" id="fileInput" /> -->
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
// import XLSX from 'xlsx'
// import { educeXlsx } from '@/utils/educeXlsx.js'

// onMounted(() => {
//   const fileInput = document.getElementById('fileInput') // 获取文件输入元素
//   fileInput.addEventListener('change', (event) => {
//     const file = event.target.files[0]
//     const reader = new FileReader()

//     reader.onload = (e) => {
//       // 读取文件内容到 ArrayBuffer
//       const data = new Uint8Array(e.target.result)

//       // 使用 SheetJS 解析数据
//       const workbook = XLSX.read(data, { type: 'array' })

//       // 获取第一个工作表
//       const sheetName = workbook.SheetNames[0]
//       const worksheet = workbook.Sheets[sheetName]

//       // 将工作表数据转换为 JSON 对象 (可选，取决于你的需求)
//       const jsonData = XLSX.utils.sheet_to_json(worksheet, { header: 1, raw: false }) // header: 1 表示第一行作为表头

//       // 处理读取到的数据 (jsonData 或 worksheet)

//       const hander = jsonData.shift()
//       const list = jsonData.map((item, index) => {
//         const D = {}
//         hander.forEach((key, index) => {
//           const is = /Time/.test(key)
//           if (is) {
//             if (item[index]) {
//               const time = new Date(item[index]).getTime() + 1000 * 60 * 60 * 8

//               D[key] = new Date(time).toISOString()
//             } else {
//               D[key] = null
//             }
//           }

//           D[key] = item[index] ? item[index] : null
//         })
//         return D
//       })

//       const c = {}
//       hander.forEach((item) => (c[item] = item))
//       console.log(list)
//       // educeXlsx(list, c, 'test')
//     }

//     reader.readAsArrayBuffer(file)
//   })
// })
</script>

<style lang="less" scoped></style>
