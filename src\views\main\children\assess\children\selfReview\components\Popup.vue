<template>
  <div class="popup">
    <div v-if="type === 'dot'">
      <div class="text-center fon-W600">{{ data['测站名'] }}</div>
      <div class="flex">
        <div class="f-xy-center f-1">
          <div>标高：</div>
          <div>{{ data['标高'] }}</div>
        </div>
        <div class="f-xy-center f-1">
          <div>井深：</div>
          <div>{{ data['井深'] }}</div>
        </div>
      </div>

      <div class="flex">
        <div class="f-xy-center f-1">
          <div>检查井编码：</div>
          <div>{{ data['检查井'] }}</div>
        </div>
      </div>
    </div>

    <div v-else>
      <div>
        <div class="f-xy-center f-1">
          <div>检查井编码：</div>
          <div>{{ data['检查井'] }}</div>
        </div>
      </div>
      <div class="flex">
        <div class="f-xy-center f-1">
          <div>管径：</div>
          <div>{{ data.DIAMETER }}</div>
        </div>
        <div class="f-xy-center f-1">
          <div>类型：</div>
          <div>{{ data.SUBTYPE }}</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
const props = defineProps({ data: Object, type: String })
</script>

<style lang="less" scoped>
.popup {
  padding: 14px;
  min-width: 220px;
}
</style>
