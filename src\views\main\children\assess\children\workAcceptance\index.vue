<template>
  <div class="all">
    <MapGlS :options="{ sources: { zone } }" @load="mapLoad" />
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue'
import MapGlS from '@/components/MapGlS2/index.vue'
import { FT_ZONE } from '@/ownedByAllMapConfig'

const zone = JSON.parse(JSON.stringify(FT_ZONE))
zone.layers.zone_fill.options.paint['fill-color'] = '#999'
zone.layers.zone_fill.events = { click: mapZoneClick }
let Map

function mapLoad(map) {
  Map = map
}

function mapZoneClick(e, f, map) {
  console.log(f)
}
</script>

<style lang="less" scoped></style>
