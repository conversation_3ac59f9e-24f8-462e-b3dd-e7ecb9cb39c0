<template>
  <!-- 污水点 -->
  <div class="all relative">
    <MapGlS ref="mapComponentRef" :options :legend @load="mapLoad" />
    <div class="icon_box absolute pointer back-white box-shadow f-xy-center z-index-100 f-column border-R6">
      <AimOutlined style="font-size: 26px" @click="handlerMove" />
    </div>

    <a-date-picker class="month absolute" v-model:value="time" size="large" valueFormat="YYYY-MM" picker="month" format="YYYY-MM" />
  </div>
</template>

<script setup>
import { ref, watch } from 'vue'
import { AimOutlined } from '@ant-design/icons-vue'
import mapboxgl from 'mapbox-gl'
import { Modal, message } from 'ant-design-vue'
import { createVNode } from 'vue'
import { ExclamationCircleOutlined } from '@ant-design/icons-vue'

import BiPopup from '@/components/MapPopup/biPopup.vue'
import { transitionComponent } from '@/utils/transitionDom'
import { storeToRefs } from 'pinia'
import { useBiStore } from '@/store/bi'
import { deleteDot } from '@/services/modules/bi'

import icon1 from '@/assets/images/011.png'
import icon0 from '@/assets/images/031.png'

import MapGlS from '@/components/MapGlS2/index.vue'
import ZONE_DATA from '@/assets/geojson/十网格.json'

const { getBiDotList } = useBiStore()
const { dots, time } = storeToRefs(useBiStore())

const legend = [
  { label: '污水外冒点', url: icon0 },
  { label: '积水点', url: icon1 }
]

let Map
const mapComponentRef = ref(null)

function mapLoad(map) {
  Map = map
  getBiDotList()
}
watch(time, () => getBiDotList())

const options = {
  sources: {
    ZONE: {
      type: 'geojson',
      data: ZONE_DATA,
      layers: {
        ZONE_fill: {
          options: {
            paint: {
              'fill-opacity': 0.5,
              'fill-color': [
                'match',
                ['get', 'name'],
                '福中-1',
                '#999999',
                '福中-2',
                '#947dd2',
                '福中-3',
                '#38056c',
                '福东-1',
                '#ffc300',
                '福东-2',
                '#1677ff',
                '梅林-2',
                '#8d0e8d',
                '梅林-1',
                '#cfc00e',
                '香蜜-3',
                '#37840b',
                '香蜜-2',
                '#900C3F',
                '香蜜-1',
                '#0B7784',
                '#999999'
              ]
            }
          }
        },
        ZONE_line: {
          options: { paint: { 'line-width': 2, 'line-color': '#000' } }
        },
        ZONE_symbol: {
          options: {
            layout: { 'text-field': ['get', 'name'], 'text-anchor': 'center', 'text-size': 22 },
            paint: { 'text-color': '#c500ff', 'text-halo-color': 'white', 'text-halo-width': 1 }
          }
        }
      }
    },
    DOT: {
      type: 'geojson',
      layers: {
        Dot_symbol: {
          images: { 污水外冒点: icon0, 积水点: icon1 },
          options: {
            layout: {
              'icon-image': ['get', 'Type'], //设置图片
              'icon-size': 0.12, //设置图片大小
              'icon-offset': [0, -105] //设置偏移
            }
          },
          events: {
            click: (e, feature) => {
              Modal.confirm({
                title: '标记删除',
                icon: createVNode(ExclamationCircleOutlined),
                content: '是否删除标记？',
                async onOk() {
                  try {
                    const res = await deleteDot(feature.properties.ID)
                    if (res) await getBiDotList()
                    message.success('删除成功')
                  } catch (error) {
                    console.error(error)
                    message.error('删除失败')
                  }
                }
              })
            }
          }
        }
      }
    }
  }
}
// 图层配置

watch(dots, handlerWatch, { deep: true })
function handlerWatch(newVal) {
  if (Marker.value) Marker.value.remove()
  mapComponentRef.value.updatedData('DOT', newVal)
}

const component = transitionComponent(BiPopup)
const Marker = ref(null)
// 中心点添加标记
function handlerMove() {
  // 监听地图拖动事件
  Map.on('move', handlerMoveEvent)
  // 获取中心点坐标
  let { lng, lat } = Map.getCenter()
  // 添加标记
  if (Marker.value) Marker.value.remove()
  Marker.value = new mapboxgl.Marker().setLngLat([lng, lat]).addTo(Map)
  Marker.value.getElement().addEventListener('click', handlerMarkerClick) // 标记添加点击事件

  // 地图拖动事件处理函数
  function handlerMoveEvent() {
    let { lng, lat } = Map.getCenter() // 获取新的中心点坐标
    Marker.value.setLngLat([lng, lat]) // 跟新标记位置
  }
  // 标记点击事件处理函数
  function handlerMarkerClick(e) {
    Marker.value.getElement().removeEventListener('click', handlerMarkerClick)
    const config = { layers: ['ZONE_fill'] }
    const { properties } = Map.queryRenderedFeatures([Marker.value._pos.x, Marker.value._pos.y], config)[0]
    const popup = new mapboxgl.Popup({ maxWidth: 'none' }).setDOMContent(component({ data: { ...properties, ...Marker.value._lngLat }, time: time.value })).addTo(Map)
    Marker.value.setPopup(popup).togglePopup()
    Map.off('move', handlerMoveEvent)
    e.stopPropagation()
  }
}
</script>

<style lang="less" scoped>
.icon_box {
  right: 40px;
  top: 120px;
  width: 40px;
  height: 40px;
  padding: 5px 3px;
}
.month {
  right: 40px;
  top: 60px;
}
</style>
