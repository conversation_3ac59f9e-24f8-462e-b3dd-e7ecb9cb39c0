<template>
  <div class="wrapper">
    <div class="mar-B10 fon-S18 fon-W600">图层控制</div>
    <a-tree v-model:checkedKeys="checkedKeys" checkable @check="onCheck" :tree-data="treeData" />
  </div>
</template>

<script setup>
import { ref } from 'vue'
const emit = defineEmits(['change'])

const treeData = [
  {
    title: '建筑白模',
    key: 'zone_fill-extrusion',
    layerId: 'zone_fill-extrusion'
  },
  {
    title: '基础底图',
    key: 'baseLayer',
    disabled: true,
    children: [
      {
        title: '片区图层',
        key: 'FTModificationWorks_fill',
        layerId: 'FTModificationWorks_fill',
        source: 'FT_ModificationWorks'
      },
      {
        title: '网格图层',
        key: 'tenGrid_fill',
        layerId: 'tenGrid_fill',
        source: 'tenGrid'
      },
      {
        title: '街道图层',
        key: 'FTstreet_fill',
        layerId: 'FTstreet_fill',
        source: 'FT_street'
      }
    ]
  },
  {
    title: '排水设施',
    key: 'drainage',
    disabled: true,
    children: [
      {
        title: '排水管道',
        key: 'tubeLayer_line',
        layerId: 'tubeLayer_line',
        children: [
          {
            title: '雨水管',
            key: 'tubeLayer_line_ys',
            layerId: 'tubeLayer_line',
            filtration: 'SUBTYPE',
            filtrationValue: '雨水管'
          },
          {
            title: '污水管',
            layerId: 'tubeLayer_line',
            key: 'tubeLayer_line_ws',
            filtration: 'SUBTYPE',
            filtrationValue: '污水管'
          }
        ]
      },
      {
        title: '排水渠',
        key: 'canalLayer_line',
        layerId: 'canalLayer_line',
        children: [
          {
            title: '雨水渠',
            key: 'canalLayer_line_ys',
            layerId: 'canalLayer_line',
            filtration: 'SUBTYPE',
            filtrationValue: '雨水渠'
          },
          {
            title: '污水渠',
            key: 'canalLayer_line_ws',
            layerId: 'canalLayer_line',
            filtration: 'SUBTYPE',
            filtrationValue: '污水渠'
          }
        ]
      },
      {
        title: '排水井',
        key: 'well_circle',
        layerId: 'well_circle',
        children: [
          {
            title: '雨水井',
            key: 'well_circle_ys',
            layerId: 'well_circle',
            filtration: 'SUBTYPE',
            filtrationValue: 'YS'
          },
          {
            title: '污水井',
            key: 'well_circle_ws',
            layerId: 'well_circle',
            filtration: 'SUBTYPE',
            filtrationValue: 'WS'
          }
        ]
      },
      {
        title: '雨水箅子',
        key: 'grate_circle',
        layerId: 'grate_circle'
      }
    ]
  },
  {
    title: '供水设施',
    key: 'feedwater',
    disabled: true,
    children: [
      {
        title: '供水管段',
        key: 'feedwaterLayer',
        layerId: 'feedwaterLayer_line'
      },
      {
        title: '总阀总表',
        key: 'clique_symbol',
        layerId: 'clique_symbol'
      },
      {
        title: '2D泵房',
        key: 'dot_symbol_2D'
      },
      {
        title: '3D泵房',
        key: 'dot_symbol_3D'
      }
    ]
  },
  {
    title: '业务图层',
    key: 'business',
    disabled: true,
    children: [
      {
        title: '日常维护',
        key: 'maintain_symbol',
        layerId: 'maintain_symbol'
      }
    ]
  }
]
const checkedKeys = ref(['zone_fill-extrusion'])

function onCheck(checkedKeys, info) {
  emit('change', { checkedKeys, info })
}
</script>

<style lang="less" scoped>
.wrapper {
  margin: 24px;
  background-color: #fff;
  padding: 10px 30px;
  border-radius: 4px;
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.5);
  :deep(.ant-tree-treenode-disabled .ant-tree-node-content-wrapper) {
    color: rgba(0, 0, 0, 0.9);
    font-weight: 600;
    cursor: pointer;
  }
  :deep(.ant-tree-checkbox-disabled) {
    display: none;
  }
}
</style>
