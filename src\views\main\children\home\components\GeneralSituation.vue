<template>
  <div @click="isShow = !isShow" class="general-situation flex f-column overflow-hidden box-shadow pad-10 border-R12 absolute" :class="{ fold: fold }">
    <div class="absolute pad-10 z-index-100 pointer fold-icon" @click="fold = !fold"><MenuFoldOutlined class="fon-S26 color-666" v-if="!fold" /> <MenuUnfoldOutlined class="fon-S26 color-666" v-else /></div>
    <Introduce :detail />
  </div>
</template>

<script setup>
import { computed, ref, watch } from 'vue'
import { MenuFoldOutlined, MenuUnfoldOutlined, ContainerOutlined, SearchOutlined, CloseOutlined } from '@ant-design/icons-vue'
import Introduce from './Introduce.vue'
import { useRouter } from 'vue-router'

const router = useRouter()
const fold = defineModel()
const isShow = ref(false)
const props = defineProps({ detail: { type: Object, default: () => ({}) } })
</script>

<style lang="less" scoped>
.general-situation {
  width: 410px;
  // height: 660px;
  background-color: #fff;
  top: 160px;
  right: 20px;
  transition: all 0.4s ease-in-out;
}
.fold {
  width: 45px !important;
  height: 45px !important;
  transition: all 0.4s ease-in-out;
}
.fold-icon {
  top: 0;
  right: 0;
}

.active {
  height: 240px;
  transition: all 0.4s ease-in-out;
}

.tag {
  right: 0;
}
</style>
