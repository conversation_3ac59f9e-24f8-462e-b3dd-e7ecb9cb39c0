<template>
  <div class="introduce back-white" v-if="detail?.Zone_Name">
    <div class="fon-S20 fon-W700 mar-B10">（ {{ detail?.Zone_Name }} ）介绍</div>
    <a-image
      :width="360"
      :height="120"
      fallback="data:image/png;base64,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"
      :src="`https://www.szwgft.cn/nodeServer/img/FTImg/${detail?.Zone_Code}.png`"
    />
    <div class="intro-text" v-if="record">
      <p class="intro-paragraph">
        <span class="highlight-text">{{ record.xqmc || '该小区' }}</span
        >位于 <span class="location-text">{{ record.districtname || '--' }}{{ record.ssjd || '--' }}{{ record.communityname || '--' }}</span
        >， 属于<span class="category-text">{{ record.xqlb || '住宅' }}</span
        >类型小区， 由<span class="management-text">{{ record.deptname || '--' }}</span
        >负责管理。
      </p>

      <p class="intro-paragraph">
        小区共有<span class="metric-text">{{ getBlockCount(record.block_number) }}栋</span>建筑， 服务<span class="metric-text">{{ record.usercount || '--' }}户</span>居民，
        {{ record.is_pump_room && record.is_pump_room.includes('否') ? '未设' : '设有' }}二次供水泵房设施。
        {{ record.jsgl?.length ? `小区共有${record.jsgl.length}路进水管线，` : '' }}
        供水方式为<span class="supply-text">{{ record.gsfs || '--' }}</span
        >。
      </p>

      <!-- 泵房改造情况 -->
      <div class="renovation-summary" v-if="record.pump_room_status && record.pump_room_status !== '--'">
        <div class="renovation-header">
          <wd-icon name="tool" size="16px" color="#722ed1"></wd-icon>
          <span class="renovation-title">泵房改造情况</span>
        </div>
        <p class="renovation-text">
          当前泵房状态为<span class="status-highlight" :class="getStatusClass(record.pump_room_status)">{{ getStatusText(record.pump_room_status) }}</span
          >。
          <template v-if="record.pump_room_status === '已通水'">
            改造工程已完成，泵房数量从
            <span class="before-after">{{ record.before_pump_room_count || 0 }}座</span>
            {{ record.bfsl && record.before_pump_room_count !== null && record.before_pump_room_count !== undefined && record.bfsl !== record.before_pump_room_count ? `调整为${record.bfsl}座` : '保持不变' }}， 加压户数从
            <span class="before-after">{{ record.before_number_pressurized || 0 }}户</span>
            {{ record.jyhs && record.before_number_pressurized !== null && record.before_number_pressurized !== undefined && record.jyhs !== record.before_number_pressurized ? `增至${record.jyhs}户` : '保持不变' }}。
            {{ record.eggzwcnd ? `改造于${record.eggzwcnd?.slice(0, 10)}年完成。` : '' }}
          </template>
          <template v-else-if="record.pump_room_status === '已立项施工中(指临供状态)'">
            改造工程正在进行中，目前处于临时供水阶段。
            {{ record.egcsjsnd ? `临时供水自${record.egcsjsnd}开始。` : '' }}
          </template>
          <template v-else-if="record.pump_room_status === '已立项未进场'"> 改造工程已立项，计划改造泵房{{ record.before_pump_room_count || '--' }}座， 服务{{ record.before_number_pressurized || '--' }}户居民。 </template>
          <template v-else-if="record.pump_room_status === '无需二供改造'"> 该小区供水设施运行良好，无需进行二次供水改造。 </template>
        </p>
      </div>

      <!-- 管网改造情况 -->
      <div class="pipeline-summary" v-if="record.sftbgz">
        <div class="pipeline-header">
          <wd-icon name="link" size="16px" color="#52c41a"></wd-icon>
          <span class="pipeline-title">管网改造情况</span>
        </div>
        <p class="pipeline-text">
          <template v-if="handlerBoolean(record.sftbgz) === '是'">
            小区已完成提标改造，
            {{ record.gzwcnd ? `于${record.gzwcnd.slice(0, 4)}年完成改造。` : '' }}
            埋地管材由<span class="material-change">{{ handlerlist(record.mdgc) || '--' }}</span> 更换为<span class="material-change">{{ handlerlist(record.buried_pipes_after) || '--' }}</span
            >， 爬墙管材由<span class="material-change">{{ handlerlist(record.pqggc) || '--' }}</span> 更换为<span class="material-change">{{ handlerlist(record.wall_climbing_pipe_after) || '--' }}</span
            >。
            {{ record.excellent_drink_batch ? `属于优饮${record.excellent_drink_batch}批次项目。` : '' }}
          </template>
          <template v-else>
            小区尚未进行提标改造， 当前埋地管材为<span class="material-info">{{ handlerlist(record.mdgc) || '--' }}</span
            >， 爬墙管材为<span class="material-info">{{ handlerlist(record.pqggc) || '--' }}</span
            >。
          </template>
        </p>
      </div>
    </div>

    <!-- 当record为空时，使用detail数据展示基础信息 -->
    <div v-else class="intro-text">
      <p class="intro-paragraph">
        <span class="highlight-text">{{ detail?.Zone_Name || '该区块' }}</span>
        位于 <span class="location-text">{{ detail?.Subdistrict || '--' }}{{ detail?.Community || '--' }}</span
        >， 属于<span class="category-text">{{ detail?.Zone_Type || '住宅' }}</span
        >类型区块， 由<span class="management-text">{{ detail?.ManagerName || '--' }}</span
        >负责管理。
      </p>

      <p class="intro-paragraph">
        该区块位于<span class="supply-text">{{ detail?.Road || '--' }}</span
        >路段， {{ detail?.GoodDrinkBatch ? `属于优饮${detail.GoodDrinkBatch}批次项目` : '暂无优饮批次信息' }}。
        {{ detail?.GoodDrinkingFinishTime ? `优饮工程于${detail.GoodDrinkingFinishTime}完成。` : '' }}
      </p>

      <!-- 二供信息 -->
      <div class="renovation-summary" v-if="detail?.SecondarySupplylot || detail?.SecondarySupplyCondition">
        <div class="renovation-header">
          <wd-icon name="tool" size="16px" color="#722ed1"></wd-icon>
          <span class="renovation-title">二次供水情况</span>
        </div>
        <p class="renovation-text">
          {{ detail?.SecondarySupplylot ? `该区块属于二供${detail.SecondarySupplylot}批次项目，` : '' }}
          {{ detail?.SecondarySupplyCondition ? `当前二供状态为${detail.SecondarySupplyCondition}。` : '暂无二供状态信息。' }}
        </p>
      </div>

      <!-- 风险分级信息 -->
      <div class="pipeline-summary" v-if="detail?.PipeNetworkRiskClassification || detail?.SecondaryRiskClassification || detail?.WaterQualityComplaint">
        <div class="pipeline-header">
          <wd-icon name="warning" size="16px" color="#fa8c16"></wd-icon>
          <span class="pipeline-title">风险分级情况</span>
        </div>
        <p class="pipeline-text">
          {{ detail?.PipeNetworkRiskClassification ? `管网风险分级：${detail.PipeNetworkRiskClassification}；` : '' }}
          {{ detail?.SecondaryRiskClassification ? `二供风险分级：${detail.SecondaryRiskClassification}；` : '' }}
          {{ detail?.WaterQualityComplaint ? `水质投诉分级：${detail.WaterQualityComplaint}。` : '' }}
          {{ !detail?.PipeNetworkRiskClassification && !detail?.SecondaryRiskClassification && !detail?.WaterQualityComplaint ? '暂无风险分级信息。' : '' }}
        </p>
      </div>

      <!-- 提示信息 -->
      <div class="info-notice">
        <wd-icon name="info" size="14px" color="#1890ff"></wd-icon>
        <span>该小区无详细档案信息，如有数据异常请联系管理员。</span>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, watch } from 'vue'
import * as API from '@/services/modules/zone.record'
import { message } from 'ant-design-vue'

const record = ref(null)
const props = defineProps({ detail: { type: Object, default: () => ({}) } })

watch(() => props.detail, watchCallback, { deep: true })

async function watchCallback(val) {
  if (!val.Client_Name) return (record.value = null)
  try {
    const { data } = await API.getFuzzyDetail(val.Client_Name)
    if (data.jsgl == '[]' || !data.jsgl || data.jsgl?.length < 10) {
      data.jsgl = []
    } else {
      data.jsgl = JSON.parse(data.jsgl)
    }
    record.value = data
    console.log(data)
  } catch (error) {
    message.error('获取小区档案失败')
  }
}

// 获取楼栋数量
function getBlockCount(val) {
  if (!val || val == '""') return '0'
  const list = val.split(';')
  const total = list.reduce((total, item) => {
    let match = item.match(/栋数：(\d+)栋/)
    if (match) {
      let count = parseInt(match[1], 10)
      return total + count
    } else {
      return total
    }
  }, 0)
  return total.toString()
}

// 获取状态样式类
function getStatusClass(status) {
  const statusMap = {
    无需二供改造: 'status-complete',
    查漏补缺: 'status-partial',
    已立项未进场: 'status-pending',
    '已立项施工中(指临供状态)': 'status-progress',
    已通水: 'status-complete'
  }
  return statusMap[status] || 'status-unknown'
}

// 获取状态文本
function getStatusText(status) {
  const textMap = {
    无需二供改造: '无需改造',
    查漏补缺: '查漏补缺',
    已立项未进场: '待进场',
    '已立项施工中(指临供状态)': '施工中',
    已通水: '已完成'
  }
  return textMap[status] || '未知状态'
}

// 处理布尔值
function handlerBoolean(value) {
  return value === true ? '是' : value === false ? '否' : value
}

// 处理列表数据
function handlerlist(value) {
  if (!value || value == '""') return ''
  if (/^[\[]/.test(value)) return JSON.parse(value).join('、')
  return value.replace(/"/g, '')
}
</script>

<style lang="less" scoped>
.introduce {
  padding: 24px 14px;
  line-height: 24px;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);

  // 标题样式
  .fon-S20 {
    color: #1a1a1a;
    margin-bottom: 16px;
    text-align: center;
    font-weight: 700;
    letter-spacing: 0.5px;
  }

  // 图片容器
  :deep(.ant-image) {
    display: block;
    margin: 0 auto 20px;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);

    img {
      transition: transform 0.3s ease;

      &:hover {
        transform: scale(1.02);
      }
    }
  }

  // 介绍文本容器
  .intro-text {
    font-size: 15px;
    line-height: 1.8;
    color: #333;

    .intro-paragraph {
      margin-bottom: 16px;
      text-align: justify;
      text-indent: 2em;

      // 高亮文本样式
      .highlight-text {
        color: #1890ff;
        font-weight: 600;
        // background: linear-gradient(120deg, #e6f7ff 0%, #bae7ff 100%);
        padding: 2px 6px;
        border-radius: 4px;
        margin: 0 2px;
      }

      // 位置信息样式
      .location-text {
        color: #52c41a;
        font-weight: 500;
        // background: linear-gradient(120deg, #f6ffed 0%, #d9f7be 100%);
        padding: 2px 6px;
        border-radius: 4px;
        margin: 0 2px;
      }

      // 类别信息样式
      .category-text {
        color: #722ed1;
        font-weight: 500;
        // background: linear-gradient(120deg, #f9f0ff 0%, #efdbff 100%);
        padding: 2px 6px;
        border-radius: 4px;
        margin: 0 2px;
      }

      // 管理信息样式
      .management-text {
        color: #fa8c16;
        font-weight: 500;
        // background: linear-gradient(120deg, #fff7e6 0%, #ffd591 100%);
        padding: 2px 6px;
        border-radius: 4px;
        margin: 0 2px;
      }

      // 数据指标样式
      .metric-text {
        color: #eb2f96;
        font-weight: 600;
        // background: linear-gradient(120deg, #fff0f6 0%, #ffadd2 100%);
        padding: 2px 6px;
        border-radius: 4px;
        margin: 0 2px;
      }

      // 供水方式样式
      .supply-text {
        color: #13c2c2;
        font-weight: 500;
        // background: linear-gradient(120deg, #e6fffb 0%, #87e8de 100%);
        padding: 2px 6px;
        border-radius: 4px;
        margin: 0 2px;
      }
    }
  }

  // 泵房改造情况样式
  .renovation-summary {
    background: linear-gradient(135deg, #f6f9ff 0%, #e6f4ff 100%);
    border: 1px solid #d6e4ff;
    border-radius: 8px;
    padding: 16px;
    margin: 20px 0;
    position: relative;

    &::before {
      content: '';
      position: absolute;
      left: 0;
      top: 0;
      bottom: 0;
      width: 4px;
      background: linear-gradient(180deg, #722ed1 0%, #9254de 100%);
      border-radius: 2px 0 0 2px;
    }

    .renovation-header {
      display: flex;
      align-items: center;
      margin-bottom: 12px;

      .renovation-title {
        margin-left: 8px;
        font-weight: 600;
        color: #722ed1;
        font-size: 16px;
      }
    }

    .renovation-text {
      color: #4a4a4a;
      line-height: 1.7;
      margin: 0;
      text-indent: 2em;

      // 状态高亮样式
      .status-highlight {
        font-weight: 600;
        padding: 3px 8px;
        border-radius: 4px;
        margin: 0 2px;

        &.status-complete {
          color: #52c41a;
          // background: linear-gradient(120deg, #f6ffed 0%, #d9f7be 100%);
        }

        &.status-progress {
          color: #1890ff;
          // background: linear-gradient(120deg, #e6f7ff 0%, #bae7ff 100%);
        }

        &.status-pending {
          color: #fa8c16;
          // background: linear-gradient(120deg, #fff7e6 0%, #ffd591 100%);
        }

        &.status-partial {
          color: #faad14;
          // background: linear-gradient(120deg, #fffbe6 0%, #fff1b8 100%);
        }

        &.status-unknown {
          color: #8c8c8c;
          // background: linear-gradient(120deg, #f5f5f5 0%, #d9d9d9 100%);
        }
      }

      // 前后对比数据样式
      .before-after {
        color: #1890ff;
        font-weight: 600;
        // background: linear-gradient(120deg, #e6f7ff 0%, #bae7ff 100%);
        padding: 2px 6px;
        border-radius: 4px;
        margin: 0 2px;
      }
    }
  }

  // 管网改造情况样式
  .pipeline-summary {
    background: linear-gradient(135deg, #f6ffed 0%, #e6f7ff 100%);
    border: 1px solid #b7eb8f;
    border-radius: 8px;
    padding: 16px;
    margin-top: 20px;
    position: relative;

    &::before {
      content: '';
      position: absolute;
      left: 0;
      top: 0;
      bottom: 0;
      width: 4px;
      background: linear-gradient(180deg, #52c41a 0%, #73d13d 100%);
      border-radius: 2px 0 0 2px;
    }

    .pipeline-header {
      display: flex;
      align-items: center;
      margin-bottom: 12px;

      .pipeline-title {
        margin-left: 8px;
        font-weight: 600;
        color: #52c41a;
        font-size: 16px;
      }
    }

    .pipeline-text {
      color: #4a4a4a;
      line-height: 1.7;
      margin: 0;
      text-indent: 2em;

      // 材料变更样式
      .material-change {
        color: #722ed1;
        font-weight: 600;
        // background: linear-gradient(120deg, #f9f0ff 0%, #efdbff 100%);
        padding: 2px 6px;
        border-radius: 4px;
        margin: 0 2px;
      }

      // 材料信息样式
      .material-info {
        color: #13c2c2;
        font-weight: 500;
        // background: linear-gradient(120deg, #e6fffb 0%, #87e8de 100%);
        padding: 2px 6px;
        border-radius: 4px;
        margin: 0 2px;
      }
    }
  }

  // 提示信息样式
  .info-notice {
    display: flex;
    align-items: center;
    background: linear-gradient(135deg, #e6f7ff 0%, #f0f9ff 100%);
    border: 1px solid #91d5ff;
    border-radius: 6px;
    padding: 12px 16px;
    margin-top: 16px;
    font-size: 14px;
    color: #1890ff;

    span {
      margin-left: 8px;
      line-height: 1.5;
    }

    &::before {
      content: '';
      position: absolute;
      left: 0;
      top: 0;
      bottom: 0;
      width: 3px;
      background: #1890ff;
      border-radius: 2px 0 0 2px;
    }

    position: relative;
  }
}

// 响应式设计
@media (max-width: 768px) {
  .introduce {
    padding: 16px 12px;

    .fon-S20 {
      font-size: 18px;
    }

    :deep(.ant-image) {
      width: 100% !important;
      height: auto !important;
    }

    .intro-text {
      font-size: 14px;

      .intro-paragraph {
        text-indent: 1.5em;
      }
    }

    .renovation-summary,
    .pipeline-summary {
      padding: 12px;
      margin: 16px 0;

      .renovation-title,
      .pipeline-title {
        font-size: 15px;
      }
    }
  }
}

// 深色模式适配
@media (prefers-color-scheme: dark) {
  .introduce {
    background: #1f1f1f;
    color: #e6e6e6;

    .fon-S20 {
      color: #ffffff;
    }

    .intro-text {
      color: #d9d9d9;
    }

    .renovation-summary {
      background: linear-gradient(135deg, #262626 0%, #1f1f1f 100%);
      border-color: #434343;
    }

    .pipeline-summary {
      background: linear-gradient(135deg, #262626 0%, #1f1f1f 100%);
      border-color: #434343;
    }
  }
}
</style>
