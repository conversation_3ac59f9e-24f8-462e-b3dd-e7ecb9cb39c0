<template>
  <div class="maintain-popup">
    <div class="fon-S16 fon-W600 mar-B10 text-center">GIS修正消息-{{ data.state }}</div>

    <div class="flex">
      <div class="f-x-center f-1">
        <div class="fon-W600">设施类型:</div>
        <div>{{ data.facility_Type }}</div>
      </div>
      <div class="f-x-center f-1">
        <div>任务类型:</div>
        <div>{{ data.task_Type }}</div>
      </div>
    </div>

    <div class="flex">
      <div class="f-x-center f-1">
        <div>上报人:</div>
        <div>{{ data.inputstaff }}</div>
      </div>
      <div class="f-x-center f-1">
        <div>上报时间:</div>
        <div>{{ data.inputdate.slice(0, 10) }}</div>
      </div>
    </div>
    <div class="flex" v-if="data.inputstaff !== data.veruserkey">
      <div class="f-x-center f-1">
        <div>修改人:</div>
        <div>{{ data.veruserkey }}</div>
      </div>
      <div class="f-x-center f-1">
        <div>修改时间:</div>
        <div>{{ data.verdate.slice(0, 10) }}</div>
      </div>
    </div>
    <div>
      <div class="pad-10 fon-S14 color-666 border-R12 border-666">{{ data.remark1 }}</div>
    </div>
  </div>
</template>

<script setup>
defineProps({
  data: {
    type: Object,
    default: () => ({})
  }
})
</script>

<style lang="less" scoped>
.maintain-popup {
  padding: 12px;
  width: 300px;
}
</style>
