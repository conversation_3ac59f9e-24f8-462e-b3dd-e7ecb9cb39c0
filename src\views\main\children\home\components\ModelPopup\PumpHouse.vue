<template>
  <div class="wrap box-shadow">
    <div class="fon-S22 text-center mar-B20 fon-W600">
      <span>{{ data.PumpHouseName }}</span>
    </div>
    <div class="">
      <div class="row mar-Y6 flex f-between">
        <div class="f-y-center f-1 mar-R10">
          <span class="nowrap">所属街道：</span>
          <div class="content nowrap">{{ data.BelongingStreet }}</div>
        </div>
        <div class="f-y-center f-1 mar-R10">
          <span class="nowrap">项目状态：</span>
          <div class="content nowrap">{{ data.ProgressStatus }}</div>
        </div>
      </div>

      <div class="row mar-Y6 flex f-between">
        <div class="f-y-center f-1 mar-R10">
          <span class="nowrap">临供停水数：</span>
          <div class="content nowrap">{{ data.TemporarySupplyEvents ?? '--' }}</div>
        </div>
        <div class="f-y-center f-1 mar-R10">
          <span class="nowrap">现场负责人：</span>
          <div class="content nowrap">{{ data.PersonInCharge ?? '--' }}</div>
        </div>
      </div>
      <div class="row mar-Y6 flex f-between">
        <div class="f-y-center f-1 mar-R10">
          <span class="nowrap">更新人：</span>
          <div class="content nowrap">{{ data.UpdatePerson ?? '--' }}</div>
        </div>
        <div class="f-y-center f-1">
          <span class="nowrap">更新时间：</span>
          <div class="content nowrap">{{ data?.UpdateTime?.slice(0, 10) ?? '--' }}</div>
        </div>
      </div>

      <div class="row mar-Y6 flex f-between">
        <div class="f-y-center f-1">
          <div class="title">施工单位：</div>
          <div class="content">{{ data.ConstructionUnit ?? '--' }}</div>
        </div>
      </div>
      <div class="row mar-Y6 flex f-between">
        <div class="f-y-center f-1">
          <div class="title">泵房位置：</div>
          <div class="content">{{ data.AccuratePosition ?? '--' }}</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
const props = defineProps({ data: { type: Object, default: () => ({}) } })
</script>

<style lang="less" scoped>
.wrap {
  padding: 12px;
}

.content {
  font-weight: 600;
}
</style>
