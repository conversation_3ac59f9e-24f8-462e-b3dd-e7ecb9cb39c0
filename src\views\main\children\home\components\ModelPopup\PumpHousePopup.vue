<template>
  <div class="border-eee border-R6 pad-12">
    <div class="W100 text-center fon-S16 fon-W600 mar-B4">{{ data.PumpHouseName }}</div>
    <div class="fon-S12 fon-W600 color-333"><span class="fon-S12 fon-W600 color-333">改造状态：</span>{{ data.RemouldState }}</div>
    <div class="fon-S12 fon-W600 color-333"><span class="fon-S12 fon-W600 color-333 nowrap">运营管理状态：</span>{{ data.OperationManagementState }}</div>
    <div class="fon-S12 fon-W600 color-333"><span class="fon-S12 fon-W600 color-333">批次：</span>{{ batchKeys[data.Batch] }}</div>
  </div>
</template>

<script setup>
defineProps({ data: { type: Object, default: () => ({}) } })

const batchKeys = { 1: '已纳改:利源代建', 2: '已纳改:查漏补缺', 3: '需纳改', 4: '无需纳改', 5: '已纳改:应改未改' }
</script>
