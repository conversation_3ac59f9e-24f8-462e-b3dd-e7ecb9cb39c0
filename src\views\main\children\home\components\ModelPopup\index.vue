<template>
  <div class="wrap">
    <PumpHouse :data v-if="type === 'pumpHouse'" />
    <Tube :data v-if="type === 'tube'" />
    <Well :data v-if="type === 'well'" />
  </div>
</template>

<script setup>
import PumpHouse from './PumpHouse.vue'
import Well from './well.vue'
import Tube from './tube.vue'
const props = defineProps({
  data: { type: Object, default: () => ({}) },
  type: { type: String, default: '' }
})
</script>

<style lang="less" scoped>
.wrap {
}
</style>
