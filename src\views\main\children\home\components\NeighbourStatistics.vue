<template>
  <div class="wrap"></div>
</template>

<script setup>
import { ref } from 'vue'
import * as SituationApi from '@/services/modules/general.situation'

const Statistics = ref({ facility: [], pipeline: [] }) //facility: 其他设施, pipeline: 管渠
const surveyData = ref({})
const checked = ref(true) //是否包含区局管辖
const dateMonth = ref(null) // 日历组件的值

async function InitialCall() {
  const { data } = await SituationApi.dateSurvey()
  dateMonth.value = data.pop().year_month
  getStatistics(dateMonth.value, checked.value)
}

InitialCall()

async function getStatistics(date, checked) {
  const { data } = await SituationApi.survey({ date, all: checked })
  if (data.length === 0) {
    const { data } = await SituationApi.dateSurvey()
    setTimeout(() => (dateMonth.value = data.pop().year_month), 1500)
    return
  }
  surveyData.value = data
}
</script>

<style lang="less" scoped>
.wrap {
  width: 330px;
  min-height: 120px;

  // margin: 24px;
  background-color: #fff;
  // padding: 6px;
  border-radius: 4px;
  // box-shadow: 0 0 10px rgba(0, 0, 0, 0.5);
  & > div {
    border: 1px dotted #999;
    border-radius: 4px;
  }
}

.title {
  background-color: #1677ff;
  display: inline-block;
  color: #fff;
  padding: 6px 18px 6px 12px;
  margin-left: -17px;
  margin-top: 12px;
  border-radius: 0 16px 16px 0;
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.5);
}
.bgf7 {
  background-color: #f7de8b;
  display: inline-block;
  min-width: 70px;
  margin-right: 12px;
  padding: 4px;
  border-radius: 4px;
}
.border-L-e {
  border-left: 1px solid #eee;
}
.num {
  color: #1161d1;
}
</style>
