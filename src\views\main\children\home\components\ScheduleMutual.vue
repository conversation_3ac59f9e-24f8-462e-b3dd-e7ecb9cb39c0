<template>
  <a-modal :footer="null" :maskClosable="false" v-model:open="open" centered width="700px">
    <div class="fon-S28 text-center mar-B20 fon-W600">
      <span>{{ data.PumpHouseName }}</span>
    </div>
    <div class="wire">
      <div></div>
      <span>基础信息</span>
      <div></div>
    </div>
    <div class="flex f-between mar-B10">
      <div class="f-y-center" style="width: 32%">
        <div class="fon-W600 color-666">所属街道：</div>
        <span class="border-B-eee fon-W600 f-1 text-nowrap">{{ data.BelongingStreet }}</span>
      </div>
      <div class="f-y-center" style="width: 32%">
        <div class="fon-W600 color-666">所属网格：</div>
        <span class="border-B-eee fon-W600 f-1 text-nowrap">{{ data.Gridding }}</span>
      </div>
      <div class="f-y-center" style="width: 32%">
        <div class="fon-W600 color-666">所属片区：</div>
        <span class="border-B-eee fon-W600 f-1 text-nowrap">{{ data.BelongingArea }}</span>
      </div>
    </div>
    <div class="flex f-between mar-B10">
      <div class="f-y-center" style="width: 32%">
        <div class="fon-W600 color-666">改造状态：</div>
        <span class="border-B-eee fon-W600 f-1 text-nowrap">{{ data.RemouldState ?? '--' }}</span>
      </div>
      <div class="f-y-center" style="width: 32%">
        <div class="fon-W600 color-666">进展状态：</div>
        <span class="border-B-eee fon-W600 f-1 text-nowrap">{{ data.ProgressStatus ?? '--' }}</span>
      </div>
      <div class="f-y-center" style="width: 32%">
        <div class="fon-W600 color-666">泵房批次：</div>
        <span class="border-B-eee fon-W600 f-1 text-nowrap">{{ batchKeys[data.Batch] ?? '--' }}</span>
      </div>
    </div>
    <div class="flex f-between mar-B10">
      <div class="f-y-center" style="width: 32%">
        <div class="fon-W600 color-666">临供停水数：</div>
        <span class="border-B-eee fon-W600 f-1 text-nowrap">{{ data.TemporarySupplyEvents ?? '--' }}</span>
      </div>
      <div class="f-y-center" style="width: 32%">
        <div class="fon-W600 color-666">泵房管理：</div>
        <span class="border-B-eee fon-W600 f-1 text-nowrap">{{ data.PumpRoomControlledState ?? '--' }}</span>
      </div>
      <div class="f-y-center" style="width: 32%">
        <div class="fon-W600 color-666">运营管理：</div>
        <span class="border-B-eee fon-W600 f-1 text-nowrap">{{ data.OperationManagementState ?? '--' }}</span>
      </div>
    </div>

    <div class="flex f-between mar-B10">
      <div class="f-y-center" style="width: 32%">
        <div class="fon-W600 color-666">物业单位：</div>
        <span class="border-B-eee fon-W600 f-1 text-nowrap">{{ data.PropertyUnit ?? '--' }}</span>
      </div>
      <div class="f-y-center" style="width: 32%">
        <div class="fon-W600 color-666">物业联系人：</div>
        <span class="border-B-eee fon-W600 f-1 text-nowrap">{{ data.ContactPerson ?? '--' }}</span>
      </div>
      <div class="f-y-center f-3">
        <div class="fon-W600 color-666">物业电话：</div>
        <span class="border-B-eee fon-W600 f-1 text-nowrap">{{ data.PhoneNumber ?? '--' }}</span>
      </div>
    </div>

    <div class="flex f-between mar-B10">
      <div class="f-y-center" style="width: 50%">
        <div class="fon-W600 color-666">施工单位：</div>
        <span class="border-B-eee fon-W600 f-1 text-nowrap">{{ data.ConstructionUnit ?? '--' }}</span>
      </div>
      <div class="f-y-center" style="width: 48%">
        <div class="fon-W600 color-666">现场责任人：</div>
        <span class="border-B-eee fon-W600 f-1 text-nowrap">{{ data.PersonInCharge ?? '--' }}</span>
      </div>
    </div>
    <div class="flex f-between mar-B10">
      <div class="f-y-center" style="width: 48%">
        <div class="fon-W600 color-666">更新人：</div>
        <span class="border-B-eee fon-W600 f-1 text-nowrap">{{ data.UpdatePerson ?? '--' }}</span>
      </div>
      <div class="f-y-center" style="width: 48%">
        <div class="fon-W600 color-666">更新时间：</div>
        <span class="border-B-eee fon-W600 f-1 text-nowrap">{{ data.UpdateTime ?? '--' }}</span>
      </div>
    </div>
    <div class="flex f-between mar-B10">
      <div class="f-y-center" style="width: 48%">
        <div class="fon-W600 color-666">泵房位置：</div>
        <span class="border-B-eee fon-W600 f-1 text-nowrap">{{ data.AccuratePosition ?? '--' }}</span>
      </div>
      <div class="f-y-center" style="width: 48%">
        <div class="fon-W600 color-666">备注：</div>
        <span class="border-B-eee fon-W600 f-1 text-nowrap">{{ data.Remark ?? '--' }}</span>
      </div>
    </div>

    <div v-if="data?.PumpHouseImg?.length">
      <div class="flex mar-B10">
        <template v-for="item in (data.PumpHouseImg ?? '').split(',')" :key="item">
          <div class="mar-R10" v-if="item">
            <a-image :width="50" :height="50" :src="item.replace('http:', 'https:')" />
          </div>
        </template>
      </div>
    </div>

    <div class="wire">
      <div></div>
      <span>节点信息</span>
      <div></div>
    </div>
    <div class="relative">
      <DownCircleOutlined class="absolute pointer fon-S26" v-if="nowIndex <= data.CurrentNode - 2" @click="scrollToTop(true)" style="right: 58px; color: #1677ff; bottom: 4px" />
      <UpCircleOutlined class="absolute pointer fon-S26" v-if="nowIndex > 0" @click="scrollToTop(false)" style="right: 58px; color: #1677ff; top: 4px" />
      <div class="content_box" @scrollend="scrollEnd" ref="scrollRef">
        <template v-for="(item, index) in pumpHouseNodeValue.slice(0, data.CurrentNode)">
          <div class="item flex">
            <div class="f-1 flex pad-12">
              <div class="f-1">
                <div class="title fon-S18 fon-W600">{{ item.DictValue }} {{ pumpHouseNodeDetail?.CompletionTime?.slice(0, 10) }}</div>
                <div class="fon-W600" style="color: dodgerblue">节点备注：</div>
                <div>{{ pumpHouseNodeDetail?.Remark }}</div>
              </div>
              <div style="width: 55%" v-if="pumpHouseNodeDetail">
                <div class="fon-W600" style="color: dodgerblue">节点文件:</div>
                <div class="overflow-auto" style="height: 160px" v-if="pumpHouseNodeDetail?.Files?.length">
                  <template v-for="(file, ind) in pumpHouseNodeDetail.Files" :key="file.Id">
                    <div style="padding: 6px; border-bottom: 1px solid #fff; color: #1677ff" class="flex f-between f-y-center">
                      <span style="width: 280px" class="text-nowrap">{{ file.FileKey }}</span>
                      <div>
                        <a-button type="primary" style="width: 26px; height: 26px" @click="downloadFile(file)">
                          <template #icon> <DownloadOutlined class="fon-S12" /> </template>
                        </a-button>
                      </div>
                    </div>
                  </template>
                </div>
                <a-empty v-else />
              </div>
              <div v-else class="f-xy-center fon-S16 fon-W600 color-333">节点未补充消息</div>
            </div>
            <div class="flex f-column pad-Y36 f-xy-center" style="width: 20%">
              <div class="f-1" style="width: 2px; background-color: rgb(0, 182, 55)"></div>
              <div class="num mar-12 fon-W600 border-Rall f-xy-center">{{ index + 1 }}</div>
              <div class="f-1" style="width: 2px; background-color: rgb(0, 182, 55)"></div>
            </div>
          </div>
        </template>
      </div>
    </div>
  </a-modal>
</template>

<script setup>
import { ref, watch, nextTick, computed } from 'vue'
import { getPumpHouseFileList } from '@/services/modules/map'
import { UploadOutlined, DownloadOutlined, FolderViewOutlined, UpCircleOutlined, DownCircleOutlined } from '@ant-design/icons-vue'
import debounce from 'lodash/debounce'
import pumpHouseKey from '@/assets/geojson/pump_house.json'

import { PumpHouseApi } from '@/services/modules/pump.house'
import { CommonApi } from '@/services/modules/common'

const open = defineModel()
const fileModal = ref(false)
const props = defineProps({ data: Object })
const emit = defineEmits(['openUpdateModal'])
const scrollRef = ref(null)

function openUpdateModalFn() {
  emit('openUpdateModal')
  open.value = false
}

const pumpHouseNodeValue = ref([])
const batchKeys = ref([]) //泵房批次
async function queryDictionaries() {
  const { data: pumpHouseBatch } = await CommonApi.queryDictionaries('pumpHouseBatch')
  const { data: pumpHouseNode } = await CommonApi.queryDictionaries('pumpHouseNode')
  batchKeys.value = Object.fromEntries(pumpHouseBatch.map((item) => [item.DictCode, item.DictValue]))
  pumpHouseNodeValue.value = pumpHouseNode
}
queryDictionaries()

// 获取已有的节点列表
async function pumpHouseNodeList(PumpRoomNumber) {
  const { data } = await PumpHouseApi.nodeList(PumpRoomNumber)
  // console.log(data)
}
watch(
  () => props.data,
  (val) => {
    nextTick(() => {
      pumpHouseNodeList(val.PumpRoomNumber)
      handlerNodeClick(val.CurrentNode)
      scrollRef.value.scrollTo(0, val.CurrentNode * 202)
      // fileList.value = {}
      // pumpHouseFileList()
    })
  }
)

// 文件列表
const fileList = ref({})
async function pumpHouseFileList() {
  try {
  } catch (error) {}
  const { code, data } = await getPumpHouseFileList(props.data.pumpRoomNumber)
  if (code == 200) {
    fileList.value = data
  } else {
    fileList.value = {}
  }
}

// 下载文件
async function downloadFile(file) {
  const filePath = file.Path
  // const filePath = file.Path.replace('https://www.szwgft.cn/nodeServer', 'http://10.1.60.61:3001')

  try {
    const E = ['.png', '.jpeg', '.jpg', '.gif', '.pdf', '.xlsx', '.docx', '.doc', '.pptx', '.ppt', '.txt', 'webp', '.mp4', '.mp3']
    const isImage = E.find((i) => filePath.includes(i))
    if (isImage) {
      const response = await fetch(filePath)
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }
      const blob = await response.blob()
      const url = URL.createObjectURL(blob)
      const a = document.createElement('a')
      a.href = url
      a.download = file.FileKey
      document.body.appendChild(a)
      a.click()
      document.body.removeChild(a)
      URL.revokeObjectURL(url) // 清理 URL 对象
    } else {
      window.location.href = filePath
    }
  } catch (error) {
    // console.error('Error downloading image:', error)
    //  在此处处理错误，例如向用户显示错误消息。
  }
}

const nowIndex = ref(0)
function scrollEnd(e) {
  const children = Array.from(e.target.children)
  nowIndex.value = children.findIndex((i) => i.offsetTop === e.srcElement.scrollTop)
}

function scrollToTop(val) {
  nowIndex.value = val ? nowIndex.value + 1 : nowIndex.value - 1

  scrollRef.value.scrollTop = nowIndex.value * 202
}
watch(nowIndex, (val) => handlerNodeClick(val + 1))

const pumpHouseNodeDetail = ref(null)
const pumpHouseNodeFileKeyMap = ref(new Map())
const pumpHouseNodeFileKey = computed(() => pumpHouseNodeFileKeyMap.value.get(nowIndex.value + 1))

async function handlerNodeClick(nodeNum) {
  pumpHouseNodeDetail.value = null

  if (!pumpHouseNodeFileKeyMap.value.has(nodeNum)) {
    const { data: FileKey } = await CommonApi.queryDictionaries(`pumpHouseNodeFile_${nodeNum}`)
    pumpHouseNodeFileKeyMap.value.set(nodeNum, FileKey)
  }
  const { data } = await PumpHouseApi.nodeDetail(props.data.PumpRoomNumber, { Node: nodeNum })
  if (data) {
    if (data.Files) {
      const keys = Object.fromEntries(pumpHouseNodeFileKeyMap.value.get(nodeNum).map(({ DictCode, DictValue }) => [DictCode, DictValue]))
      data.Files = JSON.parse(data.Files).map((item) => {
        item.FileKey = keys[item.FileType]
        return item
      })
    } else {
      data.Files = []
    }
  }
  pumpHouseNodeDetail.value = data
}
</script>

<style lang="less" scoped>
.wire {
  display: flex;
  justify-content: center;
  align-items: center;
  & > span {
    margin: 0 10px;
    font-size: 16px;
    font-weight: 600;
  }
  & > div {
    flex: 1;
    background-color: #666;
    height: 1px;
  }
}

:deep(.slick-track) {
  width: 100% !important;
  height: 100% !important;
  transform: none !important;
}

:deep(.slick-slide) {
  text-align: center;
  overflow: hidden;
}

:deep(.slick-arrow.custom-slick-arrow) {
  width: 25px;
  height: 25px;
  font-size: 25px;
  color: #000;
  z-index: 1;
}
:deep(.slick-arrow.custom-slick-arrow:before) {
  display: none;
}

.content_box {
  width: 100%;
  height: 200px;
  overflow-y: scroll;
  scroll-snap-type: y mandatory;
  transition: scrollTop 0.5s ease;
  .item {
    height: 100%;
    background: #eee;
    margin-bottom: 2px;
    border-radius: 8px;
    scroll-snap-align: center;
    scroll-snap-stop: always;
    .title {
      color: #1677ff;
    }
    .num {
      color: rgb(0, 182, 55);
      border: 2px solid rgb(0, 182, 55);
      width: 26px;
      height: 26px;
    }
  }
}
</style>
