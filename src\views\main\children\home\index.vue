<template>
  <div class="all" ref="mapboxRef">
    <MapGlS :options @load="mapLoad">
      <template #rightTop>
        <div class="mar-30 flex">
          <!-- <div class="icon_box color-333 box-shadow f-xy-center f-column" @click="drawerStatisticsOpen = true"><PieChartOutlined style="font-size: 24px" /></div> -->
          <div class="icon_box color-333 box-shadow f-xy-center f-column" @click="restore">{{ is3D ? '2D' : '3D' }}</div>
          <MapInputSearch @handlerZoneClick="handlerClick" />
        </div>
      </template>
      <template #leftTop> </template>

      <template #leftBottom>
        <!-- 图层控制 -->
        <ControllerLayer @change="changeLayer" />
      </template>
    </MapGlS>

    <!-- 小区信息 -->
    <GeneralSituation :detail v-model="fold" v-show="detail" />
    <!-- 二供弹窗 -->
    <ScheduleMutual v-model="schedule.open" :data="schedule.data" @openUpdateModal="updateOpen = true" />
  </div>

  <a-drawer v-model:open="drawerStatisticsOpen" :maskStyle="{ background: 'transparent' }" width="600px" title="供排水设施统计" placement="right">
    <NeighbourStatistics v-if="drawerStatisticsOpen" />
  </a-drawer>
</template>

<script setup>
import { ref, watch, onBeforeUnmount, reactive } from 'vue'
import MapGlS from '@/components/MapGlS2/index.vue'
import MapInputSearch from '@/components/MapInputSearch/index.vue'
import { CompassOutlined, PieChartOutlined } from '@ant-design/icons-vue'
import GeneralSituation from './components/GeneralSituation.vue'
import { getZoneData } from '@/services/modules/map'
import { message } from 'ant-design-vue'
import { FT_ZONE } from '@/ownedByAllMapConfig'
import mapboxgl from 'mapbox-gl'
import { Threebox, THREE } from 'threebox-plugin'
import proj4 from 'proj4'
import { getSecondaryWaterProgress, getPumpHouseNameLikeApi } from '@/services/modules/map'
import { getalveVerificationListApi } from '@/services/modules/home.js'

import { transitionComponent } from '@/utils/transitionDom.js'
import ModelPopup from './components/ModelPopup/index.vue'
import NeighbourStatistics from './components/NeighbourStatistics.vue'
import { CanalGeometry } from '@/utils/CanalGeometry'
import { transitionArr } from '@/utils'
import { PumpHouseApi } from '@/services/modules/pump.house'
import ScheduleMutual from './components/ScheduleMutual.vue'
import { storeToRefs } from 'pinia'

import icon0 from '@/assets/images/异常.png'
import icon1 from '@/assets/images/临供.png'
import icon2 from '@/assets/images/切换.png'
import icon3 from '@/assets/images/初验.png'
import icon4 from '@/assets/images/其他.png'

import icongis from '@/assets/images/031.png'
import icongis2 from '@/assets/images/011.png'

import clique1 from '@/assets/images/icon1.png'
import clique2 from '@/assets/images/icon2.png'

// import Stats from 'three/addons/libs/stats.module.js'

// import { MapboxOverlay } from '@deck.gl/mapbox'
// import { TripsLayer } from '@deck.gl/geo-layers'
// import { animate } from 'popmotion'
import ControllerLayer from './components/ControllerLayer.vue'

import bz from '@/assets/images/bz.webp'
import hz from '@/assets/images/hz.webp'
import sn from '@/assets/images/sn.png'
import ys from '@/assets/images/ys.webp'
import ws from '@/assets/images/ws.webp'

import PumpHousePopup from './components/ModelPopup/PumpHousePopup.vue'
import MaintainPopup from './components/ModelPopup/MaintainPopup.vue'
import CliquePopup from './components/ModelPopup/CliquePopup.vue'
import useDailyOperation from '@/store/dailyOperation'

// const stats = new Stats()
// document.body.appendChild(stats.domElement)

const zoneColor = {
  梅林片区: '#1677ff',
  景田片区: 'red',
  香蜜片区: '#fa9600',
  福东南片区: '#0000FF',
  福东北片区: '#ffd447',
  莲花片区: '#bdfb9b',
  中心城片区: '#ffb4a6',
  福保片区: '#9f4fff',
  福民片区: '#00ffff',
  新洲片区: '#00ff00'
}
const tenGridColor = {
  '福中-1': '#999999',
  '福中-2': '#947dd2',
  '福中-3': '#38056c',
  '福东-1': '#ffc300',
  '福东-2': '#1677ff',
  '梅林-2': '#8d0e8d',
  '梅林-1': '#cfc00e',
  '香蜜-3': '#37840b',
  '香蜜-2': '#900C3F',
  '香蜜-1': '#0B7784'
}
const streetColor = {
  梅林街道办: '#999999',
  莲花街道办: '#947dd2',
  香蜜湖街道办: '#38056c',
  沙头街道办: '#ffc300',
  福保街道办: '#1677ff',
  福田街道办: '#8d0e8d',
  南园街道办: '#cfc00e',
  华强北街道办: '#37840b',
  华富街道办: '#900C3F',
  园岭街道办: '#0B7784'
}

const isDraw3DPumpHouse = ref(false)
const isDraw2DPumpHouse = ref(false)
const pumpHousePopupComponent = transitionComponent(PumpHousePopup)
const maintainPopupComponent = transitionComponent(MaintainPopup)
const cliquePopupComponent = transitionComponent(CliquePopup)
const schedule = reactive({ open: false, data: null })
const drawerStatisticsOpen = ref(false)

const { getGisFaults } = useDailyOperation()
const { gisFaults } = storeToRefs(useDailyOperation())

const options = {
  map: {
    pitch: 60,
    zoom: 13.4,
    center: [114.05017285035768, 22.53537772822048]
  },
  sources: {
    // 福田无缝区块
    // 片区图层
    FT_ModificationWorks: {
      type: 'vector',
      scheme: 'tms',
      tiles: ['https://www.szwgft.cn/mapServer/geoserver/gwc/service/tms/1.0.0/Code%3AFT_ModificationWorks@EPSG%3A3857@pbf/{z}/{x}/{y}.pbf'],
      layers: {
        FTModificationWorks_fill: {
          options: {
            'source-layer': 'FT_ModificationWorks',
            layout: { visibility: 'none' },
            paint: {
              'fill-opacity': 0.35,
              'fill-outline-color': '#000000',
              'fill-color': ['match', ['get', 'AreaName'], ...transitionArr(zoneColor, 'pink')]
            }
          }
        },
        FTModificationWorks_line: {
          options: { 'source-layer': 'FT_ModificationWorks', paint: { 'line-width': 1, 'line-color': '#000' }, layout: { visibility: 'none' } }
        },
        FTModificationWorks_symbol: {
          options: {
            'source-layer': 'FT_ModificationWorks',
            layout: { 'text-field': ['get', 'AreaName'], 'text-anchor': 'center', 'text-size': 22, visibility: 'none' },
            paint: { 'text-color': '#c500ff', 'text-halo-color': 'white', 'text-halo-width': 1 }
          }
        }
      }
    },
    // 十网格图层
    tenGrid: {
      type: 'vector',
      scheme: 'tms',
      tiles: ['https://www.szwgft.cn/mapServer/geoserver/gwc/service/tms/1.0.0/Code%3AtenGrid@EPSG%3A3857@pbf/{z}/{x}/{y}.pbf'],
      layers: {
        tenGrid_fill: {
          options: {
            'source-layer': 'tenGrid',
            layout: { visibility: 'none' },
            paint: {
              'fill-opacity': 0.5,

              'fill-outline-color': '#000000',
              'fill-color': ['match', ['get', 'name'], ...transitionArr(tenGridColor, 'pink')]
            }
          }
        },
        tenGrid_line: {
          options: { 'source-layer': 'tenGrid', layout: { visibility: 'none' }, paint: { 'line-width': 1, 'line-color': '#000' } }
        },
        tenGrid_symbol: {
          options: {
            'source-layer': 'tenGrid',
            layout: { 'text-field': ['get', 'name'], 'text-anchor': 'center', 'text-size': 22, visibility: 'none' },
            paint: { 'text-color': '#c500ff', 'text-halo-color': 'white', 'text-halo-width': 1 }
          }
        }
      }
    },
    // 街道图层
    FT_street: {
      type: 'vector',
      scheme: 'tms',
      tiles: ['https://www.szwgft.cn/mapServer/geoserver/gwc/service/tms/1.0.0/Code%3AFT_street@EPSG%3A3857@pbf/{z}/{x}/{y}.pbf'],
      layers: {
        FTstreet_fill: {
          options: {
            'source-layer': 'FT_street',
            paint: {
              'fill-opacity': 0.5,
              'fill-outline-color': '#000000',
              'fill-color': ['match', ['get', 'name'], ...transitionArr(streetColor, 'pink')]
            },
            layout: { visibility: 'none' }
          }
        },
        FTstreet_line: {
          options: { 'source-layer': 'FT_street', paint: { 'line-width': 1, 'line-color': '#000' }, layout: { visibility: 'none' } }
        },
        FTstreet_symbol: {
          options: {
            'source-layer': 'FT_street',
            layout: { 'text-field': ['get', 'name'], 'text-anchor': 'center', 'text-size': 22, visibility: 'none' },
            paint: { 'text-color': '#c500ff', 'text-halo-color': 'white', 'text-halo-width': 1 }
          }
        }
      }
    },
    FT_ZONE,
    // 排水管
    tubeLine: {
      type: 'vector',
      scheme: 'tms',
      tiles: ['https://www.szwgft.cn/mapServer/geoserver/gwc/service/tms/1.0.0/Code%3APS_PIPE@EPSG%3A3857@pbf/{z}/{x}/{y}.pbf'],
      layers: {
        tubeLayer_line: {
          options: {
            minzoom: 15,
            'source-layer': 'PS_PIPE',
            paint: { 'line-color': '#000000', 'line-width': 2, 'line-opacity': 0 },
            layout: { visibility: 'none' }
          }
        }
      }
    },
    // 排水渠
    canalLine: {
      type: 'vector',
      scheme: 'tms',
      tiles: ['https://www.szwgft.cn/mapServer/geoserver/gwc/service/tms/1.0.0/Code%3APS_CONDUIT@EPSG%3A3857@pbf/{z}/{x}/{y}.pbf'],
      layers: {
        canalLayer_line: {
          options: {
            minzoom: 15,
            'source-layer': 'PS_CONDUIT',
            paint: { 'line-color': '#ff00ff', 'line-width': 2, 'line-opacity': 0 },
            layout: { visibility: 'none' }
          }
        }
      }
    },
    // 给水管
    feedwater: {
      type: 'vector',
      scheme: 'tms',
      tiles: ['https://www.szwgft.cn/mapServer/geoserver/gwc/service/tms/1.0.0/Code%3ASWAT_PIPE@EPSG%3A3857@pbf/{z}/{x}/{y}.pbf'],
      layers: {
        feedwaterLayer_line: {
          options: {
            minzoom: 15,
            'source-layer': 'SWAT_PIPE',
            layout: { visibility: 'none' },
            paint: { 'line-color': '#00ffff', 'line-width': 2, 'line-opacity': 0 },
            layout: { visibility: 'none' }
          }
        }
      }
    },
    // 雨污井
    well: {
      type: 'vector',
      scheme: 'tms',
      tiles: ['https://www.szwgft.cn/mapServer/geoserver/gwc/service/tms/1.0.0/Code%3AWELL@EPSG%3A3857@pbf/{z}/{x}/{y}.pbf'],
      layers: {
        well_circle: {
          options: {
            minzoom: 15,
            'source-layer': 'WELL',
            paint: {
              'circle-radius': 5,
              'circle-color': '#ff0000',
              'circle-opacity': 0
            },
            layout: { visibility: 'none' }
          }
        }
      }
    },
    // 雨水箅子
    grate: {
      type: 'vector',
      scheme: 'tms',
      tiles: ['https://www.szwgft.cn/mapServer/geoserver/gwc/service/tms/1.0.0/Code%3ARAINSTR@EPSG%3A3857@pbf/{z}/{x}/{y}.pbf'],
      layers: {
        grate_circle: {
          options: {
            minzoom: 15,
            'source-layer': 'RAINSTR',
            paint: {
              'circle-radius': 5,
              'circle-color': '#0000ff',
              'circle-opacity': 0
            },
            layout: { visibility: 'none' }
          }
        }
      }
    },
    // 建筑白模
    whiteModel: {
      type: 'vector',
      scheme: 'tms',
      tiles: ['https://www.szwgft.cn/mapServer/geoserver/gwc/service/tms/1.0.0/Code%3Afutianjianzhu@EPSG%3A3857@pbf/{z}/{x}/{y}.pbf'],
      layers: {
        'zone_fill-extrusion': {
          above: 'zone_symbol',
          events: { click: architectureLayerClick },
          options: {
            'source-layer': 'futianjianzhu',
            paint: {
              'fill-extrusion-vertical-gradient': true,
              'fill-extrusion-height': ['get', 'Z_Max'],
              'fill-extrusion-base': 0,
              'fill-extrusion-opacity': 1,
              'fill-extrusion-color': '#fff'
            }
          }
        }
      }
    },
    // 二供泵房
    pumpHouse: {
      type: 'geojson',
      data: { type: 'FeatureCollection', features: [] },
      layers: {
        dot_symbol: {
          events: {
            click: (e, feature) => {
              schedule.open = true
              schedule.data = feature.properties
            },
            mouseenter: (e, feature, map) => {
              if (isDraw2DPumpHouse.value) {
                map.popup = new mapboxgl.Popup({ maxWidth: 'none', offset: [0, -30] })
                  .setLngLat([feature.properties.X, feature.properties.Y])
                  .setDOMContent(pumpHousePopupComponent({ data: feature.properties }))
                  .addTo(map)
              }
            },
            mouseleave: (e, feature, map) => map.popup.remove()
          },
          images: { 已改造: icon2, 已达标: icon3, 未改造: icon1, 未达标: icon0, 正在改造: icon4 },
          options: {
            layout: {
              'icon-image': ['get', 'RemouldState'],
              'icon-size': 0.001,
              'icon-allow-overlap': true
            }
          }
        }
      }
    },
    // 日常运维
    maintain: {
      type: 'geojson',
      data: { type: 'FeatureCollection', features: [] },
      layers: {
        maintain_symbol: {
          images: { 已处理: icongis, 未处理: icongis2 },
          events: {
            mouseenter: (e, feature, map) => {
              map.popup2 = new mapboxgl.Popup({ maxWidth: 'none', offset: [0, -30] })
                .setLngLat([feature.properties.x, feature.properties.y])
                .setDOMContent(maintainPopupComponent({ data: feature.properties }))
                .addTo(map)
            },
            mouseleave: (e, feature, map) => map.popup2.remove()
          },
          options: {
            layout: {
              'icon-image': ['get', 'state'],
              'icon-size': 0.12,
              'icon-offset': [0, -100],
              'icon-allow-overlap': true,
              visibility: 'none'
            }
          }
        }
      }
    },
    // 总阀总表
    clique: {
      type: 'geojson',
      data: { type: 'FeatureCollection', features: [] },
      layers: {
        clique_symbol: {
          images: { 总表: clique2, 总阀: clique1 },
          options: {
            layout: {
              'icon-image': ['get', 'point_Type'], //设置图片
              'icon-size': 0.3, //设置图片大小
              // 'icon-offset': [10, 5] //设置偏移
              visibility: 'none'
            }
          },
          events: {
            mouseenter: (e, feature, map) => {
              const { properties } = feature
              map.popup3 = new mapboxgl.Popup({ maxWidth: 'none', offset: [0, -20] })
                .setLngLat([properties.x, properties.y])
                .setDOMContent(cliquePopupComponent({ data: properties }))
                .addTo(map)
            },
            mouseleave: (e, feature, map) => map.popup3.remove()
          }
        }
      }
    }
  }
}
const fold = ref(true)
const mapboxRef = ref(null) // 地图容器
const SCALE = 0.93 // 模型缩放比例
let activeModel = null // 用于存储选中的模型
// let flowDirectionLayter //流向图
// let animation //动画
let myMap // 地图实例
let is3D = ref(true) // 是否是3D模式
const detail = ref(null) // 区块详情

watch(gisFaults, (val) => {
  myMap.getSource('maintain').setData({ type: 'FeatureCollection', features: val })
})

function mapLoad(map) {
  myMap = map
  // 添加自定义图层
  myMap.addLayer(
    {
      id: 'custom_layer',
      type: 'custom',
      renderingMode: '3d',
      onAdd: (map, mbxContext) => {
        window.tb = new Threebox(map, mbxContext, { defaultLights: true, enableSelectingObjects: true, enableTooltips: true }) // 创建Threebox实例
        // 添加自定义光源
        const directionalLight = new THREE.DirectionalLight(0xffffff, 1.2) // 白色，强度为 1.5 (调整此值)
        directionalLight.castShadow = true // 启用阴影
        directionalLight.position.set(150, 150, 250) // 设置光源位置 (调整此值)
        tb.scene.add(directionalLight)
        tb.renderer.shadowMap.enabled = true // 启用阴影
        tb.renderer.shadowMap.type = THREE.PCFSoftShadowMap // 设置阴影类型
      },
      render: (gl, matrix) => tb.update()
    },
    'zone_fill-extrusion'
  )

  // 添加流向图
  // flowDirectionLayter = new MapboxOverlay({}) //创建图层
  addPumpHouse() // 添加泵房图层数据
  getGisFaults() // 获取日常维护数据
  getFieldWorkDots() //总阀总表数据

  // myMap.addControl(flowDirectionLayter) //添加流向图层
  myMap.on('mousemove', modelMousemoveCallback) // 模型添加事件
  myMap.on('moveend', moveendCallback) // 地图操作结束
  myMap.on('click', modelClickCallback) // 模型添加点击事件
  // window.addEventListener('resize', resizeCallback) // 窗口添加resize事件

  function animation() {
    historyTubeModleMap.forEach((tube) => tube.forEach((i) => i.flow()))
    myMap.triggerRepaint()
    // stats.update()
    requestAnimationFrame(animation)
  }
  animation()
}

onBeforeUnmount(() => {
  myMap.off('mousemove', modelMousemoveCallback)
  myMap.off('moveend', moveendCallback)
  myMap.off('click', modelClickCallback)

  tb.dispose()
  // animation?.stop()
})

// 点击图层事件
async function architectureLayerClick(e, f, map) {
  try {
    const center = f.geometry.coordinates[0][0]
    myMap.setPaintProperty('zone_fill-extrusion', 'fill-extrusion-color', ['match', ['get', 'Zone_Code'], f.properties.Zone_Code, '#7fb192', '#fff'])
    // myMap.flyTo({ center, zoom: 17.5, bearing: 0, pitch: 60, duration: 3000, speed: 1.8 })
    myMap.flyTo({ center, zoom: 17, curve: 1.5, duration: 2500 })
    // MapFlyTo(map, center)
    gainZoneData(f.properties.Zone_Code)
  } catch (error) {
    message.error('查询失败')
  }
}

function MapFlyTo(map, center) {
  map.flyTo({ center, zoom: 17.5, bearing: 0, pitch: 60, duration: 3000, speed: 1.8, curve: 2 })
}

// 恢复到初始视角
function restore() {
  let pitch = is3D.value ? 0 : 60
  let zoom = is3D.value ? 13.4 : 17.1
  is3D.value = !is3D.value
  // fold.value = true
  myMap.flyTo({ pitch, zoom, duration: 1500 })
}

// 搜索列表点击处理
function handlerClick(e) {
  myMap.setPaintProperty('zone_fill-extrusion', 'fill-extrusion-color', ['match', ['get', 'Zone_Code'], e.Zone_Code, '#7fb192', '#fff'])
  MapFlyTo(myMap, e.Center_Point.split(','))
  gainZoneData(e.Zone_Code)
  fold.value = false
}

// 获取区块需求
async function gainZoneData(code) {
  try {
    const res = await getZoneData(code)
    if (res.data) {
      const [data] = JSON.parse(res.data)
      detail.value = data
      fold.value = false
    }
  } catch (error) {
    message.error('区块信息查询失败')
  }
}

// 处理模型事件
function modelMousemoveCallback(e) {
  const [intersects] = tb.queryRenderedFeatures(e.point)
  if (intersects) {
    const model = intersects.object // 获取相交模型
    if (model.userData?.type !== 'tube') return
    if (activeModel === model) return // 判断模型是否已经被选中
    if (activeModel) {
      activeModel.material.color.set(activeModel.userData.color ?? '#BABCBE') // 恢复上一个模型的颜色
    }
    activeModel = model // 设置当前选中模型

    model.material.color.set('#00FF0A')
  } else {
    if (!activeModel) return
    activeModel.material.color.set(activeModel.userData.color ?? '#BABCBE')
    activeModel = null
  }
}

const component = transitionComponent(ModelPopup)
// 处理模型点击事件
function modelClickCallback(e) {
  const [intersects] = tb.queryRenderedFeatures(e.point)
  if (intersects) {
    const model = intersects.object // 获取相交模型
    const { center, data, type } = model.userData
    const { lng, lat } = e.lngLat
    const Popup = new mapboxgl.Popup({ offset: [0, -10], maxWidth: 'none', closeOnMove: true }) //弹窗

    if (data) {
      Popup.setLngLat([lng, lat]).setDOMContent(component({ data, type })).addTo(myMap)
    } else {
      const { data } = tb.findParent3DObject(intersects)

      const res = JSON.parse(data.data.Id)

      Popup.setLngLat([lng, lat])
        .setDOMContent(component({ data: res, type: data.type }))
        .addTo(myMap)
    }
  }
}

// 地图操作结束回调
function moveendCallback() {
  const zoom = myMap.getZoom() // 获取地图缩放等级

  if (zoom > 17 && myMap.getPitch() > 0) {
    is3D.value = true
  }
  if (zoom < 17 && myMap.getPitch() === 0) {
    is3D.value = false
  }

  const differenceValue = myMap.getPitch() - 60

  const width = 800
  const height = differenceValue <= 0 ? 400 : 300
  const point = myMap.project(myMap.getCenter())
  const scope = [
    [point.x - width, point.y - height],
    [point.x + width, point.y + height]
  ]

  if (zoom > 17.4) {
    const drainAwayWaterFeatures = myMap.queryRenderedFeatures(scope, { layers: ['tubeLayer_line', 'canalLayer_line'] }) // 管道图层数据
    const feedwaterFeatures = myMap.queryRenderedFeatures(scope, { layers: ['feedwaterLayer_line'] }) // 管道图层数据
    const wellFeatures = myMap.queryRenderedFeatures(scope, { layers: ['well_circle', 'grate_circle'] }) // 雨污井图层数据
    const pumpHouseFeatures = myMap.queryRenderedFeatures(scope, { layers: ['dot_symbol'] }) // 泵房图层数据

    addDrainAwayWaterTubeModel(drainAwayWaterFeatures) // 添加管道模型
    addFeedwaterTubeModel(feedwaterFeatures) // 添加给水管道模型
    drawWell(wellFeatures) // 添加井模型
    if (isDraw3DPumpHouse.value) addPumpHouseModel(pumpHouseFeatures) // 添加泵房模型

    // 清除流向图层动画
    // if (animation) {
    //   animation.stop()
    //   animation = null
    // }

    // 添加流向图层动画
    // animation = animate({
    //   from: 0,
    //   to: 4000,
    //   duration: 4000,
    //   repeat: Infinity,
    //   onUpdate: (e) => {
    //     const option = {
    //       id: 'TripsLayer',
    //       data: tubeFeatures,
    //       getPath: (d) => coordinateStitching(d.properties),
    //       getTimestamps: (d) => {
    //         const length = coordinateStitching(d.properties).length
    //         const times = []
    //         for (let i = 0; i < length; i++) {
    //           const item = 4000 / (length - 1)
    //           times.push(item * i)
    //         }
    //         return times
    //       },
    //       getColor: (d) => [0, 240, 255],
    //       getWidth: (d) => d.properties.DIAMETER / 600,
    //       currentTime: e,
    //       trailLength: 600,
    //       capRounded: true,
    //       jointRounded: true,
    //       fadeTrail: true, //路径是否淡出
    //       trailLength: 800, //路径完全淡出需要多长时间
    //       shadowEnabled: true
    //     }

    //     flowDirectionLayter.setProps({ layers: [new TripsLayer(option)] })
    //   }
    // })
  } else {
    // animation?.stop()
    // animation = null
    // 清除流向图
    // flowDirectionLayter?.setProps({ layers: [] })
    // 清除所有模型
    const [models] = tb.scene.children ?? []
    models.children.forEach((i) => setTimeout(() => tb.remove(i), 0))
    historyTubeModleMap.clear() // 清除所有管道模型历史记录
    historyWellModleMap.clear() // 清除所有井模型历史记录
    historyFeedwaterTubeModleMap.clear() // 清除所有进水管道模型历史记录
    historyPumpHouseModleMap.clear() // 清除所有泵房模型历史记录
  }
}

let historyTubeModleMap = new Map() // 历史管道模型
// 添加管道模型
function addDrainAwayWaterTubeModel(features) {
  const length = features.length

  let tubeMap = new Map()

  for (let i = 0; i < length; i++) {
    const { UPBTMLEVEL, DNBTMLEVEL, DIAMETER, SUBTYPE, WIDTH, FEATID } = features[i].properties
    //判断是否已经添加过
    if (!tubeMap.has(FEATID)) {
      //判断是否已渲染
      if (!historyTubeModleMap.has(FEATID)) {
        const EPSG4326PATHS = coordinateStitching(features[i].properties) //拆分EPSG:4547坐标
        const color = WIDTH !== null && WIDTH !== void 0 ? '#1f1f1f' : SUBTYPE == '污水管' ? '#ff0000' : '#0000ff' //定义元素颜色
        // 计算半径
        let radius
        if (WIDTH !== null && WIDTH !== void 0) {
          radius = WIDTH === 0 ? 1000 / 1000 / 2 : WIDTH / 2000 / 2
        } else {
          radius = DIAMETER / 1000 / 2
        }

        const models = []
        for (let j = 0; j < EPSG4326PATHS.length - 1; j++) {
          const h = j === EPSG4326PATHS.length - 1 ? DNBTMLEVEL : UPBTMLEVEL
          const [pointStart, pointEnd] = [EPSG4326PATHS[j], EPSG4326PATHS[j + 1]]
          const start = proj4('EPSG:4326', 'EPSG:3857', [...pointStart, UPBTMLEVEL])
          const end = proj4('EPSG:4326', 'EPSG:3857', [...pointEnd, h])
          const modelStart = [0, 0, start[2] * SCALE] // 计算模型放置的偏移量
          const modelEnd = [(end[0] - start[0]) * SCALE, (end[1] - start[1]) * SCALE, end[2] * SCALE] // 计算模型放置的偏移量

          const material = new THREE.MeshPhongMaterial({ color, shininess: 100, side: THREE.DoubleSide }) // 使用MeshPhongMaterial 可以设置高光

          let uniforms = {
            totalLength: { value: 2 },
            pipeFittingAt: { value: 2 },
            pipeFittingWidth: { value: 0.5 },
            pipeFittingColor: { value: new THREE.Color('#00d6e1') }
          }

          material.onBeforeCompile = (shader) => {
            shader.uniforms.totalLength = uniforms.totalLength
            shader.uniforms.pipeFittingAt = uniforms.pipeFittingAt
            shader.uniforms.pipeFittingWidth = uniforms.pipeFittingWidth
            shader.uniforms.pipeFittingColor = uniforms.pipeFittingColor
            shader.fragmentShader = `
            #define S(a, b, c) smoothstep(a, b, c)
            uniform float totalLength;
            uniform float pipeFittingAt;
            uniform float pipeFittingWidth;
            uniform vec3 pipeFittingColor;
            ${shader.fragmentShader}
            `.replace(
              `#include <color_fragment>`,
              `#include <color_fragment>
            float normAt = pipeFittingAt / totalLength;
            float normWidth = pipeFittingWidth / totalLength;
            float hWidth = normWidth * 0.5;
            float fw = fwidth(vUv.x);
            float f = S(hWidth + fw, hWidth, abs(vUv.x - normAt));
            diffuseColor.rgb = mix(diffuseColor.rgb, pipeFittingColor, f);
            // diffuseColor.rgb = mix(diffuseColor.rgb, vec3(1, 1, 0), S(fw,  0., abs(vUv.x - normAt)));
          `
            )
          }
          material.defines = { USE_UV: '' }
          const center = [(pointStart[0] + pointEnd[0]) / 2, (pointStart[1] + pointEnd[1]) / 2, (UPBTMLEVEL + DNBTMLEVEL) / 2 - radius] // 计算模型放置的中心点
          const options = { geometry: [modelStart, modelEnd], units: 'meters', radius, sides: 12, material, opacity: 1, anchor: 'center' }
          const tube = tb.tube(options)
          tube.userData.obj.userData = { color, id: i, data: features[i].properties, center, type: 'tube' } // 为管道模型添加数据
          tube.setCoords(center) // 设置模型坐标
          const clock = new THREE.Clock()
          tube.flow = () => {
            uniforms.pipeFittingAt.value -= clock.getDelta()
            if (uniforms.pipeFittingAt.value <= -1) {
              uniforms.pipeFittingAt.value = uniforms.totalLength.value
            }
          }
          tb.add(tube)
          models.push(tube)
        }

        tubeMap.set(FEATID, models)
      } else {
        historyTubeModleMap.get(FEATID)
        tubeMap.set(FEATID, historyTubeModleMap.get(FEATID))
        historyTubeModleMap.delete(FEATID)
      }
    }
  }

  historyTubeModleMap.forEach((item) => item.forEach((i) => setTimeout(() => tb.remove(i), 0))) // 清除历史数据
  historyTubeModleMap.clear()

  tubeMap.forEach((item, key) => historyTubeModleMap.set(key, item)) // 保存历史数据
  tubeMap.clear()
  tubeMap = null
}

let historyFeedwaterTubeModleMap = new Map() // 历史管道模型
// 添加给水管道模型
function addFeedwaterTubeModel(features) {
  const length = features.length
  let tubeMap = new Map() // 管道模型
  for (let i = 0; i < length; i++) {
    const item = features[i]
    const {
      FEATID, // 管段ID
      DIAMETER, //管径
      START_ELE, //起点地面标高
      START_BUR, //起点埋深
      START_TOP_, //起点顶部标高
      END_ELE, //终点地面标高
      END_BUR, //终点埋深
      END_TOP_H //终点顶部标高
    } = item.properties
    //判断是否已经添加过
    if (!tubeMap.has(FEATID)) {
      //判断是否已渲染
      if (!historyFeedwaterTubeModleMap.has(FEATID)) {
        const models = []
        const EPSG4326PATHS = coordinateStitching(features[i].properties) //拆分EPSG:4547坐标
        const radius = DIAMETER / 1000 / 2 // 管道半径

        for (let j = 0; j < EPSG4326PATHS.length - 1; j++) {
          const h = j === EPSG4326PATHS.length - 1 ? END_BUR : START_BUR
          const [pointStart, pointEnd] = [EPSG4326PATHS[j], EPSG4326PATHS[j + 1]]
          const start = proj4('EPSG:4326', 'EPSG:3857', [...pointStart, START_BUR])
          const end = proj4('EPSG:4326', 'EPSG:3857', [...pointEnd, h])
          const modelStart = [0, 0, start[2] * SCALE] // 计算模型放置的偏移量
          const modelEnd = [(end[0] - start[0]) * SCALE, (end[1] - start[1]) * SCALE, end[2] * SCALE] // 计算模型放置的偏移量
          const material = new THREE.MeshPhongMaterial({ color: '#ffac02', shininess: 100, side: THREE.DoubleSide }) // 使用MeshPhongMaterial 可以设置高光

          const center = [(pointStart[0] + pointEnd[0]) / 2, (pointStart[1] + pointEnd[1]) / 2, (END_BUR, START_BUR) / 2 - radius] // 计算模型放置的中心点
          const options = { geometry: [modelStart, modelEnd], units: 'meters', radius, sides: 12, material, opacity: 1, anchor: 'center' }
          const tube = tb.tube(options)
          tube.userData.obj.userData = { color: '#ffac02', id: i, data: features[i].properties, center, type: 'tube' } // 为管道模型添加数据
          tube.setCoords(center) // 设置模型坐标

          tb.add(tube)
          models.push(tube)
        }
        tubeMap.set(FEATID, models)
      } else {
        historyFeedwaterTubeModleMap.get(FEATID)
        tubeMap.set(FEATID, historyFeedwaterTubeModleMap.get(FEATID))
        historyFeedwaterTubeModleMap.delete(FEATID)
      }
    }
  }
  historyFeedwaterTubeModleMap.forEach((item) => item.forEach((i) => setTimeout(() => tb.remove(i), 0))) // 清除历史数据
  historyFeedwaterTubeModleMap.clear()

  tubeMap.forEach((item, key) => historyFeedwaterTubeModleMap.set(key, item)) // 保存历史数据
  tubeMap.clear()
  tubeMap = null
}

let historyWellModleMap = new Map() // 历史井模型
// 添加井模型
function drawWell(paints) {
  let wellMap = new Map() //渲染的井模型
  const length = paints.length

  const SNMaterial = new THREE.MeshBasicMaterial({ map: new THREE.TextureLoader().load(sn) }) // 水泥材质
  const HZMaterial = new THREE.MeshBasicMaterial({ map: new THREE.TextureLoader().load(hz) }) // 红砖材质
  const YSMaterial = new THREE.MeshBasicMaterial({ map: new THREE.TextureLoader().load(ys) }) // 雨水井贴图
  const WSMaterial = new THREE.MeshBasicMaterial({ map: new THREE.TextureLoader().load(ws) }) // 污水井贴图
  const BZMaterial = new THREE.MeshBasicMaterial({ map: new THREE.TextureLoader().load(bz) }) // 箅子贴图

  for (let i = 0; i < length; i++) {
    const item = paints[i]
    const { SUBTYPE, ELEVATION, SYSTEMID, BOTTOMLEVE } = item.properties
    if (!wellMap.has(SYSTEMID)) {
      if (!historyWellModleMap.has(SYSTEMID)) {
        if (SUBTYPE == 'WS' || SUBTYPE == 'YS') {
          const wellLid = SUBTYPE == 'WS' ? WSMaterial : YSMaterial // 判断井盖类型
          // 绘制水井
          const wellHeight = Number(ELEVATION) - Number(BOTTOMLEVE) // 计算水井高度
          const placeHeight = Number(BOTTOMLEVE) // 计算水井放置高度
          const wellGeometry = new THREE.CylinderGeometry(1, 1, wellHeight, 32) // 创建圆柱体几何体
          const well = new THREE.Mesh(wellGeometry, SNMaterial) // 创建水井网格
          //水井上的圆盖添加贴图
          const circleGeometry = new THREE.CircleGeometry(1, 32) // 创建圆形几何体
          const circle = new THREE.Mesh(circleGeometry, wellLid) // 创建圆盖网格
          const h = wellHeight / 2 + wellHeight / 1000
          circle.rotation.x = Math.PI / -2 // 设置圆盖旋转角度
          circle.position.set(0, h, 0) // 设置圆盖位置
          well.add(circle) // 添加圆盖到水井上

          well.userData = { id: i, data: item.properties, type: 'well' } // 为水井添加数据
          circle.userData = { id: i, data: item.properties, ype: 'well' } // 为水井添加数据

          const wellModel = tb.Object3D({ obj: well, units: 'meters', anchor: 'center', rotation: { x: 90, y: 0, z: 0 } }).setCoords([...item.geometry.coordinates, placeHeight])
          tb.add(wellModel)
          wellMap.set(SYSTEMID, wellModel)
        } else {
          const grateHeight = Number(ELEVATION) - Number(BOTTOMLEVE) // 计算箅子高度
          const placeHeight = Number(BOTTOMLEVE) // 计算箅子放置高度

          const h = grateHeight / 2 + grateHeight / 1000
          const geometry = new THREE.BoxGeometry(2, grateHeight, 1.2)
          const grate = new THREE.Mesh(geometry, HZMaterial)
          //箅子上的圆盖添加贴图
          const circleGeometry = new THREE.BoxGeometry(2, 0, 1.2)
          const circle = new THREE.Mesh(circleGeometry, BZMaterial) // 创建圆盖网格
          circle.position.set(0, h, 0) // 设置圆盖位置
          grate.userData = { id: i, data: item.properties, type: 'well' } // 为箅子添加数据
          circle.userData = { id: i, data: item.properties, type: 'well' } // 为箅子添加数据

          grate.add(circle) // 添加圆盖到箅子上
          const grateModel = tb.Object3D({ obj: grate, units: 'meters', anchor: 'center', rotation: { x: 90, y: 0, z: 0 } }).setCoords([...item.geometry.coordinates, placeHeight])

          tb.add(grateModel)
          wellMap.set(SYSTEMID, grateModel)
        }
      } else {
        wellMap.set(SYSTEMID, historyWellModleMap.get(SYSTEMID))
        historyWellModleMap.delete(SYSTEMID)
      }
    }
  }

  historyWellModleMap.forEach((item) => setTimeout(() => tb.remove(item), 0))
  historyWellModleMap.clear()
  wellMap.forEach((item, key) => historyWellModleMap.set(key, item))
  wellMap.clear()
  wellMap = null
}

// 添加泵房图层数据
async function addPumpHouse() {
  const { data } = await PumpHouseApi.list({ all: true })
  const list = data.map((item) => {
    const state = item.ProgressStatus != '正常' || item.CurrentNode < 4 ? '异常' : item.CurrentNode < 12 ? '临供' : item.CurrentNode < 14 ? '切换' : '初验'
    const isCollect = item.AccuratePosition ? '已采集' : '未采集'
    item.Id = JSON.stringify(item)
    return {
      type: 'Feature',
      geometry: { type: 'Point', coordinates: [item.X, item.Y] },
      properties: { ...item, state, isCollect }
    }
  })
  myMap.getSource('pumpHouse').setData({ type: 'FeatureCollection', features: list })
}

async function getFieldWorkDots() {
  const { data } = await getalveVerificationListApi()
  const features = data.valveVerification.map((item, index) => ({
    type: 'Feature',
    id: index,
    properties: item,
    geometry: { type: 'Point', coordinates: [item.x, item.y] }
  }))
  myMap.getSource('clique').setData({ type: 'FeatureCollection', features })
}

let historyPumpHouseModleMap = new Map() // 历史泵房模型
// 添加泵房模型
async function addPumpHouseModel(features) {
  let pumpHouseMap = new Map() // 泵房模型
  const length = features.length
  for (let i = 0; i < length; i++) {
    const item = features[i]
    const id = item.properties.PumpRoomNumber
    if (!pumpHouseMap.has(id)) {
      if (!historyPumpHouseModleMap.has(id)) {
        const options = { obj: 'https://ft-oss-image.oss-cn-shenzhen.aliyuncs.com/oss/bengfang.glb', type: 'gltf', scale: 1.6, anchor: 'center', units: 'meters', rotation: { x: 90, y: 180, z: 0 } }
        const model = await loadObjPromise(options)
        model.setCoords(item.geometry.coordinates)
        model.data = { id: i, data: item.properties, type: 'pumpHouse' }
        tb.add(model)
        pumpHouseMap.set(id, model)
      } else {
        pumpHouseMap.set(id, historyPumpHouseModleMap.get(id))
        historyPumpHouseModleMap.delete(id)
      }
    }
  }

  historyPumpHouseModleMap.forEach((item) => setTimeout(() => tb.remove(item), 0))
  historyPumpHouseModleMap.clear()
  pumpHouseMap.forEach((item, key) => historyPumpHouseModleMap.set(key, item))
  pumpHouseMap.clear()
  pumpHouseMap = null
}

// 图层控制
function changeLayer({ info, checkedKeys }) {
  const { checked, node } = info
  const { dataRef, parent } = node

  if (dataRef.source) {
    const layers = myMap.getStyle().layers
    layers.forEach((layer) => {
      if (layer.source === dataRef.source) {
        myMap.setLayoutProperty(layer.id, 'visibility', checked ? 'visible' : 'none')
      }
    })
  }

  if (dataRef.filtration) {
    myMap.setLayoutProperty(dataRef.layerId, 'visibility', 'visible')
    const newFilter = parent.children.filter((item) => checkedKeys.includes(item.key)).map((item) => ['==', ['get', item.node.filtration], item.node.filtrationValue])
    myMap.setFilter(dataRef.layerId, ['any', ...newFilter])
    setTimeout(() => myMap.fire('moveend'), 50)
    return
  }

  // 展示图层
  if (checked) {
    if (dataRef.key === 'dot_symbol_3D') {
      isDraw3DPumpHouse.value = true
      setTimeout(() => myMap.fire('moveend'), 50)
      return
    }
    if (dataRef.key === 'dot_symbol_2D') {
      isDraw2DPumpHouse.value = true
      myMap.setLayoutProperty('dot_symbol', 'icon-size', 0.08)
      return
    }

    myMap.setLayoutProperty(dataRef.layerId, 'visibility', 'visible')
    if (dataRef.children) {
      myMap.setFilter(dataRef.layerId, null)
    }
  } else {
    if (dataRef.key === 'dot_symbol_3D') {
      isDraw3DPumpHouse.value = false
      // 清除泵房模型
      addPumpHouseModel([])
      return
    }
    if (dataRef.key === 'dot_symbol_2D') {
      isDraw2DPumpHouse.value = false
      myMap.setLayoutProperty('dot_symbol', 'icon-size', 0.001)
      return
    }
    myMap.setLayoutProperty(dataRef.layerId, 'visibility', 'none')

    // 隐藏图层
  }
  setTimeout(() => myMap.fire('moveend'), 50)
}

// 坐标拼接
function coordinateStitching(data) {
  const filteredPath = Object.entries(data).filter(([key, value]) => key.includes('XY_Coord') && value.length > 0)
  const paths = filteredPath.map(([key, value]) => value.split(';').map((i) => i.split(',').map(Number))).flat()
  return paths
}

function loadObjPromise(option) {
  return new Promise((resolve, reject) => {
    tb.loadObj(option, (model) => resolve(model))
  })
}
</script>

<style lang="less" scoped>
.icon_box {
  right: 30px;
  top: 160px;
  width: 40px;
  height: 40px;
  padding: 5px 3px;
  background-color: white;
  margin-right: 12px;
  border-radius: 6px;
  cursor: pointer;
}
</style>
