<template>
  <div class="overflow-hidden">
    <a-input-search v-model:value="search" placeholder="区块搜索" bordered enter-button size="large" @search="onSearch">
      <template #suffix>
        <a-tooltip title="清除搜索">
          <CloseOutlined v-show="search" @click="handlerClean" />
        </a-tooltip>
      </template>
    </a-input-search>

    <!-- 下拉列表 -->
    <div class="list back-white mar-T6 border-R8 overflow-auto box-shadow">
      <slot></slot>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { CloseOutlined } from '@ant-design/icons-vue'
import { message } from 'ant-design-vue'

const emit = defineEmits(['onSearch', 'empty'])
const search = ref('')

// 模糊查询区块
async function onSearch(val) {
  if (!val) return message.warning('请输入区块名称')
  emit('onSearch', val)
}
// 置空搜索框与搜索列表数据
function handlerClean() {
  search.value = ''
  emit('empty')
}
</script>

<style lang="less" scoped>
.list {
  max-height: 400px;
  width: 299px;
  transition: all 0.5s;
  &:empty {
    display: none;
  }
  & > * {
    padding: 10px 16px;
    border-bottom: 1px solid #f0f0f0;
    transition: all 0.5s;
    font-size: 14px;
    cursor: pointer;
    display: flex;
    align-items: center;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    &:hover {
      padding-left: 20px;
      background: #eeeeee;
      border-bottom: 1px solid #999;
    }
  }
}
</style>
