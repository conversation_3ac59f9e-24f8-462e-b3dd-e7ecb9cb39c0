<template>
  <!-- 重大工地 -->
  <div class="all">
    <MapGlS ref="mapComponentRef" :options :legend @choose="handlerChoose">
      <!-- <MapInputSearch class="map_search absolute" @onSearch="onSearch" @empty="zones = []">
        <template v-for="item in zones" :key="item">
          <div @click="handlerClick(item)">
            <div class="text-nowrap">{{ item }}</div>
          </div>
        </template>
      </MapInputSearch> -->
    </MapGlS>
  </div>
</template>

<script setup>
import { ref, onMounted, computed } from 'vue'
import MapGlS from '@/components/MapGlS2/index.vue'
import RANGE_DATA from '@/assets/geojson/福田水务所范围.json'
import ZONE_DATA from '@/assets/geojson/新建小区及在建工地.json'
import { message } from 'ant-design-vue'

import MapInputSearch from './components/MapInputSearch/index.vue'

const legend = computed(() => {
  const { features } = ZONE_DATA
  const obj = {}
  features.forEach((item) => {
    const type = item.properties['是否已通水']
    obj[type] ? (obj[type] += 1) : (obj[type] = 1)
  })
  return [
    { color: '#eee', label: `全部 (${features.length}) 个`, value: false },
    { color: '#0ca107', label: `已通水 (${obj['是']}) 个`, value: '是' },
    { color: '#cc0f0f', label: `未通水 (${obj['否']}) 个`, value: '否' },
    { color: '#f0f336', label: `在建工地 (${obj['工地']}) 个`, value: '工地' }
  ]
})

const options = {
  sources: {
    RANGE: {
      type: 'geojson',
      data: RANGE_DATA,
      layers: {
        RANGE_fill: {
          options: {
            paint: {
              'fill-opacity': ['case', ['boolean', ['feature-state', 'active'], false], 0, 0.35],
              'fill-color': ['match', ['get', 'NAME'], '福中水务所', '#ee0000', '福东水务所', '#bdfb9b', '梅林水务所', '#1677ff', '香蜜水务所', '#fa9600', '#999999']
            }
          }
        },
        RANGE_line: {
          options: {
            paint: {
              'line-opacity': 1,
              'line-color': '#000',
              'line-width': 1
            }
          }
        }
      }
    },
    ZONE: {
      type: 'geojson',
      data: ZONE_DATA,
      layers: {
        ZONE_fill: {
          events: {
            click: (e, f) => console.log(e, f),
            mouseleave: () => Map.setFilter('ZONE_symbol', ['==', ['get', '小区名'], '']),
            mouseenter: (e, feature) => Map.setFilter('ZONE_symbol', ['==', ['get', '小区名'], feature.properties.小区名])
          },
          options: {
            paint: {
              'fill-opacity': 0.8,
              'fill-color': ['match', ['get', '是否已通水'], '是', '#0ca107', '否', '#cc0f0f', '#f0f336']
            }
          }
        },
        ZONE_line: {
          options: {
            paint: {
              'line-opacity': 1,
              'line-color': '#000',
              'line-width': 2
            }
          }
        },
        ZONE_symbol: {
          options: {
            layout: { 'text-field': ['get', '小区名'], 'text-anchor': 'center', 'text-size': 16 },
            paint: { 'text-color': '#000', 'text-halo-color': 'white', 'text-halo-width': 2 },
            filter: ['==', ['get', '小区名'], ''],
            minzoom: 12
          }
        }
      }
    }
  }
}

const mapComponentRef = ref(null)
let Map = null

onMounted(() => setTimeout(setTimeoutCallBack, 100))

function setTimeoutCallBack() {
  Map = mapComponentRef.value.getMapInstance()
}
function handlerChoose(e) {
  const layersID = ['ZONE_fill', 'ZONE_line']
  if (e.value) {
    layersID.forEach((item) => Map.setFilter(item, ['match', ['get', '是否已通水'], e.value, true, false]))
  } else {
    layersID.forEach((item) => Map.setFilter(item, null))
  }
}

const zones = ref([])
function onSearch(value) {
  const list = ZONE_DATA.features.filter((item) => item.properties.小区名.includes(value))
  zones.value = list.map((item) => item.properties.小区名)
  if (!zones.value.length) return message.error('没有找到有效区块')
}

function handlerClick(val) {
  zones.value = []
  const layersID = ['ZONE_fill', 'ZONE_line']
  layersID.forEach((item) => Map.setFilter(item, ['match', ['get', '小区名'], val, true, false]))
}
</script>

<style lang="less" scoped>
.map_search {
  top: 30px;
  right: 40px;
  width: 300px;
}
</style>
