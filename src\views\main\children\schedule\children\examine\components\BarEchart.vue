<template>
  <div class="barEchar box-shadow border-R10"><Echert :option /></div>
</template>

<script setup>
import { computed, watch } from 'vue'
import Echert from '@/components/Echarts/index.vue'
const props = defineProps({ count: Object })

const option = computed(() => ({
  title: { text: '点子类数量统计', top: '3%', left: '4%' },
  tooltip: { trigger: 'axis', axisPointer: { type: 'shadow' } },
  grid: { left: '3%', right: '6%', bottom: '5%', top: '13%', containLabel: true },
  xAxis: { type: 'value', boundaryGap: [0, 0.01] },
  yAxis: { type: 'category', data: props.count.key },
  series: [{ label: { show: true, position: 'right', color: 'black' }, barWidth: '30%', name: '提交数', type: 'bar', data: props.count.value }]
}))
</script>

<style lang="less" scoped>
.barEchar {
  width: 100%;
  height: 300px;
  background-color: white;
}
</style>
