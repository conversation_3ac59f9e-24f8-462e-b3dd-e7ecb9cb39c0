<template>
  <div class="wrapper">
    <div class="mar-B10 fon-S18 fon-W600">图层控制</div>
    <a-tree v-model:checkedKeys="checkedKeys" defaultExpandAll checkable @check="onCheck" :tree-data="treeData" />
  </div>
</template>

<script setup>
import { ref } from 'vue'
const emit = defineEmits(['change'])

const treeData = [
  {
    title: '基础底图',
    key: 'baseLayer',
    disabled: true,
    children: [
      {
        title: '片区图层',
        key: 'FTModificationWorks_fill',
        layerId: 'FTModificationWorks_fill',
        source: 'FT_ModificationWorks'
      },
      {
        title: '网格图层',
        key: 'tenGrid_fill',
        layerId: 'tenGrid_fill',
        source: 'tenGrid'
      },
      {
        title: '街道图层',
        key: 'FTstreet_fill',
        layerId: 'FTstreet_fill',
        source: 'FT_street'
      }
    ]
  },
  {
    title: '供水图层',
    key: 'plumbing',
    disabled: true,
    children: [
      {
        title: '供水管网',
        key: 'feedwaterLayer_line',
        layerId: 'feedwaterLayer_line'
      },
      {
        title: '优饮小区',
        key: 'zone2_fill',
        layerId: 'zone2_fill'
      },
      {
        title: '居民类无管网测绘',
        key: 'zone4_fill',
        layerId: 'zone4_fill'
      },
      {
        title: '需核对小区',
        key: 'zone3_fill',
        layerId: 'zone3_fill'
      }
    ]
  }
]
const checkedKeys = ref([])

function onCheck(checkedKeys, info) {
  emit('change', { checkedKeys, info })
}
</script>

<style lang="less" scoped>
.wrapper {
  margin: 24px;
  background-color: #fff;
  padding: 10px 18px;
  border-radius: 4px;
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.5);
  :deep(.ant-tree-treenode-disabled .ant-tree-node-content-wrapper) {
    color: rgba(0, 0, 0, 0.9);
    font-weight: 600;
    cursor: pointer;
  }
  :deep(.ant-tree-checkbox-disabled) {
    display: none;
  }
}
</style>
