<template>
  <a-modal destroyOnClose :maskClosable="false" v-model:open="open" title="文件导出" :footer="null" centered @ok="open = false" width="600px" class="relative">
    <div class="overflow-auto pad-16 border-eee border-R10" style="height: 170px">
      <div class="mar-T12 flex f-y-center text-center">
        <div>导出数据类型：</div>
        <a-radio-group v-model:value="size">
          <a-radio-button value="fieldWork">外业数据</a-radio-button>
          <a-radio-button value="pointed">点线表数据</a-radio-button>
        </a-radio-group>
      </div>

      <div style="margin-top: 12px" class="text-center flex f-y-center">
        <div>时间段选择：</div>
        <a-range-picker class="range" v-model:value="time" valueFormat="YYYY-MM-DD" />
      </div>
      <div class="mar-T12 text-center">
        <a-button style="width: 200px" type="primary" @click="handlerClick">导出文件</a-button>
      </div>
    </div>
  </a-modal>
</template>

<script setup>
import { ref, watch } from 'vue'

import Cache from '@/utils/cache'
import { getPointLineApi } from '@/services/modules/home'
import { getValveVerificationTimeListApi, getZoneData } from '@/services/modules/map'
import ExcelJS from 'exceljs'
import { message } from 'ant-design-vue'

import { createHeader, getBase64Image, createPointLineHeader } from '@/utils'

const open = defineModel()

const size = ref('fieldWork')
const time = ref([])

function handlerClick() {
  if (!time.value.length) return message.warning('请选择时间范围')
  if (size.value === 'fieldWork') {
    getValveVerificationTimeList()
  } else {
    getPointLine()
  }

  open.value = false
  time.value = []
}

// 获取点线表数据
async function getPointLine() {
  const token = Cache.get('token')
  const res = await getPointLineApi(time.value[0], time.value[1], token)
  const data = res.data

  const workbook = new ExcelJS.Workbook()
  const sheet = workbook.addWorksheet('Sheet1')
  sheet.columns = createPointLineHeader()
  const hide = message.loading('正在导出文件...', 0)
  // 添加数据
  for (let index = 0; index < data.length; index++) {
    const item = data[index]
    sheet.addRow(item)
  }

  // 文件下载
  const buffer = await workbook.xlsx.writeBuffer()
  const blob = new Blob([buffer], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' })
  const url = window.URL.createObjectURL(blob)
  const a = document.createElement('a')
  a.href = url
  a.download = `${size.value}.xlsx`
  a.click()
  window.URL.revokeObjectURL(url)
  hide()
}

// 获取外业数据 并导出
async function getValveVerificationTimeList() {
  try {
    const res = await getValveVerificationTimeListApi(time.value[0], time.value[1])
    const data = res.data.valveVerification

    for (let i = 0; i < data.length; i++) {
      const item = data[i]
      const res = await getCachehandler(item.zone_Code)
      const { District, Community, Subdistrict, Zone_Name } = res
      item.address = District + Community + Subdistrict + Zone_Name + item.address
    }

    //创建表簿
    const workbook = new ExcelJS.Workbook()
    // 创建sheet
    const sheet = workbook.addWorksheet('Sheet1')
    sheet.columns = createHeader()
    const hide = message.loading('正在导出文件...', 0)
    // 添加数据
    for (let index = 0; index < data.length; index++) {
      const item = data[index]
      sheet.addRow(item)
      if (item.site_Photo) {
        const imageUrl = 'https://www.szwgft.cn:8090/' + item.site_Photo.split(',')[0]
        const imageData = await getBase64Image(imageUrl)
        const imgId = workbook.addImage({ base64: imageData, extension: '.jpeg' })
        sheet.addImage(imgId, { tl: { col: 13, row: index + 1 }, ext: { width: 30, height: 30 }, editAs: 'oneCell' })
        sheet.getRow(index + 2).height = 30
      }
    }

    // 文件下载
    const buffer = await workbook.xlsx.writeBuffer()
    const blob = new Blob([buffer], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' })
    const url = window.URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = `${size.value}.xlsx`
    a.click()
    window.URL.revokeObjectURL(url)
    hide()
  } catch (error) {
    message.error('导出失败' + error)
    console.log(error)
  }
}

// 缓存区块信息
const getCachehandler = ((cache) => async (code) => {
  if (cache[code]) return cache[code]
  const { data } = await getZoneData(code)
  cache[code] = JSON.parse(data)[0]
  return cache[code]
})(Object.create(null))
</script>
