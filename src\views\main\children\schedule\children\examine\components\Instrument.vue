<template>
  <div class="wrap_instrument flex absolute z-index-99 border-R6 back-white box-shadow">
    <div class="pad-12" v-show="isShow">
      <DeriveFile />
    </div>
    <div class="control back-white border-R6 f-y-center f-column box-shadow">
      <CloudDownloadOutlined class="icon fon-S30 color-666" @click="iconClick" />
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { CloudDownloadOutlined } from '@ant-design/icons-vue'
import DeriveFile from './DeriveFile.vue'

const isShow = ref(false)

function iconClick() {
  isShow.value = !isShow.value
}
</script>

<style lang="less" scoped>
.wrap_instrument {
  top: 90px;
  right: 30px;
  .control {
    width: 40px;
    padding: 5px 3px;
  }
}

.icon_bottom {
  border-bottom: 1px solid #999;
}
</style>
