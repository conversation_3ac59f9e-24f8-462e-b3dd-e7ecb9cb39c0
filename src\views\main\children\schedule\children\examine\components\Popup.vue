<template>
  <div class="border-B-eee pad-6 flex border-R4">
    <a-image :width="100" :height="100" v-if="data.site_Photo" :src="'https://www.szwgft.cn:8090/' + data.site_Photo" />
    <div class="mar-L12" style="height: 100px; width: 200px">
      <div>
        <span>点子类：</span><span>{{ data.point_Type }}</span>
      </div>
      <div>
        <span>管径：</span><span>{{ data.pipe_Diameter }}</span>
      </div>
      <div>
        <span>时间：</span><span>{{ data.created_Time }}</span>
      </div>
      <div>
        <span>备注：</span><span>{{ data.notes }}</span>
      </div>
    </div>
  </div>
</template>

<script setup>
defineProps({ data: { type: Object, default: () => ({}) } })
</script>
