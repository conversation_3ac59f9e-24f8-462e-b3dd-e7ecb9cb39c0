<template>
  <!-- 总表核对 -->
  <div class="all relative">
    <MapGlS :options ref="mapComponentRef" :legend @load="mapLoad">
      <template #rightTop>
        <div class="mar-30">
          <div class="flex">
            <div class="f-1 flex">
              <div class="icon_box box-shadow f-xy-center f-column">
                <a-tooltip title="刷新展示所有"><RedoOutlined style="font-size: 26px; color: #444" @click="replacement" /></a-tooltip>
              </div>
              <div class="icon_box box-shadow f-xy-center f-column">
                <a-tooltip title="导出数据"><VerticalAlignBottomOutlined style="font-size: 26px; color: #444" @click="open = true" /></a-tooltip>
              </div>
            </div>
            <MapInputSearch style="width: 300px" @handlerZoneClick="handlerClick" />
          </div>
          <div class="mar-T10">
            <a-select size="large" v-model:value="selectValue" style="width: 140px" @change="handleSelectChange">
              <a-select-option value="全部">全部</a-select-option>
              <template v-for="item in selectOption.fieldWorkFacility" :key="item.Id">
                <a-select-option :value="item.DictValue">{{ item.DictValue }}</a-select-option>
              </template>
            </a-select>
            <a-range-picker class="mar-L16" v-model:value="times" style="width: 300px" @change="handleChange" valueFormat="YYYY-MM-DD" size="large" />
          </div>
        </div>
      </template>
      <template #leftBottom>
        <div style="margin-bottom: 160px"><ControllerLayer @change="changeLayer" /></div>
      </template>
    </MapGlS>
    <DownloadModal v-model="open" />
    <!-- <Instrument /> -->
    <GeneralSituation :Map :dotsData />
  </div>
</template>

<script setup>
import { ref, watch } from 'vue'
import mapboxgl from 'mapbox-gl'
import MapGlS from '@/components/MapGlS2/index.vue'
import DownloadModal from './components/DownloadModal.vue'
import Instrument from './components/Instrument.vue'
import MapInputSearch from '@/components/MapInputSearch/index.vue'
import { CommonApi } from '@/services/modules/common.js'
import { RedoOutlined, VerticalAlignBottomOutlined, ArrowDownOutlined } from '@ant-design/icons-vue'
import { transitionComponent, flyTo } from '@/components/MapGlS2/utils'
import Popup from './components/Popup.vue'
import { FT_ZONE, ZONE2_LINE_LAYER, FT_ModificationWorks, FT_street, tenGrid } from '@/ownedByAllMapConfig'
import ExcelJS from 'exceljs'

import { getalveVerificationListApi } from '@/services/modules/home.js'
import { getValveVerificationTimeListApi } from '@/services/modules/map'

import icon1 from '@/assets/images/icon1.png'
import icon2 from '@/assets/images/icon2.png'
import icon3 from '@/assets/images/icon3.png'
import ControllerLayer from './components/ControllerLayer.vue'

import GeneralSituation from './components/GeneralSituation/index.vue'
import { message } from 'ant-design-vue'

import listJson from '@/assets/geojson/优饮小区慢.json'
import listJson2 from '@/assets/geojson/优饮无管网.json'

const mapComponentRef = ref(null)
const open = ref(false)
const selectValue = ref('全部')
const time = ref([])

// 添加优饮图层
FT_ZONE.layers.zone2_fill = {
  above: 'zone_line',
  options: {
    'source-layer': 'FT_ZONE',
    filter: ['==', 'ppit', 'zop'],
    paint: {
      'fill-opacity': 0.8,
      // 'fill-color': ['case', ['in', ['get', 'Zone_Code'], ['literal', listJson2.list]], '#8358f6', '#5cdb54']
      'fill-color': '#5cdb54'
    },
    layout: {
      visibility: 'none'
    }
  }
}

FT_ZONE.layers.zone3_fill = {
  above: 'zone2_fill',
  options: {
    'source-layer': 'FT_ZONE',
    filter: ['in', 'Zone_Code', ...listJson.list],
    paint: {
      'fill-opacity': 0.8,
      'fill-color': '#1e6fff'
    },
    layout: { visibility: 'none' }
  }
}
FT_ZONE.layers.zone4_fill = {
  above: 'zone3_fill',
  options: {
    'source-layer': 'FT_ZONE',
    filter: ['in', 'Zone_Code', ...listJson2.list],
    paint: {
      'fill-opacity': 0.8,
      'fill-color': '#6761f1'
    },
    layout: {
      visibility: 'none'
    }
  }
}

const options = {
  sources: {
    FT_ModificationWorks,
    // 十网格图层
    tenGrid,
    // 街道图层
    FT_street,
    FT_ZONE,
    // 给水管
    feedwater: {
      type: 'vector',
      scheme: 'tms',
      tiles: ['https://www.szwgft.cn/mapServer/geoserver/gwc/service/tms/1.0.0/Code%3ASWAT_PIPE@EPSG%3A3857@pbf/{z}/{x}/{y}.pbf'],
      layers: {
        feedwaterLayer_line: {
          options: {
            minzoom: 15,
            'source-layer': 'SWAT_PIPE',
            layout: { visibility: 'none' },
            paint: { 'line-color': '#99336b', 'line-width': 2, 'line-opacity': 1 }
          }
        }
      }
    },
    dot: {
      data: { type: 'FeatureCollection', features: [] },
      type: 'geojson',
      options: { cluster: false, clusterMaxZoom: 14, clusterRadius: 50 },
      layers: {
        dotclusters_circle: {
          options: {
            filter: ['has', 'point_count'],
            paint: {
              'circle-color': ['step', ['get', 'point_count'], '#2bdeff', 100, '#f1f075', 750, '#f28cb1'],
              'circle-radius': ['step', ['get', 'point_count'], 20, 100, 30, 750, 40]
            }
          }
        },
        dotclusters_symbol: {
          options: {
            filter: ['has', 'point_count'],
            layout: {
              'text-field': ['get', 'point_count_abbreviated'],
              'text-font': ['DIN Offc Pro Medium', 'Arial Unicode MS Bold'],
              'text-size': 12
            }
          }
        },
        dotclusterspoint_symbol: {
          images: { 总表: icon2, 总阀: icon1, 立管点: icon3 },
          options: {
            filter: ['!', ['has', 'point_count']],
            layout: {
              'icon-image': ['get', 'point_Type'], //设置图片
              'icon-size': 0.3, //设置图片大小
              'icon-allow-overlap': true
            }
          },
          events: {
            click: (e, feature, map) => {
              const { properties } = feature
              new mapboxgl.Popup({ maxWidth: 'none', offset: [0, -20] })
                .setLngLat(properties.coordinates.split(','))
                .setDOMContent(component({ data: properties }))
                .addTo(map)
            }
          }
        }
      }
    }
  }
}

const legend = [
  { label: '总表', url: icon2 },
  { label: '总阀', url: icon1 },
  { label: '立管点', url: icon3 }
]

// 地图绘制结束回调
let Map
async function mapLoad(map) {
  Map = map
  getDots()
  const { data } = await CommonApi.getZoneCodeList()
  const list = JSON.parse(data).map(({ Zone_Code }) => Zone_Code)
  map.setFilter('zone2_fill', ['in', 'Zone_Code', ...list])
}

// 打开文件导出弹窗
const component = transitionComponent(Popup) //创建弹窗组件
const dotsData = ref([])

async function getDots() {
  const { data } = await getalveVerificationListApi()
  const features = data.valveVerification.map((item, index) => {
    return {
      type: 'Feature',
      id: index,
      properties: { ...item, data: JSON.stringify(item) },
      geometry: { type: 'Point', coordinates: item.coordinates.split(',') }
    }
  })
  dotsData.value = features
  Map.getSource('dot').setData({ type: 'FeatureCollection', features })

  const codes = new Set(features.map((item) => item.properties.zone_Code))
  Map.setPaintProperty('zone3_fill', 'fill-color', ['case', ['in', ['get', 'Zone_Code'], ['literal', [...codes]]], '#5cdb54', '#1e6fff'])
}

// 跳转区块与区块高亮
function handlerClick(e) {
  flyTo(Map, e.Center_Point.split(','), 17)
  Map.setFilter(ZONE2_LINE_LAYER, ['match', ['get', 'Zone_Name'], e.Zone_Name, true, false])
}

// 图层控制
function changeLayer({ info, checkedKeys }) {
  const { checked, node } = info
  const { dataRef, parent } = node

  if (dataRef.source) {
    const layers = Map.getStyle().layers
    layers.forEach((layer) => {
      if (layer.source === dataRef.source) {
        Map.setLayoutProperty(layer.id, 'visibility', checked ? 'visible' : 'none')
      }
    })
  }

  if (dataRef.filtration) {
    Map.setLayoutProperty(dataRef.layerId, 'visibility', 'visible')
    const newFilter = parent.children.filter((item) => checkedKeys.includes(item.key)).map((item) => ['==', ['get', item.node.filtration], item.node.filtrationValue])
    Map.setFilter(dataRef.layerId, ['any', ...newFilter])
    setTimeout(() => Map.fire('moveend'), 50)
    return
  }

  // 展示图层
  if (checked) {
    if (dataRef.key === 'dot_symbol_3D') {
      isDraw3DPumpHouse.value = true
      setTimeout(() => Map.fire('moveend'), 50)
      return
    }
    if (dataRef.key === 'dot_symbol_2D') {
      isDraw2DPumpHouse.value = true
      Map.setLayoutProperty('dot_symbol', 'icon-size', 0.08)
      return
    }

    Map.setLayoutProperty(dataRef.layerId, 'visibility', 'visible')
    if (dataRef.children) {
      Map.setFilter(dataRef.layerId, null)
    }
  } else {
    if (dataRef.key === 'dot_symbol_3D') {
      isDraw3DPumpHouse.value = false
      // 清除泵房模型
      addPumpHouseModel([])
      return
    }
    if (dataRef.key === 'dot_symbol_2D') {
      isDraw2DPumpHouse.value = false
      Map.setLayoutProperty('dot_symbol', 'icon-size', 0.001)
      return
    }
    Map.setLayoutProperty(dataRef.layerId, 'visibility', 'none')

    // 隐藏图层
  }
  setTimeout(() => Map.fire('moveend'), 50)
}

const selectOption = ref({})
async function getQueryDictionaries() {
  const { data: fieldWorkFacility } = await CommonApi.queryDictionaries('fieldWorkFacility') //外业点子类
  selectOption.value.fieldWorkFacility = fieldWorkFacility
}

getQueryDictionaries()

// 过滤图层
function handleSelectChange(value) {
  if (value === '全部') {
    Map.setFilter('dotclusterspoint_symbol', null)
    return
  }
  Map.setFilter('dotclusterspoint_symbol', ['==', 'point_Type', value])
}

// 重置数据展示所有点
async function replacement() {
  const hide = message.loading('正在加载数据...', 0)
  try {
    await getDots()
    time.value = []
    selectValue.value = '全部'
    hide()
  } catch (error) {
    hide()
    message.error('数据加载失败')
  }
}

async function handleChange(value) {
  let list = []
  if (value) {
    const { data } = await getValveVerificationTimeListApi(value[0], value[1])
    list = data
  } else {
    const { data } = await getalveVerificationListApi()
    list = data
  }
  const features = list.valveVerification.map((item, index) => {
    return {
      type: 'Feature',
      id: index,
      properties: { ...item, data: JSON.stringify(item) },
      geometry: { type: 'Point', coordinates: item.coordinates.split(',') }
    }
  })
  dotsData.value = features
  Map.getSource('dot').setData({ type: 'FeatureCollection', features })
  selectValue.value = '全部'
}
</script>

<style lang="less" scoped>
.icon_box {
  right: 30px;
  top: 160px;
  width: 40px;
  height: 40px;
  padding: 5px 3px;
  background-color: white;
  margin-right: 12px;
  border-radius: 6px;
  cursor: pointer;
}
</style>
