<template>
  <div class="barEchar box-shadow border-R10"><Echert :option /></div>
</template>

<script setup>
import { computed, watch } from 'vue'
import Echert from '@/components/Echarts/index.vue'
const props = defineProps({ count: Object })
const colors = [
  '#FF6347', // 番茄红
  '#4682B4', // 钢青色
  '#9ACD32', // 黄绿色
  '#FFD700', // 金色
  '#BA55D3', // 中兰花紫
  '#00CED1', // 暗青色
  '#FF8C00', // 暗橙色
  '#32CD32', // 酸橙绿
  '#4169E1', // 皇家蓝
  '#DC143C' // 深红
]
let data = [
  {
    value: 37,
    name: '一期'
  },
  {
    value: 32,
    name: '二期'
  },
  {
    value: 16,
    name: '三期'
  },
  {
    value: 116,
    name: '四期'
  },
  {
    value: 48,
    name: '五期'
  },
  {
    value: 144,
    name: '六期'
  },
  {
    value: 322,
    name: '七期'
  },
  {
    value: 133,
    name: '查缺补漏'
  }
]

const option = computed(() => ({
  backgroundColor: '#fff',
  title: [
    { text: '优饮工程项目统计', top: '3%', left: '4%' },
    {
      text: '优饮统计',
      subtext: 848,
      x: 'center',
      y: 'center',
      textStyle: { fontWeight: 'normal', fontSize: 16 }
    }
  ],
  tooltip: { show: true, trigger: 'item', formatter: '{b}: {c} ({d}%)' },
  legend: { orient: 'horizontal', bottom: '0%', data: data.map((i) => i.name) },
  series: [
    {
      type: 'pie',
      selectedMode: 'single',
      radius: ['25%', '58%'],
      // color: ['#86D560', '#AF89D6', '#59ADF3', '#FF999A', '#FFCC67'],
      color: colors,

      label: { normal: { position: 'inner', formatter: '{d}%', textStyle: { color: '#fff', fontWeight: 'bold', fontSize: 14 } } },
      labelLine: { normal: { show: false } },
      data: data
    },
    {
      type: 'pie',
      radius: ['58%', '83%'],
      itemStyle: { normal: { color: '#F2F2F2' }, emphasis: { color: '#ADADAD' } },
      label: { normal: { position: 'inner', formatter: '{c} 个', textStyle: { color: '#777777', fontWeight: 'bold', fontSize: 14 } } },
      data: data
    }
  ]
}))
</script>

<style lang="less" scoped>
.barEchar {
  width: 100%;
  height: 500px;
  background-color: white;
}
</style>
