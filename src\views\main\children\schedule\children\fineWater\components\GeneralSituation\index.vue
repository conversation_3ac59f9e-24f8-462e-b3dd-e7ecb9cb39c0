<template>
  <div class="wrap">
    <div class="all back-white border-R12 box-shadow pad-12">
      <BarEchart :count />
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'
import BarEchart from '../BarEchart.vue'
const props = defineProps({ Map: Object, dotsData: { type: Array, defaulel: () => [] } })

const count = computed(() => {
  const D = {}
  for (let i = 0; i < props.dotsData.length; i++) {
    const item = props.dotsData[i]
    const data = JSON.parse(item.properties.data)
    D[data.point_Type] = D[data.point_Type] ? D[data.point_Type] + 1 : 1
  }
  const key = Object.keys(D)
  const value = Object.values(D)
  return { key, value }
})
</script>

<style lang="less" scoped>
.wrap {
  position: absolute;
  top: 130px;
  // bottom: 0;
  right: 0;
  width: 500px;
  padding: 24px 12px;
}
</style>
