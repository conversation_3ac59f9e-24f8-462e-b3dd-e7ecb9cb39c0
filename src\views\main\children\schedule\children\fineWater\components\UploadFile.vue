<template>
  <a-modal destroyOnClose :maskClosable="false" v-model:open="open" title="大文件上传" :footer="null" centered @ok="open = false">
    <div>
      <div class="f-y-center mar-y12">
        <div class="name">区块名称 <span style="color: #f00">*</span>：</div>
        <a-input style="width: 300px" v-model:value="inputValue" />
      </div>
      <div class="f-y-center mar-y12">
        <div class="name">类型 <span style="color: #f00">*</span>：</div>
        <a-select ref="select" v-model:value="selectValue" style="width: 300px" @change="(e) => (selectValue = e)">
          <a-select-option value="基本资料">基本资料</a-select-option>
          <a-select-option value="图纸资料">图纸资料</a-select-option>
          <a-select-option value="协议资料">协议资料</a-select-option>
          <a-select-option value="其他/优饮">其他/优饮</a-select-option>
        </a-select>
      </div>
    </div>

    <a-upload-dragger v-model:fileList="fileList" name="file" :multiple="true" :headers :action="pathFile" @change="handleChange">
      <p class="ant-upload-drag-icon">
        <inbox-outlined></inbox-outlined>
      </p>
      <p class="ant-upload-text">单击或拖动文件到此区域进行上传</p>
      <p class="ant-upload-hint">Support for a single or bulk upload. Strictly prohibit from uploading company data or other band files</p>
    </a-upload-dragger>
  </a-modal>
</template>

<script setup>
import { ref, computed, watch } from 'vue'

import { InboxOutlined } from '@ant-design/icons-vue'
import { message } from 'ant-design-vue'
import { setDocument } from '@/services/modules/home'
import Cache from '@/utils/cache'
const inputValue = ref('')
const selectValue = ref('基本资料')

const props = defineProps({ data: { type: Object, default: () => ({}) } })

const headers = { Authorization: `Bearer ${Cache.get('userInfo')?.token}` }

const open = defineModel()
const fileList = ref([])

watch(
  () => props.data,
  () => (inputValue.value = props.data.Zone_Name)
)

watch(open, (val) => (!val ? (fileList.value = []) : null))

const pathFile = computed(() => {
  return `https://www.szwgft.cn:5000/api/UpWrite/uploadmax?FolderPath=${props?.data?.ManagerName}\\${props?.data?.Subdistrict}\\${props?.data?.Community}\\${inputValue.value}\\${selectValue.value}`
})

const handleChange = (info) => {
  if (!info.file?.response) return
  if (info.file?.response?.code === 200) {
    const data = {
      id: 0,
      type: selectValue.value,
      zone_Code: props.data.Zone_Code,
      zone_Name: props.data.Zone_Name,
      filePath1: info.file.response.data.filePath,
      enteredby: Cache.get('userInfo').name,
      entryTime: new Date()
    }

    setDocument(data)
  }

  if (info?.file?.response?.code === 200) {
    message.success(`${info.file.name} 上传成功.`)
  } else {
    fileList.value.pop()
    message.error(`${info.file.name} 上传失败,${info.file?.response?.message}`)
  }
}
</script>

<style lang="less" scoped>
.name {
  width: 80px;
  text-align: right;
}
</style>
