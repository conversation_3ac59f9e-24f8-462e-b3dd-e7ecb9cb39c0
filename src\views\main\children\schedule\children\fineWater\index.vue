<template>
  <div class="all">
    <!-- 优饮 -->
    <MapGlS ref="mapComponentRef" :options :legend @load="mapLoad">
      <template #rightTop>
        <div class="mar-30">
          <MapInputSearch @handlerZoneClick="handlerClick" />
        </div>
      </template>
      <template #leftBottom>
        <div style="margin-bottom: 160px"><ControllerLayer @change="changeLayer" /></div>
      </template>
    </MapGlS>
    <UploadFile v-model="open" :data="data" />
    <GeneralSituation :dotsData />
  </div>
</template>

<script setup>
import { ref } from 'vue'

import MapGlS from '@/components/MapGlS2/index.vue'
import { flyTo, transitionComponent } from '@/components/MapGlS2/utils'
import { getZoneData } from '@/services/modules/map'
import mapboxgl from 'mapbox-gl'
import { getZoneCodeList } from './api'
import GeneralSituation from './components/GeneralSituation/index.vue'

import UploadFile from './components/UploadFile.vue'
import fineWaterPopup from '@/components/MapPopup/fineWaterPopup.vue'
import { storeToRefs } from 'pinia'
import { useFineWaterStore } from '@/store/fineWater'
import ControllerLayer from './components/ControllerLayer.vue'

import options from '@/ownedByAllMapConfig'
import { ZONE_FILL_LAYER, ZONE2_LINE_LAYER, highlightZone } from '@/ownedByAllMapConfig'
const legend = ref([{ label: '优饮小区', color: '#f00' }])

const { open, data } = storeToRefs(useFineWaterStore())
const component = transitionComponent(fineWaterPopup)
const mapComponentRef = ref(null)
const dotsData = ref([])

let Map

const codes = ref([])
async function mapLoad(map) {
  Map = map
  const res = await getZoneCodeList()
  const codeList = JSON.parse(res.data).map((item) => item.Zone_Code)
  codes.value = codeList
  const filter = ['match', ['get', 'Zone_Code'], codeList, '#ee0000', '#999999']
  map.setPaintProperty('zone_fill', 'fill-color', filter)
  highlightZone(Map)
  insertMapPopup()
}

// 添加区块信息弹窗
function insertMapPopup() {
  Map.on('click', ZONE_FILL_LAYER, async (e) => {
    const [feature] = Map.queryRenderedFeatures(e.point, { layers: [ZONE_FILL_LAYER] })
    const { properties } = feature
    if (!codes.value.includes(properties.Zone_Code)) return
    const result = await getZoneData(properties.Zone_Code)
    const [res] = JSON.parse(result.data)
    const dom = component({ data: res })
    const popup = new mapboxgl.Popup({ maxWidth: 'none' })
    popup.setLngLat(e.lngLat).setDOMContent(dom).addTo(Map)
  })
}
// 跳转区块与区块高亮
function handlerClick(e) {
  flyTo(Map, e.Center_Point.split(','), 17)
  Map.setFilter(ZONE2_LINE_LAYER, ['match', ['get', 'Zone_Name'], e.Zone_Name, true, false])
}

// 图层控制
function changeLayer({ info, checkedKeys }) {
  const { checked, node } = info
  const { dataRef, parent } = node

  if (dataRef.source) {
    const layers = Map.getStyle().layers
    layers.forEach((layer) => {
      if (layer.source === dataRef.source) {
        Map.setLayoutProperty(layer.id, 'visibility', checked ? 'visible' : 'none')
      }
    })
  }

  if (dataRef.filtration) {
    Map.setLayoutProperty(dataRef.layerId, 'visibility', 'visible')
    const newFilter = parent.children.filter((item) => checkedKeys.includes(item.key)).map((item) => ['==', ['get', item.node.filtration], item.node.filtrationValue])
    Map.setFilter(dataRef.layerId, ['any', ...newFilter])
    setTimeout(() => Map.fire('moveend'), 50)
    return
  }

  // 展示图层
  if (checked) {
    if (dataRef.key === 'dot_symbol_3D') {
      isDraw3DPumpHouse.value = true
      setTimeout(() => Map.fire('moveend'), 50)
      return
    }
    if (dataRef.key === 'dot_symbol_2D') {
      isDraw2DPumpHouse.value = true
      Map.setLayoutProperty('dot_symbol', 'icon-size', 0.08)
      return
    }

    Map.setLayoutProperty(dataRef.layerId, 'visibility', 'visible')
    if (dataRef.children) {
      Map.setFilter(dataRef.layerId, null)
    }
  } else {
    if (dataRef.key === 'dot_symbol_3D') {
      isDraw3DPumpHouse.value = false
      // 清除泵房模型
      addPumpHouseModel([])
      return
    }
    if (dataRef.key === 'dot_symbol_2D') {
      isDraw2DPumpHouse.value = false
      Map.setLayoutProperty('dot_symbol', 'icon-size', 0.001)
      return
    }
    Map.setLayoutProperty(dataRef.layerId, 'visibility', 'none')

    // 隐藏图层
  }
  setTimeout(() => Map.fire('moveend'), 50)
}
</script>

<style lang="less" scoped></style>
