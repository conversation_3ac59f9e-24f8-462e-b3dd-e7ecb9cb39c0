<template>
  <div class="modern-row">
    <template v-for="item in Object.entries(value)" :key="item[0]">
      <div class="field-item">
        <div class="field-label">{{ item[0] }}</div>
        <div class="field-value">
          <a-tooltip placement="top" :title="getTooltipContent(item)">
            <div class="value-content">
              <span v-if="item[1] == 'jsgl'">
                {{
                  handlerParams(item[1], data[item[1]])
                    .map((i) => i.name)
                    .join('、')
                }}
              </span>
              <span v-else-if="item[1] == 'block_number'">
                {{ handlerCount(handlerParams(item[1], data[item[1]])) }}
              </span>
              <span v-else>
                {{ handlerParams(item[1], data[item[1]]) }}
              </span>
            </div>
          </a-tooltip>
        </div>
      </div>
    </template>
  </div>
</template>

<script setup>
const props = defineProps({ data: Object, value: Object, fn: Boolean })

const keys = {
  pipeDia: '市政管径',
  location: '水表组位置',
  pipe: '市政管材',
  status: '水表组阀门状态',
  waterStatus: '预留口阀门状态',
  value: '预留阀门口号',
  waterLocation: '预留口位置'
}

function handlerParams(key, value) {
  if (!value || value == '""') return '无数据'
  if (key === 'jsgl' && /^[\[]/.test(value)) {
    return JSON.parse(value)
  } else if (key === 'jsgl') {
    return []
  }

  if (key === 'block_number') return value.split(';')

  if (['wall_climbing_pipe_after', 'pqggc', 'buried_pipes_after', 'mdgc'].includes(key)) {
    if (/^[\[]/.test(value)) return JSON.parse(value).join('、')
    return value.replace(/"/g, '')
  }
  if (['gzwcnd', 'egcsjsnd'].includes(key)) return value.slice(0, 10)

  return value === true ? '是' : value === false ? '否' : value
}

function handlerCount(val) {
  if (val === '无数据') return val

  return (
    '楼栋总数' +
    val.reduce((total, item) => {
      let match = item.match(/栋数：(\d+)栋/)
      if (match) {
        let count = parseInt(match[1], 10)
        return total + count
      } else {
        return total
      }
    }, 0)
  )
}

function getTooltipContent(item) {
  const [, key] = item
  const value = props.data[key]

  if (key === 'jsgl') {
    const params = handlerParams(key, value)
    if (Array.isArray(params) && params.length > 0) {
      return params
        .map((i2) => {
          let content = `${i2.name}\n`
          Object.entries(i2).forEach(([k, v]) => {
            if (k !== 'name' && v) {
              content += `${keys[k] || k}：${v}\n`
            }
          })
          return content
        })
        .join('\n---\n')
    }
  } else if (key === 'block_number') {
    const params = handlerParams(key, value)
    if (Array.isArray(params)) {
      return params.join('\n')
    }
  }

  return handlerParams(key, value)
}
</script>

<style lang="less" scoped>
.modern-row {
  display: grid;
  gap: 8px;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
}

.field-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 8px 12px;
  background: #f8f9fa;
  border-radius: 6px;
  border: 1px solid #e9ecef;
  transition: all 0.2s ease;

  &:hover {
    background: #e9ecef;
    border-color: #dee2e6;
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }
}

.field-label {
  font-size: 13px;
  font-weight: 500;
  color: #6c757d;
  min-width: 80px;
  flex-shrink: 0;
  position: relative;

  &::after {
    content: ':';
    margin-left: 2px;
  }
}

.field-value {
  flex: 1;
  min-width: 0;
}

.value-content {
  background: white;
  border: 1px solid #dee2e6;
  border-radius: 4px;
  padding: 6px 10px;
  font-size: 12px;
  color: #495057;
  cursor: pointer;
  transition: all 0.2s ease;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  min-height: 28px;
  display: flex;
  align-items: center;

  &:hover {
    border-color: #adb5bd;
    background: #f8f9fa;
  }

  span {
    width: 100%;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
}

// 响应式设计
@media (max-width: 768px) {
  .modern-row {
    grid-template-columns: 1fr;
  }

  .field-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 6px;
  }

  .field-label {
    min-width: auto;
    font-weight: 600;
  }

  .value-content {
    width: 100%;
    min-height: 32px;
  }
}

// 深色模式支持
@media (prefers-color-scheme: dark) {
  .field-item {
    background: #2d3748;
    border-color: #4a5568;
  }

  .field-label {
    color: #a0aec0;
  }

  .value-content {
    background: #1a202c;
    border-color: #4a5568;
    color: #e2e8f0;

    &:hover {
      background: #2d3748;
      border-color: #718096;
    }
  }
}
</style>
