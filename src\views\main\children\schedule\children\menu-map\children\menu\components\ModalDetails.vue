<template>
  <a-modal class="modern-modal" :footer="null" :width="900" :title="data?.xqmc ?? ''" v-model:open="open" centered @ok="modal2Visible = false">
    <div class="modal-content">
      <!-- 基础信息区域 - 默认展开 -->
      <div class="collapsible-section" :class="{ 'is-collapsed': !activeKeys.includes('basic') }">
        <div class="section-header" @click="toggleSection('basic')">
          <div class="section-title">
            <span class="title-text">基础信息</span>
            <div class="expand-icon" :class="{ 'is-expanded': activeKeys.includes('basic') }">
              <svg width="16" height="16" viewBox="0 0 16 16" fill="currentColor">
                <path d="M4.427 9.573L8 6l3.573 3.573a.5.5 0 0 0 .708-.708l-4-4a.5.5 0 0 0-.708 0l-4 4a.5.5 0 1 0 .708.708z" />
              </svg>
            </div>
          </div>
        </div>
        <div class="section-content">
          <div class="info-grid">
            <ItemRow :data :value="{ 组织机构: 'fgs', 小区编码: 'xqbm' }" />
            <ItemRow :data :value="{ 管理单位: 'deptname', 小区类别: 'xqlb' }" />
            <ItemRow :data :value="{ 所属街道: 'ssjd', 所属社区: 'communityname' }" />
            <ItemRow :data :value="{ 行政区: 'districtname', 楼宇属性: 'lcjds' }" />
          </div>
        </div>
      </div>

      <!-- 小区基本信息 -->
      <div class="collapsible-section" :class="{ 'is-collapsed': !activeKeys.includes('community') }">
        <div class="section-header" @click="toggleSection('community')">
          <div class="section-title">
            <span class="title-text">小区基本信息</span>
            <div class="expand-icon" :class="{ 'is-expanded': activeKeys.includes('community') }">
              <svg width="16" height="16" viewBox="0 0 16 16" fill="currentColor">
                <path d="M4.427 9.573L8 6l3.573 3.573a.5.5 0 0 0 .708-.708l-4-4a.5.5 0 0 0-.708 0l-4 4a.5.5 0 1 0 .708.708z" />
              </svg>
            </div>
          </div>
        </div>
        <div class="section-content">
          <div class="info-grid">
            <ItemRow :data :value="{ 性质: 'nature', 类型: 'home_type' }" />
            <ItemRow :data :value="{ 栋数: 'block_number', 户数: 'usercount' }" />
            <ItemRow :data :value="{ 抄表情况: 'czccbqk', 水厂: 'water_works' }" />
            <ItemRow :data :value="{ 是否有泵房: 'is_pump_room', 黄海高程: 'yellow_sea_elevation' }" />
          </div>
        </div>
      </div>

      <!-- 供水系统基本信息 -->
      <div class="collapsible-section" :class="{ 'is-collapsed': !activeKeys.includes('water-system') }">
        <div class="section-header" @click="toggleSection('water-system')">
          <div class="section-title">
            <span class="title-text">供水系统基本信息</span>
            <div class="expand-icon" :class="{ 'is-expanded': activeKeys.includes('water-system') }">
              <svg width="16" height="16" viewBox="0 0 16 16" fill="currentColor">
                <path d="M4.427 9.573L8 6l3.573 3.573a.5.5 0 0 0 .708-.708l-4-4a.5.5 0 0 0-.708 0l-4 4a.5.5 0 1 0 .708.708z" />
              </svg>
            </div>
          </div>
        </div>
        <div class="section-content">
          <div class="info-grid">
            <ItemRow :data :value="{ 进水路数: 'jsgl', 小区供水方式: 'gsfs' }" />
            <ItemRow :data :value="{ 利用市政压力: 'lyszyl', 加压方式: 'pressurized_zone' }" />
            <ItemRow :data :value="{ 管网生消分离情况: 'gwsxflqk', 加压分区: 'jyfq' }" />
          </div>
        </div>
      </div>

      <!-- 供水管网基本信息 -->
      <div class="collapsible-section" :class="{ 'is-collapsed': !activeKeys.includes('pipeline') }">
        <div class="section-header" @click="toggleSection('pipeline')">
          <div class="section-title">
            <span class="title-text">供水管网基本信息</span>
            <div class="expand-icon" :class="{ 'is-expanded': activeKeys.includes('pipeline') }">
              <svg width="16" height="16" viewBox="0 0 16 16" fill="currentColor">
                <path d="M4.427 9.573L8 6l3.573 3.573a.5.5 0 0 0 .708-.708l-4-4a.5.5 0 0 0-.708 0l-4 4a.5.5 0 1 0 .708.708z" />
              </svg>
            </div>
          </div>
        </div>
        <div class="section-content">
          <div class="info-grid">
            <ItemRow :data :value="{ 初始建设年代: 'csjsnd', 是否提标改造: 'sftbgz' }" />
            <ItemRow :data :value="{ 改造完成年代: 'gzwcnd', 优饮批次: 'excellent_drink_batch' }" />
            <ItemRow :data :value="{ 改造前埋地管材: 'mdgc', 改造后埋地管材: 'buried_pipes_after' }" />
            <ItemRow :data :value="{ 改造前爬墙管材: 'pqggc', 改造后爬墙管材: 'wall_climbing_pipe_after' }" />
            <ItemRow :data :value="{ '常用总表口径(mm)': 'commonly_used_general_table_caliber' }" />
          </div>
        </div>
      </div>

      <!-- 二次供水基本信息 -->
      <div class="collapsible-section" :class="{ 'is-collapsed': !activeKeys.includes('secondary') }">
        <div class="section-header" @click="toggleSection('secondary')">
          <div class="section-title">
            <span class="title-text">二次供水基本信息</span>
            <div class="expand-icon" :class="{ 'is-expanded': activeKeys.includes('secondary') }">
              <svg width="16" height="16" viewBox="0 0 16 16" fill="currentColor">
                <path d="M4.427 9.573L8 6l3.573 3.573a.5.5 0 0 0 .708-.708l-4-4a.5.5 0 0 0-.708 0l-4 4a.5.5 0 1 0 .708.708z" />
              </svg>
            </div>
          </div>
        </div>
        <div class="section-content">
          <div class="info-grid">
            <ItemRow :data :value="{ 泵房状态: 'pump_room_status', 泵房是否由我司运维: 'is_pump_room_our_company' }" />
          </div>
        </div>
      </div>

      <!-- 泵房改造前（现状）情况 -->
      <div class="collapsible-section" v-if="isShow.includes(1)" :class="{ 'is-collapsed': !activeKeys.includes('before') }">
        <div class="section-header" @click="toggleSection('before')">
          <div class="section-title">
            <span class="title-text">泵房改造前（现状）情况</span>
            <div class="expand-icon" :class="{ 'is-expanded': activeKeys.includes('before') }">
              <svg width="16" height="16" viewBox="0 0 16 16" fill="currentColor">
                <path d="M4.427 9.573L8 6l3.573 3.573a.5.5 0 0 0 .708-.708l-4-4a.5.5 0 0 0-.708 0l-4 4a.5.5 0 1 0 .708.708z" />
              </svg>
            </div>
          </div>
        </div>
        <div class="section-content">
          <div class="info-grid">
            <ItemRow :data :value="{ '泵房数量（座）': 'before_pump_room_count', 备注: 'before_remark' }" />
            <ItemRow :data :value="{ '设计加压规模(m³/日)': 'before_pressure_scale', 加压户数: 'before_number_pressurized' }" />
            <ItemRow :data :value="{ 供水方式: 'before_water_supply_method', 加压分区情况: 'before_pressurized_partition_situation' }" />
            <ItemRow :data :value="{ '水泵数量(台)': 'before_number_pumps', 水泵材质: 'before_pump_material' }" />
            <ItemRow :data :value="{ '水池/箱(座)': 'before_pool_number', '水池/箱材质': 'before_pool_material' }" />
            <ItemRow :data :value="{ 泵房管路材质: 'before_pump_room_piping_material', 泵房阀门主要材质: 'before_pump_room_valve_main_material' }" />
          </div>
        </div>
      </div>

      <!-- 泵房临供情况 -->
      <div class="collapsible-section" v-if="isShow.includes(2)" :class="{ 'is-collapsed': !activeKeys.includes('temp') }">
        <div class="section-header" @click="toggleSection('temp')">
          <div class="section-title">
            <span class="title-text">泵房临供情况</span>
            <div class="expand-icon" :class="{ 'is-expanded': activeKeys.includes('temp') }">
              <svg width="16" height="16" viewBox="0 0 16 16" fill="currentColor">
                <path d="M4.427 9.573L8 6l3.573 3.573a.5.5 0 0 0 .708-.708l-4-4a.5.5 0 0 0-.708 0l-4 4a.5.5 0 1 0 .708.708z" />
              </svg>
            </div>
          </div>
        </div>
        <div class="section-content">
          <div class="info-grid">
            <ItemRow :data :value="{ 泵房管路材质: 'lgbf_pump_room_piping_material', 泵房阀门主要材质: 'lgbf_pump_room_valve_main_material' }" />
            <ItemRow :data :value="{ 临时供水开始时间: 'egcsjsnd', 供水方式: 'lgbf_water_supply_method' }" />
            <ItemRow :data :value="{ 备注: 'lgbf_remark' }" />
          </div>
        </div>
      </div>

      <!-- 泵房改造后情况 -->
      <div class="collapsible-section" v-if="isShow.includes(3)" :class="{ 'is-collapsed': !activeKeys.includes('after') }">
        <div class="section-header" @click="toggleSection('after')">
          <div class="section-title">
            <span class="title-text">泵房改造后情况</span>
            <div class="expand-icon" :class="{ 'is-expanded': activeKeys.includes('after') }">
              <svg width="16" height="16" viewBox="0 0 16 16" fill="currentColor">
                <path d="M4.427 9.573L8 6l3.573 3.573a.5.5 0 0 0 .708-.708l-4-4a.5.5 0 0 0-.708 0l-4 4a.5.5 0 1 0 .708.708z" />
              </svg>
            </div>
          </div>
        </div>
        <div class="section-content">
          <div class="info-grid">
            <ItemRow :data :value="{ '泵房数量（座）': 'bfsl', 备注: 'eg_remark' }" />
            <ItemRow :data :value="{ '设计加压规模(m³/日)': 'jygm', 加压户数: 'jyhs' }" />
            <ItemRow :data :value="{ 供水方式: 'water_supply_method', 加压分区情况: 'pressurized_partition_situation' }" />
            <ItemRow :data :value="{ '水泵数量(台)': 'sbsl', 水泵材质: 'sbcz' }" />
            <ItemRow :data :value="{ '水池/箱(座)': 'dxsc', '水池/箱材质': 'dxscxcz' }" />
            <ItemRow :data :value="{ 泵房管路材质: 'bfglcz', 泵房阀门主要材质: 'bffmzycz' }" />
            <ItemRow :data :value="{ 改造完成年代: 'eggzwcnd' }" />
          </div>
        </div>
      </div>
    </div>
  </a-modal>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import ItemRow from './ItemRow.vue'

const open = defineModel()
const props = defineProps({ data: Object })

// 折叠状态管理
const activeKeys = ref(['basic']) // 默认展开基础信息

// 监听模态框打开状态，重置折叠状态
watch(open, (val) => {
  if (val) {
    activeKeys.value = ['basic'] // 每次打开时默认只展开基础信息
  }
})

// 切换折叠状态
const toggleSection = (key) => {
  const index = activeKeys.value.indexOf(key)
  if (index > -1) {
    activeKeys.value.splice(index, 1)
  } else {
    activeKeys.value.push(key)
  }
}

const isShow = computed(() => {
  switch (props?.data?.pump_room_status ?? '#') {
    case '无需二供改造':
      return [1]
    case '查漏补缺':
      return [1]
    case '已立项未进场':
      return [1]
    case '已立项施工中(指临供状态)':
      return [1, 2]
    case '已通水':
      return [1, 2, 3]
    default:
      return []
  }
})
</script>

<style lang="less" scoped>
.modern-modal {
  :deep(.ant-modal-content) {
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  }

  :deep(.ant-modal-header) {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-bottom: none;
    padding: 20px 24px;

    .ant-modal-title {
      color: white;
      font-size: 18px;
      font-weight: 600;
      text-align: center;
      margin: 0;
    }
  }

  :deep(.ant-modal-body) {
    padding: 0;
    max-height: 70vh;
    overflow-y: auto;
  }

  :deep(.ant-modal-close) {
    color: white;
    top: 16px;
    right: 16px;

    &:hover {
      color: rgba(255, 255, 255, 0.8);
    }
  }
}

.modal-content {
  padding: 20px;
  background: #fafafa;
}

// 折叠区域样式
.collapsible-section {
  background: white;
  border-radius: 8px;
  margin-bottom: 12px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

  &:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    transform: translateY(-1px);
  }

  &:last-child {
    margin-bottom: 0;
  }

  &.is-collapsed {
    .section-content {
      max-height: 0;
      padding: 0 20px;
      opacity: 0;
    }

    .expand-icon {
      transform: rotate(180deg);
    }
  }
}

// 区域头部样式
.section-header {
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  padding: 14px 20px;
  cursor: pointer;
  user-select: none;
  transition: all 0.2s ease;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);

  &:hover {
    background: linear-gradient(135deg, #e9ecef 0%, #dee2e6 100%);
  }

  &:active {
    transform: scale(0.98);
  }
}

.section-title {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
}

.title-text {
  font-size: 14px;
  font-weight: 600;
  color: #2c3e50;
  display: flex;
  align-items: center;

  &::before {
    content: '';
    width: 4px;
    height: 16px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 2px;
    margin-right: 10px;
  }
}

// 展开图标样式
.expand-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  border-radius: 50%;
  background: rgba(102, 126, 234, 0.1);
  color: #667eea;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  transform: rotate(180deg);

  &.is-expanded {
    transform: rotate(0deg);
    background: rgba(102, 126, 234, 0.15);
  }

  &:hover {
    background: rgba(102, 126, 234, 0.2);
    transform: scale(1.1) rotate(180deg);
  }

  &.is-expanded:hover {
    transform: scale(1.1) rotate(0deg);
  }

  svg {
    transition: transform 0.2s ease;
  }
}

// 内容区域样式
.section-content {
  max-height: 1000px;
  padding: 16px 20px;
  opacity: 1;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  overflow: hidden;
}

.info-grid {
  display: grid;
  gap: 12px;
}

// 优化 ItemRow 在新布局中的样式
:deep(.modern-row) {
  .field-item {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    transition: all 0.2s ease;

    &:hover {
      background: #e9ecef;
      border-color: #dee2e6;
      transform: translateY(-1px);
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }
  }

  .value-content {
    background: white;
    border: 1px solid #dee2e6;
    transition: all 0.2s ease;

    &:hover {
      border-color: #adb5bd;
      background: #f8f9fa;
    }
  }

  .field-label {
    color: #6c757d;
    font-weight: 500;
  }
}

// 滚动条样式优化
.modal-content {
  &::-webkit-scrollbar {
    width: 6px;
  }

  &::-webkit-scrollbar-track {
    background: #f1f3f4;
    border-radius: 3px;
  }

  &::-webkit-scrollbar-thumb {
    background: #c1c8cd;
    border-radius: 3px;

    &:hover {
      background: #a8b2ba;
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .modern-modal {
    :deep(.ant-modal) {
      width: 95% !important;
      margin: 10px;
    }
  }

  .modal-content {
    padding: 16px;
  }

  .section-header {
    padding: 12px 16px;
  }

  .section-content {
    padding: 12px 16px;
  }

  .title-text {
    font-size: 13px;
  }

  .expand-icon {
    width: 20px;
    height: 20px;
  }
}

// 深色模式支持
@media (prefers-color-scheme: dark) {
  .collapsible-section {
    background: #2d3748;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
  }

  .section-header {
    background: linear-gradient(135deg, #4a5568 0%, #2d3748 100%);
    border-bottom-color: rgba(255, 255, 255, 0.1);

    &:hover {
      background: linear-gradient(135deg, #718096 0%, #4a5568 100%);
    }
  }

  .title-text {
    color: #e2e8f0;
  }

  .expand-icon {
    background: rgba(102, 126, 234, 0.2);
    color: #90cdf4;
  }

  .modal-content {
    background: #1a202c;
  }
}

// 动画增强
@keyframes slideDown {
  from {
    max-height: 0;
    opacity: 0;
  }
  to {
    max-height: 1000px;
    opacity: 1;
  }
}

@keyframes slideUp {
  from {
    max-height: 1000px;
    opacity: 1;
  }
  to {
    max-height: 0;
    opacity: 0;
  }
}
</style>
