<template>
  <div class="all" style="height: 850px">
    <a-table class="ant-table-striped" :data-source="Menus" :pagination="{ pageSize: 50 }" :loading :scroll="{ y: 850 }" :row-class-name="(_record, index) => (index % 2 === 1 ? 'table-striped' : null)">
      <a-table-column key="deptname" title="管理单位" data-index="deptname" />
      <a-table-column key="xqmc" title="小区名称" data-index="xqmc" />
      <a-table-column key="xqbm" title="小区编码" data-index="xqbm" />
      <a-table-column key="ssjd" title="所属街道" data-index="ssjd" />
      <a-table-column key="xqlb" title="小区类别" data-index="xqlb" />
      <a-table-column key="action" title="操作">
        <template #default="{ record }"> <a-button type="primary" @click="handlerClick(record)">详情</a-button> </template>
      </a-table-column>
      <template #title>
        <div class="flex f-row-reverse pad-R30"><a-button type="primary" @click="derivedTable">导出</a-button></div>
      </template>
    </a-table>

    <ModalDetails v-model="open" :data="detail" />
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { getDwd_gwyy_xqxttz } from '@/services/modules/home'
import { educeXlsx } from '@/utils/educeXlsx'
import recordKeys from '@/assets/geojson/recordKeys.json'
import ModalDetails from './components/ModalDetails.vue'
import { useRoute } from 'vue-router'
import { message } from 'ant-design-vue'
const route = useRoute()

const Menus = ref([])
const open = ref(false)
const detail = ref(null)
const loading = ref(false)
async function getMenuList(data) {
  loading.value = true
  const res = await getDwd_gwyy_xqxttz(data)
  Menus.value = res
  loading.value = false
}
getMenuList({ xqmc: null, xqbm: null })

if (route?.query?.name) getMenu()

async function getMenu() {
  const [data] = await getDwd_gwyy_xqxttz({ xqmc: route?.query?.name, xqbm: null })
  if (!data) return message.warning('该小区未存在档案')
  open.value = true
  detail.value = data
}

function handlerClick(value) {
  open.value = true
  detail.value = value
}

function derivedTable() {
  const data = Menus.value.map((item) => {
    const newItem = JSON.parse(JSON.stringify(item))
    newItem.block_number = newItem.block_number ? `${handlerCount(newItem.block_number)}\n${newItem.block_number.replace(/;/g, '\n')}` : ''
    newItem.jsgl = handlerStr2(newItem.jsgl)
    newItem.csjsnd = newItem.csjsnd ? newItem.csjsnd.slice(0, 4) : ''
    ;['wall_climbing_pipe_after', 'pqggc', 'buried_pipes_after', 'mdgc'].forEach((key) => (newItem[key] = handlerStr(newItem[key])))
    ;['is_pump_room_our_company', 'sftbgz'].forEach((key) => (newItem[key] = newItem[key] ? '是' : '否'))

    return newItem
  })
  const fileName = '小区台账_' + new Date().toLocaleString().slice(0, 10)
  educeXlsx(data, recordKeys, fileName)
}

function handlerCount(val) {
  if (!val) return
  return (
    '楼栋总数' +
    val.split('，').reduce((total, item) => {
      let match = item.match(/栋数：(\d+)栋/)
      if (match) {
        let count = parseInt(match[1], 10)
        return total + count
      } else {
        return total
      }
    }, 0)
  )
}

function handlerStr(value) {
  if (!value || value == '""') return ''
  if (/^[\[]/.test(value)) {
    try {
      // 尝试解析为 JSON 数组
      return JSON.parse(value).join('、')
    } catch (error) {
      // 如果解析失败，手动处理类似 [不锈钢] 的格式
      const match = value.match(/^\[(.+)\]$/)
      if (match) {
        // 移除方括号，按逗号分割（如果有的话），然后用顿号连接
        return match[1]
          .split(',')
          .map((item) => item.trim())
          .join('、')
      }
      // 如果都不匹配，返回原值去掉引号
      return value.replace(/"/g, '')
    }
  }
  return value.replace(/"/g, '')
}

const keys = { pipeDia: '市政管径', location: '水表组位置', pipe: '市政管材', status: '水表组阀门状态', waterStatus: '预留口阀门状态', value: '预留阀门口号', waterLocation: '预留口位置' }
const entries = Object.entries(keys)
function handlerStr2(str) {
  if (!str || str == '[]') return
  if (!/^[\[]/.test(str)) return str.replace(/"/g, '')
  const data = JSON.parse(str)
  const val = data.map((item) => item.name + '\n' + entries.map(([key, value]) => (item[key] ? `${value}： ${item[key]}\n` : '')).filter((item) => item))
  return val.join('\n').replace(/,/g, '')
}
</script>

<style lang="less" scoped>
.table-striped {
  background-color: aqua;
}

.ant-table-striped :deep(.table-striped) td {
  background-color: #fafafa;
}
.ant-table-striped :deep(.table-striped) td {
  background-color: #eee;
}
// .ant-table-striped :deep(.ant-spin) {
//   max-height: none;
//   height: 100vh;
// }
</style>
