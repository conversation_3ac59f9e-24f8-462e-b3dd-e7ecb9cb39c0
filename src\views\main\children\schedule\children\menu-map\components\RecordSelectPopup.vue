<template>
  <div class="record-select-popup">
    <!-- 标题区域 -->
    <div class="popup-header">
      <div class="header-icon">
        <FileTextOutlined />
      </div>
      <div class="header-title">请选择需要查看的档案</div>
      <div class="header-subtitle">共 {{ data.length }} 个档案可选</div>
    </div>

    <!-- 档案列表 -->
    <div class="popup-content">
      <div class="record-list">
        <div v-for="(item, index) in data" :key="index" class="record-item" @click="handleRecordClick(item)">
          <div class="record-item-content">
            <div class="record-icon">
              <FolderOutlined />
            </div>
            <div class="record-info">
              <div class="record-name">{{ item.xqmc }}</div>
              <div class="record-desc">点击查看详细档案信息</div>
            </div>
            <div class="record-arrow">
              <RightOutlined />
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 底部提示 -->
    <div class="popup-footer">
      <div class="footer-tip">
        <InfoCircleOutlined />
        <span>选择档案后将显示详细信息</span>
      </div>
    </div>
  </div>
</template>

<script setup>
import { FileTextOutlined, FolderOutlined, RightOutlined, InfoCircleOutlined } from '@ant-design/icons-vue'

const props = defineProps({
  data: {
    type: Array,
    default: () => []
  }
})

// 处理档案点击事件
const handleRecordClick = (item) => {
  if (item.selectfn && typeof item.selectfn === 'function') {
    item.selectfn(item)
  }
}
</script>

<style lang="less" scoped>
.record-select-popup {
  min-width: 320px;
  max-width: 400px;
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
  overflow: hidden;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;

  .popup-header {
    padding: 20px 24px 16px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: #fff;
    text-align: center;

    .header-icon {
      font-size: 24px;
      margin-bottom: 8px;
      opacity: 0.9;
    }

    .header-title {
      font-size: 18px;
      font-weight: 600;
      margin-bottom: 4px;
      line-height: 1.4;
    }

    .header-subtitle {
      font-size: 13px;
      opacity: 0.8;
      font-weight: 400;
    }
  }

  .popup-content {
    padding: 16px 0;
    max-height: 300px;
    overflow-y: auto;

    &::-webkit-scrollbar {
      width: 4px;
    }

    &::-webkit-scrollbar-track {
      background: #f5f5f5;
    }

    &::-webkit-scrollbar-thumb {
      background: #d9d9d9;
      border-radius: 2px;

      &:hover {
        background: #bfbfbf;
      }
    }
  }

  .record-list {
    .record-item {
      margin: 0 16px 8px;
      border-radius: 8px;
      transition: all 0.2s ease;
      cursor: pointer;
      border: 1px solid #f0f0f0;

      &:last-child {
        margin-bottom: 0;
      }

      &:hover {
        border-color: #1890ff;
        box-shadow: 0 2px 8px rgba(24, 144, 255, 0.15);
        transform: translateY(-1px);

        .record-arrow {
          color: #1890ff;
          transform: translateX(2px);
        }
      }

      &:active {
        transform: translateY(0);
        box-shadow: 0 1px 4px rgba(24, 144, 255, 0.2);
      }

      .record-item-content {
        display: flex;
        align-items: center;
        padding: 16px;
        gap: 12px;

        .record-icon {
          width: 36px;
          height: 36px;
          background: linear-gradient(135deg, #e6f7ff 0%, #bae7ff 100%);
          border-radius: 8px;
          display: flex;
          align-items: center;
          justify-content: center;
          color: #1890ff;
          font-size: 16px;
          flex-shrink: 0;
        }

        .record-info {
          flex: 1;
          min-width: 0;

          .record-name {
            font-size: 15px;
            font-weight: 500;
            color: #262626;
            margin-bottom: 2px;
            line-height: 1.4;
            word-break: break-all;
          }

          .record-desc {
            font-size: 12px;
            color: #8c8c8c;
            line-height: 1.3;
          }
        }

        .record-arrow {
          color: #bfbfbf;
          font-size: 12px;
          transition: all 0.2s ease;
          flex-shrink: 0;
        }
      }
    }
  }

  .popup-footer {
    padding: 12px 24px 16px;
    background: #fafafa;
    border-top: 1px solid #f0f0f0;

    .footer-tip {
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 6px;
      font-size: 12px;
      color: #8c8c8c;

      .anticon {
        font-size: 14px;
      }
    }
  }
}

// 响应式设计
@media (max-width: 480px) {
  .record-select-popup {
    min-width: 280px;
    max-width: 90vw;

    .popup-header {
      padding: 16px 20px 12px;

      .header-title {
        font-size: 16px;
      }
    }

    .record-list .record-item .record-item-content {
      padding: 12px;
      gap: 10px;

      .record-icon {
        width: 32px;
        height: 32px;
        font-size: 14px;
      }

      .record-info .record-name {
        font-size: 14px;
      }
    }
  }
}

// 空状态样式
.record-list:empty::after {
  content: '暂无档案数据';
  display: block;
  text-align: center;
  color: #bfbfbf;
  font-size: 14px;
  padding: 40px 20px;
}
</style>
