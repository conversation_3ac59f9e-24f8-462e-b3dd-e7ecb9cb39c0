<template>
  <div class="all">
    <!-- 小区档案 -->
    <MapGlS ref="mapComponentRef" :legend @load="mapLoad">
      <template #rightTop>
        <div class="mar-30">
          <MapInputSearch @handlerZoneClick="handlerClick" />
        </div>
      </template>
    </MapGlS>
    <ModalDetails v-model="open" :data="detail" />
    <a-tooltip placement="left" title="档案列表">
      <div class="icon_box absolute pointer back-white box-shadow f-xy-center z-index-100 f-column border-R6">
        <ContainerOutlined style="font-size: 26px" @click="handlerMove" />
      </div>
    </a-tooltip>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import MapGlS from '@/components/MapGlS2/index.vue'
import { flyTo, transitionComponent } from '@/components/MapGlS2/utils'
import { getZoneData } from '@/services/modules/map'
import mapboxgl from 'mapbox-gl'
import { getSQL } from '@/services/modules/map'
import ModalDetails from '@/views/main/children/schedule/children/menu-map/children/menu/components/ModalDetails.vue'
import MapPopup from './components/RecordSelectPopup.vue'
import { ContainerOutlined } from '@ant-design/icons-vue'
import { useRouter } from 'vue-router'
import { ZONE_FILL_LAYER, ZONE2_LINE_LAYER, highlightZone } from '@/ownedByAllMapConfig'
import { storeToRefs } from 'pinia'
import useHomeStore from '@/store/home'
import * as API from '@/services/modules/menu-map'

// const { getMenu } = useHomeStore()
// const { open, detail } = storeToRefs(useHomeStore())
const open = ref(false)
const detail = ref({})

const legend = ref([{ label: '居住性质', color: '#6761f1', value: 0 }])

const component = transitionComponent(MapPopup)
const mapComponentRef = ref(null)

let Map

async function mapLoad(map) {
  Map = map
  await revampOptions()
  highlightZone(Map)
  insertMapPopup()
}

async function revampOptions() {
  const res = await getSQL('SELECT Zone_Code FROM [dbo].[DIST_ADDRESS]  where Client_Name is not NULL')
  const codeList = JSON.parse(res.data).map((item) => item.Zone_Code)
  legend.value[0].value = codeList.length
  const filter = ['match', ['get', 'Zone_Code'], codeList, '#6761f1', '#999999']

  const FT_ZONE = {
    type: 'vector',
    scheme: 'tms',
    tiles: ['https://www.szwgft.cn/mapServer/geoserver/gwc/service/tms/1.0.0/Code%3AFT_ZONE@EPSG%3A3857@pbf/{z}/{x}/{y}.pbf'],
    layers: {
      zone_fill: {
        options: {
          'source-layer': 'FT_ZONE',
          paint: { 'fill-opacity': ['match', ['get', 'Zone_Code'], codeList, 0.8, 0.4], 'fill-color': filter }
        }
      },
      zone_line: {
        options: { minzoom: 15, 'source-layer': 'FT_ZONE', paint: { 'line-width': 2, 'line-color': '#000' } }
      },
      zone2_line: {
        options: { minzoom: 15, 'source-layer': 'FT_ZONE', paint: { 'line-width': 4, 'line-color': '#07c160' }, filter: ['==', ['get', 'Zone_Name'], ''] }
      },
      zone_symbol: {
        options: { 'source-layer': 'FT_ZONE', minzoom: 15, layout: { 'text-field': ['get', 'Zone_Name'], 'text-size': 12 }, paint: { 'text-color': 'black', 'text-halo-color': 'white', 'text-halo-width': 1 } }
      }
    }
  }

  mapComponentRef.value.loadingLayer({ FT_ZONE })
}

// 添加区块信息弹窗
function insertMapPopup() {
  Map.on('click', ZONE_FILL_LAYER, async (e) => {
    const [feature] = Map.queryRenderedFeatures(e.point, { layers: [ZONE_FILL_LAYER] })
    const { properties } = feature
    const result = await getZoneData(properties.Zone_Code)
    const [res] = JSON.parse(result.data)
    if (res.Client_Name) {
      const separator = res.Client_Name.includes(';') ? ';' : '；'
      const xqbms = res.Client_Name.split(separator).filter((item) => !!item)
      if (xqbms.length > 1) {
        const records = await Promise.all(xqbms.map((item) => API.getFuzzyDetail(item)))
        const data = records.map((item) => {
          item.data.selectfn = (T) => {
            open.value = true
            detail.value = T
          }
          return item.data
        })
        const dom = component({ data })
        const popup = new mapboxgl.Popup({ maxWidth: 'none' })
        popup.setLngLat(e.lngLat).setDOMContent(dom).addTo(Map)
        return
      }
      const record = await API.getFuzzyDetail(xqbms[0])
      open.value = true
      detail.value = record.data
    }
  })
}
// 跳转区块与区块高亮
function handlerClick(e) {
  flyTo(Map, e.Center_Point.split(','), 17)
  Map.setFilter(ZONE2_LINE_LAYER, ['match', ['get', 'Zone_Name'], e.Zone_Name, true, false])
}

const router = useRouter()
function handlerMove() {
  router.push('/main/schedule/menu')
}
</script>

<style lang="less" scoped>
.icon_box {
  right: 30px;
  top: 150px;
  width: 40px;
  height: 40px;
  padding: 5px 3px;
}
</style>
