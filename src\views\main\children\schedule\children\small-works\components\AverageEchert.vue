<template>
  <div class="average-echert">
    <Echarts :option />
  </div>
</template>

<script setup>
import { computed } from 'vue'
import Echarts from '@/components/Echarts/index.vue'
const props = defineProps({ averageVal: Object })

const option = computed(() => ({
  title: { text: '项目进度平均时间', top: '4%', left: '2%' },
  tooltip: { trigger: 'axis', axisPointer: { type: 'shadow' }, formatter: '{b} 所用平均时间：{c}天' },
  grid: { left: '3%', right: '4%', bottom: '3%', containLabel: true },
  xAxis: [
    {
      type: 'category',
      data: props.averageVal.key ?? [],
      axisTick: { alignWithLabel: true },
      axisLabel: {
        // interval: 0,
        // rotate: 40,
        fontFamily: 'Microsoft YaHei',
        color: '#000', // x轴颜色
        fontWeight: 'normal',
        fontSize: '14',
        lineHeight: 22,
        interval: 0, //标签设置为全部显示
        margin: 15,
        lineHeight: 15,
        // fontSize: 11,
        formatter: function (params) {
          const arr = params.slice(0, 6)
          const tail = params.slice(6, params.length)
          const str = arr + '\n' + tail
          return tail === '' ? arr : str
        }
      }
    }
  ],
  yAxis: [
    {
      type: 'value',
      axisLabel: { formatter: '{value} 天' },
      axisLine: { show: true, onZero: true, onZeroAxisIndex: null, lineStyle: { color: ' #6E7079', width: 1, type: 'solid' } }
    }
  ],

  series: [
    {
      name: '项目',
      type: 'bar',
      label: { show: true, position: 'top', color: 'black' },
      showBackground: true,
      backgroundStyle: { color: 'rgba(180, 180, 180, 0.2)' },
      barWidth: '30%',
      data: props.averageVal.value ?? []
    }
  ]
}))
</script>

<style lang="less" scoped>
.average-echert {
  background-color: #f6f7f9;
}
</style>
