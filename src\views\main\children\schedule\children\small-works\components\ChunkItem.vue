<template>
  <div class="chunk-item relative pad-22" v-if="Mydata">
    <div class="pad-B32 relative">
      <a-tooltip title="删除项目">
        <DeleteFilled class="fon-S28 absolute" v-if="Mydata.step === 1" @click="handleDelete" style="top: -10px; right: -10px; color: #fb211d" />
      </a-tooltip>
      <div class="top flex f-between">
        <div class="flex f-y-center">
          <CompassTwoTone style="font-size: 30px" />
          <div class="mar-L16">
            <div class="color-999 fon-S14">项目编号</div>
            <div class="fon-S16 mar-T10 fon-W600">{{ Mydata.prj_No }}</div>
          </div>

          <div style="margin-left: 20px">
            <div class="color-999 fon-S14">申请人</div>
            <div class="fon-S16 mar-T10 fon-W600">{{ Mydata.applicant ?? '暂无' }}</div>
          </div>

          <div style="margin-left: 20px">
            <div class="color-999 fon-S14">估算价（万）</div>
            <div class="fon-S16 mar-T10 fon-W600" style="color: #212e7e">{{ Mydata.est_price ?? '暂无' }}</div>
          </div>

          <div style="margin-left: 20px">
            <div class="color-999 fon-S14">项目类型</div>
            <div class="fon-S16 mar-T10 fon-W600">{{ Mydata.prj_Type ?? '暂无' }}</div>
          </div>

          <div style="margin-left: 20px">
            <div class="color-999 fon-S14">申报部门</div>
            <div class="fon-S16 mar-T10 fon-W600">{{ Mydata.department ?? '暂无' }}</div>
          </div>

          <div style="margin-left: 20px" class="f-y-center f-column">
            <div class="color-999 fon-S14">子项目</div>
            <a-upload :headers v-if="!Mydata?.file_Path1?.length" class="updata" v-model:file-list="files" name="file" accept=".pdf,.docx" :action @change="({ fileList }) => handleChangeFile(fileList[0], '子项目')">
              <a-tooltip title="导入子项目">
                <PlusSquareTwoTone class="fon-S30 pointer" />
              </a-tooltip>
            </a-upload>
            <a-tooltip v-else title="查看子项目">
              <LayoutTwoTone class="fon-S30 pointer" @click="gainSmallProjectList" />
            </a-tooltip>
          </div>
        </div>
      </div>

      <div class="mar-Y16 fon-S16 fon-W600">{{ Mydata.prj_Name }}</div>
      <div class="relative W100 f-xy-center" style="height: 100px">
        <div style="left: 0; top: 30%; height: 3px; background-color: #3c7bf1" class="absolute wire"></div>
        <div class="mar-T24 flex f-between absolute all">
          <div class="item_icon f-column f-xy-center relative pointer">
            <a :href="Mydata.file_Path" target="_blank">
              <a-tooltip :title="Mydata?.file_Path?.length ? '查看原件' : '无原件'">
                <ReconciliationTwoTone class="fon-S18 icon" />
              </a-tooltip>
            </a>
            <div class="absolute nowrap fon-W600 fon-S16" style="bottom: -30px">立项</div>
            <div class="absolute nowrap fon-S14" style="bottom: -55px">{{ Mydata?.prj_InitTime?.slice(0, 10) }}</div>
          </div>

          <div class="item_icon f-column f-xy-center relative pointer" :class="{ fallshort: Mydata.step < 1 }">
            <a-upload :headers v-if="Mydata.step == 1" class="updata" v-model:file-list="files" name="file" accept=".jpg,.png" action="https://www.szwgft.cn:5000/api/Ocr/OCR" @change="(e) => handleChangeFile(e.fileList[0], '采购', e)">
              <PlusOutlined style="color: #1890ff" class="fon-S26" />
            </a-upload>
            <a :href="Mydata.file_Purchase" target="_blank" v-else>
              <a-tooltip :title="Mydata?.file_Purchase?.length ? '查看原件' : '无原件'">
                <ShopTwoTone class="fon-S18 icon" />
              </a-tooltip>
            </a>
            <div class="absolute nowrap fon-W600 fon-S16" style="bottom: -30px">采购</div>
            <div class="absolute nowrap fon-S14" style="right: 50px; top: 0px" v-if="Mydata.step >= 2">{{ countTime(Mydata.prj_InitTime, Mydata.purchase_Time) }}</div>
            <div class="absolute nowrap fon-S14" style="bottom: -55px" v-if="Mydata.step >= 2">{{ Mydata?.purchase_Time?.slice(0, 10) }}</div>
          </div>

          <div class="item_icon f-column f-xy-center relative pointer" :class="{ fallshort: Mydata.step < 2 }">
            <a-upload :headers v-if="Mydata.step == 2" class="updata" v-model:file-list="files" name="file" accept=".pdf,.docx" :action @change="({ fileList }) => handleChangeFile(fileList[0], '合同')">
              <PlusOutlined style="color: #1890ff" class="fon-S26" />
            </a-upload>
            <a :href="Mydata.file_Contract" target="_blank" v-else>
              <a-tooltip :title="Mydata?.file_Contract?.length ? '查看原件' : '无原件'">
                <ContainerTwoTone class="fon-S18 icon" />
              </a-tooltip>
            </a>
            <div class="absolute nowrap fon-W600 fon-S16" style="bottom: -30px">合同签订</div>
            <div class="absolute nowrap fon-S14" style="right: 50px; top: 0px" v-if="Mydata.step >= 3">{{ countTime(Mydata.purchase_Time, Mydata.contract_Time) }}</div>
            <div class="absolute nowrap fon-S14" style="bottom: -55px" v-if="Mydata.step >= 3">{{ Mydata?.contract_Time?.slice(0, 10) }}</div>
          </div>

          <div class="item_icon f-column f-xy-center relative pointer" :class="{ fallshort: Mydata.step < 3 }">
            <a-upload :headers v-if="Mydata.step == 3" class="updata" v-model:file-list="files" name="file" accept=".pdf,.docx" :action @change="({ fileList }) => handleChangeFile(fileList[0], '初审')">
              <PlusOutlined style="color: #1890ff" class="fon-S26" />
            </a-upload>
            <a :href="Mydata.file_Examine" target="_blank" v-else>
              <a-tooltip :title="Mydata?.file_Examine?.length ? '查看原件' : '无原件'">
                <AlertTwoTone class="fon-S18 icon" />
              </a-tooltip>
            </a>
            <div class="absolute nowrap fon-W600 fon-S16" style="bottom: -30px">初审时间</div>
            <div class="absolute nowrap fon-S14" style="right: 50px; top: 0px" v-if="Mydata.step >= 4">{{ countTime(Mydata.contract_Time, Mydata.examine_Time) }}</div>
            <div class="absolute nowrap fon-S14" style="bottom: -55px" v-if="Mydata.step >= 4">{{ Mydata?.examine_Time?.slice(0, 10) }}</div>
          </div>

          <div class="item_icon f-column f-xy-center relative pointer" :class="{ fallshort: Mydata.step < 4 }">
            <a-upload :headers v-if="Mydata.step == 4" class="updata" v-model:file-list="files" name="file" accept=".pdf,.docx" :action @change="({ fileList }) => handleChangeFile(fileList[0], '初审报告')">
              <PlusOutlined style="color: #1890ff" class="fon-S26" />
            </a-upload>
            <a :href="Mydata.file_Examine1" target="_blank" v-else>
              <a-tooltip :title="Mydata?.file_Examine1?.length ? '查看原件' : '无原件'">
                <AlertTwoTone class="fon-S18 icon" />
              </a-tooltip>
            </a>
            <div class="absolute nowrap fon-W600 fon-S16" style="bottom: -30px">初审报告</div>
            <div class="absolute nowrap fon-S14" style="right: 50px; top: 0px" v-if="Mydata.step >= 5">{{ countTime(Mydata.examine_Time, Mydata.examine_Time1) }}</div>
            <div class="absolute nowrap fon-S14" style="bottom: -55px" v-if="Mydata.step >= 5">{{ Mydata?.examine_Time1?.slice(0, 10) }}</div>
          </div>

          <!-- <div class="item_icon f-column f-xy-center relative pointer" :class="{ fallshort: Mydata.step < 5 }">
            <a-upload :headers v-if="Mydata.step == 5" class="updata" v-model:file-list="files" name="file" accept=".pdf,.docx" :action @change="({ fileList }) => handleChangeFile(fileList[0], '终审')">
              <PlusOutlined style="color: #1890ff" class="fon-S26" />
            </a-upload>
            <a :href="Mydata.file_Examine2" target="_blank" v-else>
              <a-tooltip :title="Mydata?.file_Examine2?.length ? '查看原件' : '无原件'">
                <AlertTwoTone class="fon-S18 icon" />
              </a-tooltip>
            </a>
            <div class="absolute nowrap fon-W600 fon-S16" style="bottom: -30px">复审时间</div>
            <div class="absolute nowrap fon-S14" style="right: 50px; top: 0px" v-if="Mydata.step >= 6">{{ countTime(Mydata.examine_Time1, Mydata.examine_Time2) }}</div>
            <div class="absolute nowrap fon-S14" style="bottom: -55px" v-if="Mydata.step >= 6">{{ Mydata?.examine_Time2?.slice(0, 10) }}</div>
          </div> -->

          <div class="item_icon f-column f-xy-center relative pointer" :class="{ fallshort: Mydata.step < 5 }">
            <a-upload :headers v-if="Mydata.step == 5" class="updata" v-model:file-list="files" name="file" accept=".pdf,.docx" :action @change="({ fileList }) => handleChangeFile(fileList[0], '终审报告')">
              <PlusOutlined style="color: #1890ff" class="fon-S26" />
            </a-upload>
            <a :href="Mydata.file_Examine3" target="_blank" v-else>
              <a-tooltip :title="Mydata?.file_Examine3?.length ? '查看原件' : '无原件'">
                <AlertTwoTone class="fon-S18 icon" />
              </a-tooltip>
            </a>
            <div class="absolute nowrap fon-W600 fon-S16" style="bottom: -30px">复审报告</div>
            <div class="absolute nowrap fon-S14" style="right: 50px; top: 0px" v-if="Mydata.step >= 6">{{ countTime(Mydata.examine_Time1, Mydata.examine_Time3) }}</div>
            <div class="absolute nowrap fon-S14" style="bottom: -55px" v-if="Mydata.step >= 6">{{ Mydata?.examine_Time3?.slice(0, 10) }}</div>
          </div>

          <div class="item_icon f-column f-xy-center relative pointer" :class="{ fallshort: Mydata.step < 6 }">
            <a-upload :headers v-if="Mydata.step == 6" class="updata" v-model:file-list="files" name="file" accept=".pdf,.docx" :action @change="({ fileList }) => handleChangeFile(fileList[0], '结算')">
              <PlusOutlined style="color: #1890ff" class="fon-S26" />
            </a-upload>
            <a :href="Mydata.file_Settlement" target="_blank" v-else>
              <a-tooltip :title="Mydata?.file_Settlement?.length ? '查看原件' : '无原件'">
                <DollarCircleTwoTone class="fon-S18 icon" />
              </a-tooltip>
            </a>
            <div class="absolute nowrap fon-W600 fon-S16" style="bottom: -30px">结算</div>
            <div class="absolute nowrap fon-S14" style="right: 50px; top: 0px" v-if="Mydata.step >= 7">{{ countTime(Mydata.examine_Time3, Mydata.settlement_Time) }}</div>
            <div class="absolute nowrap fon-S14" style="bottom: -55px" v-if="Mydata.step >= 7">{{ Mydata?.settlement_Time?.slice(0, 10) }}</div>
          </div>
        </div>
      </div>
    </div>
    <!-- 展示弹窗 -->
    <a-modal :footer="null" v-model:open="showData.open" width="1000px" title="子项目" centered>
      <SmallProject :data="showData.smallProject" />
    </a-modal>
  </div>
</template>

<script setup>
import { ref, reactive, computed } from 'vue'
import { CompassTwoTone, ReconciliationTwoTone, ShopTwoTone, DeleteFilled, ContainerTwoTone, AlertTwoTone, PlusOutlined, DollarCircleTwoTone, LayoutTwoTone, PlusSquareTwoTone } from '@ant-design/icons-vue'
import { analysisPdf, establishSmallProject, updataProject, deleteProject, gainSmallProject, updateFile } from '@/services/modules/small.works'
import chunk from 'lodash/chunk'
import { message } from 'ant-design-vue'
import SmallProject from './SmallProject.vue'
import Cache from '@/utils/cache'

// const props = defineProps({ isActive: Boolean, data: Object })
const Mydata = defineModel()
const emit = defineEmits(['changeDelete'])
const action = 'https://www.szwgft.cn/nodeServer/update?fileType=pdf'
const wireWidth = computed(() => (((Mydata.value.step ?? 1) - 1) / 6) * 100 + '%')
const files = ref([]) // 文件列表
const headers = computed(() => ({ Authorization: `Bearer ${Cache.get('userInfo')?.token}` }))

const showData = reactive({ open: false, show: '', smallProject: null }) // 核对弹窗数据

const updataS = { stroe: true, hide: null }
// 文件上传
async function handleChangeFile({ response }, type, e) {
  if (updataS.stroe) {
    updataS.hide = message.loading('正在解析', 0)
    updataS.stroe = false
  }
  try {
    if (!response) return
    const url = response?.data?.https
    const fns = {
      子项目: () => handleSmallProject(url),
      采购: () => handlePurchase(response, e.file),
      合同: () => handleContract(url),
      初审: () => handleFirstTrial(url),
      初审报告: () => handleFirstTrialReport(url),
      终审: () => handleFirstTrial2(url),
      终审报告: () => handleFirstTrial2Report(url),
      结算: () => handlCloseAnAccount(url)
    }
    await fns[type]()
    files.value = []
  } catch (error) {
    files.value = []
    updataS.hide()
    message.error(error.message)
  }
}
// 文件解析
async function analysisFile(filePath) {
  try {
    const url = filePath.replace('http://www.szwgft.cn/nodeServer/', '')
    const { data } = await analysisPdf(url)
    return data
  } catch (error) {
    message.error('解析失败' + error.message)
  }
}

// 新增子项目
async function handleSmallProject(url) {
  const res = await analysisFile(url)
  const prj_No = res.match(/项目编号：(\w+)/)[1]
  const [text] = res.match(/(?<=（万）)(.*?)(?=合计)/s) ?? []
  if (!text) throw new Error('解析错误')
  const str = text.split('\n').filter((i) => i)

  const data = chunk(str, 6)
    .map((i) => i.slice(1, 6))
    .filter((i) => i.length)

  const strs = data.map((i, c) => `${c + 1}.${i[0]}，施工原因：${i[1]}，施工方案：${i[2]}，时间：${i[3]}，费用：${i[4]}万`).join('\n')

  if (prj_No !== Mydata.value.prj_No) throw new Error('项目编号不匹配')
  for (let i = 0; i < data.length; i++) {
    const item = data[i].map((i) => i.trim())
    const [prj_Name, implement_Reason, construction_Scheme, time, cost] = item
    const [s_Time, e_Time] = time.split('至').map((i) => {
      const t = i
        .trim()
        .replace(/[年|月]/g, '-')
        .replace(/[日]/g, '')
      return new Date(t)
    })
    const T = { prj_No, prj_Name, implement_Reason, construction_Scheme, s_Time, e_Time, cost }

    await establishSmallProject(T) // 提交子项目数据
  }
  updataProjectContent({ file_Path1: url, sub_LX: strs }) //更新
}
// 获取项目列表
async function gainSmallProjectList() {
  if (showData.smallProject) {
    showData.open = true
    return
  }
  try {
    const data = await gainSmallProject(Mydata.value.prj_No)
    showData.smallProject = data
    showData.open = true
  } catch (error) {
    message.error(error.message)
  }
}

// 删除项目
async function handleDelete() {
  try {
    const { data } = await deleteProject(Mydata.value.id)
    if (data) {
      message.success('删除成功')
      emit('changeDelete')
    }
  } catch (error) {
    message.error('删除失败' + error.message)
  }
}
//更新项目内容
async function updataProjectContent(data) {
  try {
    const parameter = Object.assign(Mydata.value, data)
    const res = await updataProject(parameter)
    Mydata.value = res.data

    updataS.stroe = true
    updataS.hide()
  } catch (error) {
    updataS.hide()
    message.error('更新失败' + error.message)
  }
}

// 采购
async function handlePurchase(response, { originFileObj }) {
  const timeStr = response.text.match(/发布时间.*\n/)[0]
  if (!timeStr) throw new Error('读取失败，请查看图片内容完整')
  const time = new Date(timeStr.match(/\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}/)[0])
  // 上传文件
  const formData = new FormData()
  formData.append('file', originFileObj)
  const { data } = await updateFile(formData)
  updataProjectContent({ file_Purchase: data.https, purchase_Time: time, step: 2 })
}
// 合同
async function handleContract(url) {
  const text = await analysisFile(url)
  if (!/合同/.test(text)) throw new Error('文件内容不包含合同')
  const txt = text.replace(/\n/g, '')
  const a = txt.match(/）.*号经/)[0].replace(/）|经/g, '')
  const b = text.match(/合同名称(.*)/)[0].replace('合同名称', '')
  const c = text.match(/签约方2(.*)/)[0].replace('签约方2', '')
  const d = text.match(/合同金额(.*)/)[0].match(/\d+/g)[0]

  const e = new Date(txt.match(/单位负责人意见.*合同文本/)[0].match(/\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}/)[0])

  updataProjectContent({ file_Contract: url, contract_Time: e, step: 3, contract_price: d, contract_No: a, contract_Name: b, contract_Party: c })
}
// 初审
async function handleFirstTrial(url) {
  try {
    const time = new Date()
    await updataProjectContent({ examine_Time: time, file_Examine: url, step: 4 })
  } catch (error) {
    message.error('更新失败')
  }
}
// 初审报告
async function handleFirstTrialReport(url) {
  try {
    const time = new Date()
    await updataProjectContent({ file_Examine1: url, examine_Time1: time, step: 5 })
  } catch (error) {
    message.error('更新失败')
  }
}
// 终审
async function handleFirstTrial2(url) {
  try {
    const time = new Date()
    await updataProjectContent({ file_Examine2: url, examine_Time2: time, step: 6 })
  } catch (error) {
    message.error('更新失败')
  }
}
// 终审报告
async function handleFirstTrial2Report(url) {
  try {
    const time = new Date()
    await updataProjectContent({ file_Examine3: url, examine_Time3: time, step: 7 })
  } catch (error) {
    message.error('更新失败')
  }
}
// 结算
async function handlCloseAnAccount(url) {
  try {
    const time = new Date()
    await updataProjectContent({ file_Settlement: url, settlement_Time: time, step: 8 })
  } catch (error) {
    message.error('更新失败')
  }
}

function countTime(t1, t2) {
  const D = new Date(t2) - new Date(t1)
  return Math.floor(D / 1000 / 60 / 60 / 24) + '天'
}
</script>

<style lang="less" scoped>
.chunk-item {
  height: 260px;
  width: 49%;
  background-color: white;
  border-radius: 12px;
  transition: all 0.6s ease-in-out;
  margin-bottom: 12px;
  border: 1px solid #ccc;
}

.active {
  height: 500px !important;
}
.item_icon {
  width: 35px;
  height: 35px;
  border: 3px solid #3c7bf1;
  background-color: white;
  border-radius: 50%;
}

.wire {
  width: v-bind(wireWidth) !important;
  transition: all 0.6s ease-in-out;
}

.fallshort {
  border: 3px solid #999 !important;
  .icon {
    opacity: 0.3 !important;
  }
}
.updata:deep(.ant-upload-list) {
  display: none;
}
</style>
