<template>
  <div class="all">
    <Echarts :option />
  </div>
</template>

<script setup>
import { computed } from 'vue'
import Echarts from '@/components/Echarts/index.vue'
const props = defineProps({
  data: {
    type: Array,
    default: () => [
      { name: '项目总数', value: 0, color: '#009688' },
      { name: '异常数', value: 0, color: '#009688' },
      { name: '正常数', value: 0, color: '#009688' }
    ]
  }
})

function conduct(val) {
  const maxSize = 120
  const values = (val ?? []).map((item) => item.value)
  const max = Math.max(...values)
  return (val ?? []).map(({ name, value, color }) => {
    const size = value / max < 0.3 ? 0.3 * maxSize : (value / max) * maxSize
    return { name: name + value, value, symbolSize: size, draggable: true, itemStyle: { shadowBlur: 10, shadowColor: color, color } }
  })
}

const option = computed(() => {
  return {
    tooltip: {},
    title: { text: '项目数量统计', top: '4%', left: '2%' },
    animationDurationUpdate: (idx) => idx * 100,
    animationEasingUpdate: 'bounceIn',
    series: [
      {
        type: 'graph',
        layout: 'force',
        force: { repulsion: 230, edgeLength: 10 },
        roam: true,
        label: { show: true },
        data: conduct(props.data)
      }
    ]
  }
})
</script>
