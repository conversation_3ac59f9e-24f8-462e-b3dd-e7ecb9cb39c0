<template>
  <div class="wrap">
    <div class="row fon-W600 flex">
      <template v-for="item in headers" :key="item">
        <div class="td">{{ item }}</div>
      </template>
    </div>
    <template v-for="(item, index) in data" :key="item">
      <div class="flex row">
        <div class="td">{{ index + 1 }}</div>
        <div class="td">{{ item.prj_Name }}</div>
        <div class="td">{{ item.implement_Reason }}</div>
        <div class="td">{{ item.construction_Scheme }}</div>
        <div class="td">{{ item.s_Time + ' ~ ' + item.e_Time }}</div>
        <!-- <div class="td">{{ item.s_Time && item?.s_Time.slice(0, 10) + ' ~ ' + item.e_Time && item?.e_Time.slice(0, 10) }}</div> -->
        <div class="td">{{ item.cost }}</div>
      </div>
    </template>
  </div>
</template>

<script setup>
defineProps({ data: Array })

const headers = ['序号', '项目名称', '实施原因', '施工方案', '预计工期', '费用（万）']
</script>

<style lang="less" scoped>
.wrap {
  max-height: 600px;
  overflow: auto;
}
.td {
  border: 1px solid #eee;
  display: flex;
  justify-content: center;
  align-items: center;
  text-align: center;
  padding: 12px 0;
}
.row {
  & > div:nth-child(1) {
    width: 50px;
  }
  & > div:nth-child(2) {
    width: 150px;
  }
  & > div:nth-child(3) {
    width: 200px;
  }
  & > div:nth-child(4) {
    width: 260px;
  }
  & > div:nth-child(5) {
    width: 170px;
  }
  & > div:nth-child(6) {
    width: 90px;
  }
}
</style>
