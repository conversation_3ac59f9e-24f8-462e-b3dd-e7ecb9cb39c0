<template>
  <div class="wrap">
    <Echarts :option />
  </div>
</template>

<script setup>
import { computed } from 'vue'
import Echarts from '@/components/Echarts/index.vue'
const props = defineProps({ ProjectsSumCount: Object })

const option = computed(() => ({
  title: { text: '项目阶段金额统计', top: '4%', left: '2%' },
  tooltip: { trigger: 'axis' },
  legend: { itemWidth: 20, itemHeight: 20, right: 20, top: '4%' },
  grid: [
    {
      left: '12%',
      top: '25%',
      right: '8%',
      bottom: '20%'
    }
  ],
  xAxis: [
    {
      type: 'category',

      data: ['全部', '立项', '采购', '合同', '审核', '结算'],
      axisTick: { alignWithLabel: true },
      axisLabel: {
        // interval: 0,
        // rotate: 40,
        fontFamily: 'Microsoft YaHei',
        color: '#000', // x轴颜色
        fontWeight: 'normal',
        fontSize: '14',
        lineHeight: 22,
        interval: 0, //标签设置为全部显示
        margin: 15,
        lineHeight: 15,
        // fontSize: 11,
        formatter: function (params) {
          const arr = params.slice(0, 6)
          const tail = params.slice(6, params.length)
          const str = arr + '\n' + tail
          return tail === '' ? arr : str
        }
      }
    }
  ],
  yAxis: [{ type: 'value', axisLabel: { formatter: '{value} 万' }, axisLine: { show: true, onZero: true, onZeroAxisIndex: null, lineStyle: { color: ' #6E7079', width: 1, type: 'solid' } } }],

  series: [
    {
      name: '项目金额累计',
      type: 'bar',
      label: { show: true, position: 'top', color: 'black' },
      showBackground: true,
      backgroundStyle: { color: 'rgba(180, 180, 180, 0.2)' },
      barWidth: '30%',
      data: Object.values(props.ProjectsSumCount ?? {})
    }
  ]
}))
</script>
