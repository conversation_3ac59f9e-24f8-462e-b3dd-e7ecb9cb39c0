<template>
  <div class="all wrap pad-30 flex">
    <!-- 左侧 -->
    <div class="f-2 flex f-column">
      <!-- 操作栏 -->
      <div class="pad-12 border-R10 flex f-y-center back-white flex f-between hander" style="border: 1px solid #66666655">
        <div class="fon-S20 fon-W600">小额项目</div>
        <div class="flex">
          <a-upload class="updata mar-R12" v-model:file-list="files" name="file" :headers accept=".pdf" action="https://www.szwgft.cn/nodeServer/update?fileType=pdf" @change="handleChangeFile">
            <a-button type="primary" size="large" block> 新建项目</a-button>
          </a-upload>
          <a-tooltip title="项目状态筛选">
            <a-select size="large" class="mar-L12" @change="changeSelect" v-model:value="seek.abnormal" placeholder="选择项目状态" style="width: 150px">
              <a-select-option :value="null">全部项目</a-select-option>
              <a-radio-button value="异常">异常项目</a-radio-button>
            </a-select>
          </a-tooltip>
          <a-tooltip title="申报部门筛选">
            <a-select size="large" @change="changeSelect" v-model:value="seek.department" placeholder="选择申报部门" style="width: 150px">
              <a-select-option :value="null">全部</a-select-option>
              <a-radio-button value="运营中心">运营中心</a-radio-button>
              <a-select-option value="福中水务所">福中水务所</a-select-option>
              <a-select-option value="福东水务所">福东水务所</a-select-option>
              <a-select-option value="梅林水务所">梅林水务所</a-select-option>
              <a-select-option value="香蜜水务所">香蜜水务所</a-select-option>
            </a-select>
          </a-tooltip>
          <a-tooltip title="所在阶段筛选">
            <a-select size="large" @change="changeSelect" v-model:value="seek.step" placeholder="选择阶段" style="width: 130px">
              <a-select-option :value="null">全部</a-select-option>
              <a-select-option :value="1">立项</a-select-option>
              <a-select-option :value="2">采购</a-select-option>
              <a-select-option :value="3">合同签订</a-select-option>
              <a-select-option :value="4">初审时间</a-select-option>
              <a-select-option :value="5">初审报告</a-select-option>
              <a-select-option :value="6">复审报告</a-select-option>
              <a-select-option :value="7">结算</a-select-option>
            </a-select>
          </a-tooltip>
          <a-button size="large" type="primary" @click="resetClick">重置</a-button>

          <a-input class="mar-L24" size="large" v-model:value="selectData.text" @pressEnter="searchProject" placeholder="项目搜索" style="width: 300px">
            <template #addonBefore>
              <a-tooltip title="选择搜索条件">
                <a-select v-model:value="selectData.type">
                  <a-select-option value="项目名称">项目名称</a-select-option>
                  <a-select-option value="项目编号">项目编号</a-select-option>
                </a-select>
              </a-tooltip>
            </template>
            <template #addonAfter>
              <a-tooltip title="搜索"> <SearchOutlined class="fon-S22" @click="searchProject" /> </a-tooltip>
            </template>
          </a-input>
        </div>
      </div>
      <!-- 内容 -->
      <div class="content flex f-between f-wrap pad-Y12">
        <template v-if="Projects.showData.length" v-for="(item, index) in Projects.showData" :key="item.prj_No">
          <ChunkItem v-model="Projects.showData[index]" @click="active = item" @changeDelete="getFiltrateProject" :isActive="item === active" />
        </template>
        <a-empty style="width: 100%" v-else />
      </div>
      <div class="f-between flex back-white border-R12 pad-10" style="border: 1px solid #66666655">
        <div></div>
        <div class="flex f-y-center">
          <a-pagination :page-size-options="[10]" v-model:current="current" :total="Projects.total" @change="getFiltrateProject({ pageNumber: current })" />
          <a-tooltip title="导出所筛选的文件">
            <a-button type="primary" size="large" @click="exportFile" class="mar-L24">
              <template #icon> <DownloadOutlined /> </template>
              导出
            </a-button>
          </a-tooltip>
        </div>
      </div>

      <!-- 异常项目操作栏 -->
      <div class="pad-12 border-R10 mar-T10 flex f-y-center back-white flex f-between hander" style="border: 1px solid #66666655">
        <div class="fon-S20 fon-W600">异常项目</div>
        <div class="flex">
          <a-tooltip title="申报部门筛选">
            <a-select size="large" @change="screenAbnormalProject('下拉')" v-model:value="abnormalParameter.department" placeholder="选择申报部门" style="width: 150px">
              <a-select-option :value="null">全部</a-select-option>
              <a-radio-button value="运营中心">运营中心</a-radio-button>
              <a-select-option value="福中水务所">福中水务所</a-select-option>
              <a-select-option value="福东水务所">福东水务所</a-select-option>
              <a-select-option value="梅林水务所">梅林水务所</a-select-option>
              <a-select-option value="香蜜水务所">香蜜水务所</a-select-option>
            </a-select>
          </a-tooltip>
          <a-tooltip title="所在阶段筛选">
            <a-select size="large" @change="screenAbnormalProject('下拉')" v-model:value="abnormalParameter.step" placeholder="选择阶段" style="width: 130px">
              <a-select-option :value="null">全部</a-select-option>
              <a-select-option :value="1">立项</a-select-option>
              <a-select-option :value="2">采购</a-select-option>
              <a-select-option :value="3">合同签订</a-select-option>
              <a-select-option :value="4">初审时间</a-select-option>
              <a-select-option :value="5">初审报告</a-select-option>
              <a-select-option :value="6">复审报告</a-select-option>
              <a-select-option :value="7">结算</a-select-option>
            </a-select>
          </a-tooltip>
          <a-button size="large" type="primary" @click="screenAbnormalProject('重置')">重置</a-button>

          <a-input class="mar-L24" size="large" v-model:value="abnormalInput.text" @pressEnter="screenAbnormalProject(abnormalInput.type)" placeholder="项目搜索" style="width: 300px">
            <template #addonBefore>
              <a-tooltip title="选择搜索条件">
                <a-select v-model:value="abnormalInput.type">
                  <a-select-option value="项目名称">项目名称</a-select-option>
                  <a-select-option value="项目编号">项目编号</a-select-option>
                </a-select>
              </a-tooltip>
            </template>
            <template #addonAfter>
              <a-tooltip title="搜索"> <SearchOutlined class="fon-S22" @click="screenAbnormalProject(abnormalInput.type)" /> </a-tooltip>
            </template>
          </a-input>
        </div>
      </div>

      <div class="back-white border-R10 pad-16" style="height: 480px; border: 1px solid #66666655">
        <!-- <div class="f-2" style="border-right: 1px solid #66666655"><CountEchart :data="StateProjectsQuantity" /></div> -->
        <div class="flex f-column all">
          <div class="pad-14 f-between flex f-y-center border-B-eee">
            <div class="flex f-y-center">
              <div class="fon-W600 mar-R14">异常项目原因：</div>
              <div class="H100 flex f-y-center fon-W600" style="padding: 5px 16px; color: #ee6666; border-radius: 6px; border: 1px solid #66666655">初审时间在合同签订前</div>
              <div class="fon-W600 mar-L20">异常项目数量：</div>
              <div class="H100 flex f-y-center fon-W600" style="padding: 5px 16px; color: #ee6666; border-radius: 6px; border: 1px solid #66666655">{{ abnormalProjects.length }}</div>
            </div>
            <a-button type="primary" class="mar-L14" @click="exportAbnormalFile"
              ><template #icon> <DownloadOutlined /> </template>导出异常</a-button
            >
          </div>

          <div class="f-1 overflow-auto pad-10">
            <template v-for="item in abnormalProjects" :key="item.id">
              <div class="abnormalItem fon-S14 flex" style="background-color: #f6f7f9">
                <div class="text-nowrap" style="width: 700px; color: #333">{{ item.prj_Name }}</div>
                <div class="f-1" style="color: #ee6666; font-weight: 600">合同签订时间：{{ item.contract_Time?.slice(0, 10) }}</div>
                <div class="f-1" style="color: #ee6666; font-weight: 600">初审时间：{{ item.examine_Time?.slice(0, 10) }}</div>
                <div class="fon-W600" style="min-width: 120px; color: #a7ce9b; border-left: 1px solid #66666655; padding-left: 8px">金额：{{ item.est_price }}万</div>
              </div>
            </template>
          </div>
        </div>
      </div>
    </div>
    <!-- 右侧 -->
    <div class="f-1 back-white border-R10 pad-12 box-shadow mar-L24">
      <div class="mar-B12 flex f-between">
        <div class="fon-S22 fon-W600">小额项目数据概况</div>
        <a-radio-group v-model:value="segmentedValue" size="large" @change="handleChange">
          <a-radio-button value="全部">全部</a-radio-button>
          <a-radio-button value="运营中心">运营中心</a-radio-button>
          <a-radio-button value="福中水务所">福中所</a-radio-button>
          <a-radio-button value="福东水务所">福东所</a-radio-button>
          <a-radio-button value="梅林水务所">梅林所</a-radio-button>
          <a-radio-button value="香蜜水务所">香蜜所</a-radio-button>
        </a-radio-group>
      </div>
      <AverageEchert :averageVal style="height: 250px" class="box-shadow" />
      <div class="mar-Y12 flex f-between">
        <div></div>
        <a-range-picker class="mar-L12" v-model:value="times" style="width: 300px" @change="handleChange" valueFormat="YYYY-MM-DD" />
      </div>

      <div class="box-shadow flex" style="height: 280px; background-color: #f6f7f9">
        <CountEchart :data="StateProjectsQuantity" class="f-1" style="border-right: 1px solid #66666655" />
        <QuantityEchart :ProjectsCount class="f-1" />
      </div>

      <SumEchart :ProjectsSumCount class="all mar-T16 box-shadow" style="height: 280px" />
    </div>
  </div>

  <!-- 弹出层 -->
  <a-modal size="large" v-model:open="establish.open" @cancel="files = []" width="600px" title="新增项目" centered @ok="createProjectClick">
    <template v-for="item in Object.entries(establish.data)" :key="item[0]">
      <div>
        {{ item[0] }}： <b>{{ item[1] }}</b>
      </div>
    </template>
  </a-modal>
</template>

<script setup>
import { ref, reactive, watch, computed } from 'vue'
import ChunkItem from './components/ChunkItem.vue'
import { analysisPdf, establishProject, filtrateProject, projectNameLike, projectCode, getSteptime, getSmallPrj, filtrationProject, abnormalProject, projectParticulars } from '@/services/modules/small.works'
import { message } from 'ant-design-vue'
import { SearchOutlined, DownloadOutlined } from '@ant-design/icons-vue'
import AverageEchert from './components/AverageEchert.vue'
import QuantityEchart from './components/QuantityEchart.vue'
import CountEchart from './components/CountEchart.vue'
import SumEchart from './components/SumEchart.vue'
import { educeXlsx } from '@/utils/educeXlsx.js'
import projectKey from '@/assets/geojson/projectKey.json'
import Cache from '@/utils/cache'

const current = ref(1)
const active = ref(null)
const files = ref([])
const establish = reactive({ open: false, showData: [], data: [], time: null })
const selectData = reactive({ type: '项目名称', text: null })
const Projects = reactive({ data: [], showData: [], total: 0 })
const segmentedValue = ref('全部')
const headers = computed(() => ({ Authorization: `Bearer ${Cache.get('userInfo')?.token}` }))

const seek = ref({ department: null, step: null, abnormal: null, prj_Name: null, prj_No: null, pageNumber: 1, pageSize: 10 })
getFiltrateProject()
getStatisticsTime()
// 立项pdf上传
const updataS = { stroe: true, hide: null }
async function handleChangeFile(e) {
  if (updataS.stroe) {
    updataS.hide = message.loading('正在解析', 0)
    updataS.stroe = false
  }
  try {
    if (e.fileList[0]?.response?.data?.https) {
      const url = e.fileList[0].response.data.https.replace('http://www.szwgft.cn/nodeServer/', '')
      const { data } = await analysisPdf(url) // 解析pdf
      // 验证是否为立项pdf
      const isTrue = /立项申报/.test(data)
      if (!isTrue) throw new Error('解析失败')

      const code = data.match(/项目编号：.*\n/)[0].match(/\d+|\w+/)[0]
      const name = data.match(/申请人(.*)/)[0].replace('申请人', '')
      const name2 = data.match(/项目名称(.*)/)[0].replace('项目名称', '')
      const p = data.match(/估算价（万）(.*)/)[0].replace('估算价（万）', '')
      const p1 = data.match(/申报部门.*项目类型/)[0].replace(/申报部门|项目类型/g, '')
      const p2 = data.match(/项目类型(.*)/)[0].replace('项目类型', '')
      const timeStr = data.replace(/\n/g, '').match(/分管领导审核.*/)[0]
      const time = timeStr.match(/\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}/)[0]

      establish.data = { 项目编号: code, 项目名称: name2, 申请人: name, 估算价: p, 申报部门: p1, 项目类型: p2, 日期: time }
      establish.open = true
      updataS.stroe = true
      updataS.hide()
    }
  } catch (error) {
    updataS.hide()
    updataS.stroe = true
    message.error('解析失败，请重新上传')
  }
}

// 确认立项
async function createProjectClick() {
  try {
    const D = establish.data
    establish.open = false
    const T = {
      prj_No: D['项目编号'],
      prj_Name: D['项目名称'],
      applicant: D['申请人'],
      est_price: D['估算价'],
      prj_InitTime: new Date(D['日期']),
      prj_Type: D['项目类型'],
      department: D['申报部门'],
      file_Path: files.value[0]?.response?.data?.https,
      file_Path1: '',
      step: 1
    }

    await establishProject(T)
    getFiltrateProject()

    files.value = []
  } catch (error) {
    message.error(error.message)
  }
}

// 搜索
async function searchProject() {
  try {
    if (!selectData.text) return message.error('请输入搜索内容')
    seek.value = { ...seek.value, ...{ department: null, step: null, abnormal: null, pageNumber: 1 } }
    if (selectData.type == '项目名称') {
      seek.value.prj_Name = selectData.text
      seek.value.prj_No = null
    } else {
      seek.value.prj_No = selectData.text
      seek.value.prj_Name = null
    }
    await getFiltrateProject()
  } catch (error) {
    message.error('未找到相关项目')
  }
}

// 平均时间
const averageVal = ref({ key: [], value: [] })
async function getStatisticsTime() {
  try {
    const res = await getSteptime(segmentedValue.value)
    averageVal.value.value = res.map((i) => Math.floor(i.averageDays))
    averageVal.value.key = res.map((i) => i.metricName)
  } catch (error) {
    message.error(error.message)
  }
}

// 下拉框筛选
async function changeSelect() {
  seek.value.prj_Name = null
  seek.value.prj_No = null
  selectData.text = null
  current.value = 1
  getFiltrateProject({ pageNumber: 1 })
}

watch(segmentedValue, () => getStatisticsTime())

// 时间筛选项目统计数
const ProjectsCount = ref({ 立项: 0, 采购: 0, 合同: 0, 审核: 0, 结算: 0 })
const ProjectsSumCount = ref({ 立项: 0, 采购: 0, 合同: 0, 审核: 0, 结算: 0 })
const times = ref([])
async function handleChange() {
  const department = segmentedValue.value == '全部' ? '' : segmentedValue.value
  const s_Time = times.value && times.value[0] ? new Date(times.value[0]) : null
  const e_Time = times.value && times.value[1] ? new Date(new Date(times.value[1] + ' 23:59:59').getTime() + 1000 * 60 * 60 * 8) : null
  const res = await filtrationProject({ s_Time, e_Time, department })

  const count = { 立项: 0, 采购: 0, 合同: 0, 审核: 0, 结算: 0 }
  const sum = { 全部: 0, 立项: 0, 采购: 0, 合同: 0, 审核: 0, 结算: 0 }
  ;(res ?? []).forEach((i) => {
    if (i.step == 1) {
      count['立项']++
      sum['立项'] += i.est_price
    }
    if (i.step == 2) {
      count['采购']++
      sum['采购'] += i.est_price
    }
    if (i.step == 3) {
      count['合同']++
      sum['合同'] += i.est_price
    }
    if (i.step > 3 && i.step < 7) {
      count['审核']++
      sum['审核'] += i.est_price
    }
    if (i.step == 7) {
      count['结算']++
      sum['结算'] += i.est_price
    }
    sum['全部'] += i.est_price
  })
  Object.keys(sum).forEach((key) => (sum[key] = sum[key].toFixed(2)))
  ProjectsCount.value = count
  ProjectsSumCount.value = sum
  countProject(segmentedValue.value)
}
handleChange()

// 导出文件

async function exportFile() {
  const hide = message.loading({ content: '正在导出文件' })
  try {
    const P = { ...seek.value }
    delete P.pageNumber
    delete P.pageSize
    const { data } = await filtrateProject(P)

    educeXlsx(data, projectKey, '小额工程')
    hide()
  } catch (error) {
    message.error('导出失败')
    hide()
  }
}

// 获取项目列表
async function getFiltrateProject(parameter) {
  seek.value = { ...seek.value, ...parameter }
  try {
    const { data, totalRecords } = await filtrateProject(seek.value)
    Projects.showData = data
    Projects.total = totalRecords
  } catch (error) {
    message.error('未查询到数据')
  }
}

// 重置列表
function resetClick() {
  getFiltrateProject({ department: null, step: null, abnormal: null, prj_Name: null, prj_No: null, pageNumber: 1, pageSize: 10 })
  selectData.text = null
  current.value = 1
}

const StateProjectsQuantity = ref(null)
async function countProject(type) {
  try {
    const department = type === '全部' ? null : type
    const { totalRecords: all } = await filtrateProject({ department, step: null, abnormal: null, prj_Name: null, prj_No: null, pageNumber: 1, pageSize: 1 })
    const { totalRecords: abnormal } = (await filtrateProject({ department, step: null, abnormal: '异常', prj_Name: null, prj_No: null, pageNumber: 1, pageSize: 1 })) ?? { totalRecords: 0 }
    StateProjectsQuantity.value = [
      { name: '项目总数', value: all, color: '#4983f5' },
      { name: '异常数', value: abnormal, color: '#ee6666' },
      { name: '正常数', value: all - abnormal, color: '#3d993d' }
    ]
  } catch (error) {}
}
countProject('全部')

const abnormalParameter = reactive({ abnormal: '异常', department: null, step: null, prj_Name: null, prj_No: null })
const abnormalInput = reactive({ type: '项目名称', text: null })
const abnormalProjects = ref([])

async function screenAbnormalProject(type) {
  if (type === '重置') {
    abnormalParameter.department = null
    abnormalParameter.step = null
    abnormalParameter.prj_Name = null
    abnormalParameter.prj_No = null
    abnormalInput.text = null
  } else if (type === '下拉') {
    abnormalParameter.prj_Name = null
    abnormalParameter.prj_No = null
    abnormalInput.text = null
  } else if (type === '项目名称') {
    abnormalParameter.department = null
    abnormalParameter.step = null
    abnormalParameter.prj_No = null
    abnormalParameter.prj_Name = abnormalInput.text
  } else if (type === '项目编号') {
    abnormalParameter.department = null
    abnormalParameter.step = null
    abnormalParameter.prj_Name = null
    abnormalParameter.prj_No = abnormalInput.text
  }

  const { data } = await filtrateProject(abnormalParameter)
  abnormalProjects.value = data
}
screenAbnormalProject('重置')
// 导出异常项目文件
async function exportAbnormalFile() {
  if (abnormalProjects.value.length) {
    educeXlsx(abnormalProjects.value, projectKey, '小额工程异常项目')
  }
}
</script>

<style lang="less" scoped>
.works {
  padding: 20px 30px;
  height: 1080px;
}
.wrap {
  background-color: #f6f7f9;
  // padding: 16px 60px;
  .right {
    width: 1000px;
  }
}

.content {
  height: 280px;
  overflow-y: scroll;
}
.updata:deep(.ant-upload-list) {
  display: none;
}

.hander {
  border-bottom: 1px solid #ccc;
}

.icon_box {
  right: 30px;
  top: 160px;
  width: 40px;
  height: 40px;
  padding: 5px 3px;
  background-color: white;
  margin-right: 12px;
  border-radius: 6px;
  cursor: pointer;
}

.abnormalItem {
  width: 100%;
  padding: 10px;
  border-radius: 8px;
  border: 1px solid #66666655;
  margin: 6px 0;
}
</style>
