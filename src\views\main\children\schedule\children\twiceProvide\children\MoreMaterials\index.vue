<template>
  <div class="all">
    <a-table
      class="ant-table-striped"
      :columns="columns"
      :data-source="showList"
      :pagination="{ pageSize: 50 }"
      :scroll="{ x: 16000, y: 830 }"
      :loading
      :row-class-name="(_record, index) => (index % 2 === 1 ? 'table-striped' : null)"
      bordered
    >
      <template #title>
        <div class="f-between">
          <LeftCircleOutlined class="fon-S30 pointer color-666" style="left: 20px" @click="router.go(-1)" />
          <div>
            <a-select class="mar-L24" v-model:value="selectValue" style="width: 80px" :options @change="handleChange" />
            <a-select class="mar-L12" :disabled="!['街道', '网格', '片区'].includes(selectValue)" v-model:value="selectValue2" style="width: 130px" :options="options2" @change="handleChange2" />
            <a-input-search style="width: 260px" class="mar-L16" v-model:value="text" placeholder="输入泵站名称" enter-button @search="onSearch" />
            <a-button class="mar-L12" type="primary" @click="exportExcel">导出Excel</a-button>
          </div>
        </div>
      </template>
      <template #footer>Footer</template>
    </a-table>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'
import { getSecondaryWaterProgress, getPumpHouseNameLikeApi } from '@/services/modules/map'
import pumpHouseNodeKeys from '@/assets/geojson/pump_house.json'

import { message } from 'ant-design-vue'
import twiceProvideKeys from '@/assets/geojson/twiceProvideKeysAll.json'
import { LeftCircleOutlined } from '@ant-design/icons-vue'
import ExcelJS from 'exceljs'
import { useRouter } from 'vue-router'

const list = ref([])
const showList = ref([])
const router = useRouter()

const selectValue = ref('全部')
const selectValue2 = ref('选择')
const columns = computed(() => Object.entries(twiceProvideKeys).map(([dataIndex, title]) => ({ title, dataIndex, fixed: dataIndex === 'pumpHouseName' })))
const batchKeys = { 1: '已纳改:利源代建', 2: '已纳改:查漏补缺', 3: '需纳改', 4: '无需纳改', 5: '已纳改:应改未改' }

const options = [
  { value: '全部', label: '全部' },
  { value: '片区', label: '片区' },
  { value: '街道', label: '街道' },
  { value: '网格', label: '网格' },
  { value: '异常', label: '异常' },
  { value: '临供', label: '临供' },
  { value: '切换', label: '切换' },
  { value: '初验', label: '初验' }
]

const selects = {
  街道: [
    { value: '梅林街道办', label: '梅林街道办' },
    { value: '香蜜湖街道办', label: '香蜜湖街道办' },
    { value: '莲花街道办', label: '莲花街道办' },
    { value: '沙头街道办', label: '沙头街道办' },
    { value: '福保街道办', label: '福保街道办' },
    { value: '福田街道办', label: '福田街道办' },
    { value: '园岭街道办', label: '园岭街道办' },
    { value: '南园街道办', label: '南园街道办' },
    { value: '华强北街道办', label: '华强北街道办' },
    { value: '华富街道办', label: '华富街道办' }
  ],
  网格: [
    { value: '梅林-1', label: '梅林-1' },
    { value: '梅林-2', label: '梅林-2' },
    { value: '香蜜-1', label: '香蜜-1' },
    { value: '香蜜-2', label: '香蜜-2' },
    { value: '香蜜-3', label: '香蜜-3' },
    { value: '福中-1', label: '福中-1' },
    { value: '福中-2', label: '福中-2' },
    { value: '福中-3', label: '福中-3' },
    { value: '福东-1', label: '福东-1' },
    { value: '福东-2', label: '福东-2' }
  ],
  片区: [
    { value: '梅林片区', label: '梅林片区' },
    { value: '香蜜片区', label: '香蜜片区' },
    { value: '景田片区', label: '景田片区' },
    { value: '莲花片区', label: '莲花片区' },
    { value: '新洲片区', label: '新洲片区' },
    { value: '福保片区', label: '福保片区' },
    { value: '福民片区', label: '福民片区' },
    { value: '中心城片区', label: '中心城片区' },
    { value: '福东北片区', label: '福东北片区' },
    { value: '福东南片区', label: '福东南片区' }
  ]
}

const options2 = computed(() => (['街道', '网格', '片区'].includes(selectValue.value) ? selects[selectValue.value] : []))

async function getData() {
  try {
    const data = await getSecondaryWaterProgress()

    list.value = data
    showList.value = data
  } catch (error) {
    message.error(error.message)
  }
}
getData()

async function onSearch(e) {
  if (!e) return message.error('请输入泵站名称')
  const { data } = await getPumpHouseNameLikeApi(e)
  showList.value = data.result
  selectValue.value = '选择'
}

function handleChange(value) {
  if (value === '全部') showList.value = list.value
  if (value === '异常') showList.value = list.value.filter((item) => item.progressStatus != '正常')
  if (value === '临供') showList.value = list.value.filter((item) => item.current < 10)
  if (value === '切换') showList.value = list.value.filter((item) => item.current < 14)
  if (value === '初验') showList.value = list.value.filter((item) => item.current >= 14)
  selectValue2.value = '选择'
}

function handleChange2(value) {
  const key = selectValue.value === '片区' ? 'belongingArea' : selectValue.value === '网格' ? 'gridding' : 'belongingStreet'
  showList.value = list.value.filter((item) => item[key] === value)
}

// 文件导出
async function exportExcel() {
  const header = Object.entries(twiceProvideKeys).map(([key, header]) => ({ header, key }))
  const workbook = new ExcelJS.Workbook()
  const sheet = workbook.addWorksheet('Sheet1')
  sheet.columns = header
  const hide = message.loading('正在导出文件...', 0)
  // 添加数据
  const keys = Object.values(pumpHouseNodeKeys)
  showList.value.forEach((item) => {
    item.current = keys[item.current - 1]
    item.type = batchKeys[item.type]
    sheet.addRow(item)
  })

  // 文件下载
  const buffer = await workbook.xlsx.writeBuffer()
  const blob = new Blob([buffer], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' })
  const url = window.URL.createObjectURL(blob)
  const a = document.createElement('a')
  a.href = url
  a.download = `${new Date().getTime()}.xlsx`
  a.click()
  window.URL.revokeObjectURL(url)
  hide()
}
</script>

<style lang="less" scoped>
.ant-table-striped :deep(.table-striped) td {
  background-color: #f80e0e;
}
.ant-table-striped :deep(.table-striped) td {
  background-color: #eee;
}
.ant-table-striped :deep(.ant-spin) {
  max-height: none;
  height: 100vh;
}
.ant-table-striped :deep(.ant-table-thead > tr > th) {
  background-color: #aaaaa9;
  color: #111;
  text-align: center;
}
.ant-table-striped :deep(.ant-table-tbody > tr > td) {
  color: #111;
  text-align: center;
}
</style>
