<template>
  <div class="general-situation border-666 flex f-column overflow-hidden z-index-100 box-shadow pad-10 border-R12 absolute" :class="{ fold: fold }">
    <div class="absolute z-index-100 back-white pointer pad-10 fold-icon" @click="fold = !fold"><MenuFoldOutlined class="fon-S26 color-666" v-if="!fold" /> <MenuUnfoldOutlined class="fon-S26 color-666" v-else /></div>
    <div class="all" style="height: 900px" @click.prevent="isShow = false">
      <div class="title fon-S18 fon-W600 mar-R30 flex">
        <a-tooltip title="泵房搜索">
          <div style="width: 38px; height: 38px" class="box-shadow f-xy-center pointer border-R10" @click.stop="isShow = !isShow">
            <SearchOutlined class="icon fon-S22 color-666 pad-B4" v-if="!isShow" /><CloseOutlined class="icon fon-S22 color-666 pad-B4" v-else />
          </div>
        </a-tooltip>
        <a-tooltip title="详细列表">
          <div style="width: 38px; height: 38px" class="box-shadow mar-L10 f-xy-center pointer border-R10" @click="router.push('/MoreMaterials')"><ContainerOutlined class="icon fon-S22 color-666 pad-B4" /></div>
        </a-tooltip>

        <a-tooltip title="查询时间段内有更新的泵房">
          <a-range-picker class="mar-L12" v-model:value="times" style="width: 280px" @change="handleTimeChange" valueFormat="YYYY-MM-DD" />
        </a-tooltip>
        <a-button class="mar-L10" type="primary" @click="getAll">全部</a-button>
      </div>
      <div @click.stop="" :class="{ 'seek-active': isShow }" class="seek overflow-hidden border-R10 absolute z-index-100 back-white box-shadow"><slot></slot></div>

      <div class="box-shadow mar-T6 pad-10 border-666 border-R6 flex f-column" style="height: 200px" @mouseenter="mouseenterItem()" @mouseleave="mouseleaveItem()">
        <div class="flex f-y-center f-between">
          <div class="fon-W600 color-333 fon-S18 mar-B12">{{ selectValue }}统计 - ({{ pumpHouseDots.length }}) 个</div>
          <a-tooltip title="刷新" placement="left">
            <a-button type="primary" @click="handleChange">
              <template #icon> <RedoOutlined /> </template>
            </a-button>
          </a-tooltip>
        </div>
        <div class="flex f-wrap f-between pad-X10 f-1 overflow-auto">
          <template v-for="(item, index) in showData" :key="item[0]">
            <div class="item f-y-center f-between mar-B10 pointer pad-10 border-eee mar-R10" style="background-color: #f6f7f9; height: 35px; min-width: 180px" @click="handlerClickItem(item[0], index)">
              <div class="fon-W600">{{ item[0] }}：</div>
              <div>{{ item[1] }} 个</div>
            </div>
          </template>
        </div>
      </div>

      <div class="pad-Y10 mar-T10 border-R6 border-666 box-shadow" style="height: 335px"><Echarts :option :events="{ click: eventBarClick }" /></div>

      {{ pumpHouseStatistics }}
      <div class="mar-T10 pad-10 border-R6 border-666 box-shadow relative" style="height: 300px">
        <a-tooltip title="筛选泵房数据,可点击饼图进行更加详细的筛选" placement="topRight">
          <a-select v-model:value="pumpHouseShowValue" style="width: 140px; right: 90px" @change="handlepumpHouseShowValue" class="absolute z-index-100">
            <a-select-option value="state">主要节点</a-select-option>
            <a-select-option value="RemouldState">改造状态</a-select-option>
            <a-select-option value="OperationManagementState">运营管理状态</a-select-option>
            <a-select-option value="Batch">批次</a-select-option>
            <a-select-option value="isCollect">位置采集</a-select-option>
          </a-select>
        </a-tooltip>
        <a-tooltip title="刷新" placement="top">
          <a-button type="primary" style="right: 50px" @click="handlepumpHouseShowValue" class="absolute z-index-100">
            <template #icon> <RedoOutlined /> </template>
          </a-button>
        </a-tooltip>
        <a-tooltip title="导出地图所筛选的数据" placement="bottomRight">
          <a-button type="primary" style="right: 10px" @click="handlerExport" class="absolute z-index-100">
            <template #icon> <DownloadOutlined /> </template>
          </a-button>
        </a-tooltip>
        <a-tooltip title="请先点击饼图进行筛选" placement="topLeft">
          <a-cascader class="absolute z-index-100 cascader_c" style="width: 220px" :disabled="!pieFilter" v-model:value="cascaderValue" :options="cascaderOptions" placeholder="请选择" @change="handleCascaderChange" />
        </a-tooltip>

        <Echarts :option="optionPie" :events="{ click: eventPieClick }" />
      </div>
    </div>

    <!-- 片区详情 -->
    <div v-if="!fold" class="detail z-index-100 back-white border-R12 absolute box-shadow-enhanced" :class="{ active: detail.isShow }">
      <div class="detail-content" v-if="detail.data">
        <!-- 标题区域 -->
        <div class="detail-header">
          <div class="area-name">{{ detail.data.AreaName }}</div>
          <div class="detail-divider"></div>
        </div>

        <!-- 详情内容区域 -->
        <div class="detail-body">
          <!-- 代建单位信息 -->
          <div class="info-section">
            <div class="section-title">
              <span class="section-icon">🏢</span>
              代建单位信息
            </div>
            <div class="info-item">
              <span class="info-label">代建单位</span>
              <span class="info-value">{{ detail.data.Cons_tion || '暂无信息' }}</span>
            </div>
            <div class="info-item">
              <span class="info-label">负责人</span>
              <span class="info-value">{{ detail.data.Per_Char || '暂无信息' }}</span>
            </div>
            <div class="info-item">
              <span class="info-label">联系电话</span>
              <span class="info-value phone-number">{{ detail.data.Con_Number || '暂无信息' }}</span>
            </div>
          </div>

          <!-- 分公司信息 -->
          <div class="info-section">
            <div class="section-title">
              <span class="section-icon">🏛️</span>
              分公司信息
            </div>
            <div class="info-item">
              <span class="info-label">负责人</span>
              <span class="info-value">{{ detail.data.Sub_Compan || '暂无信息' }}</span>
            </div>
            <div class="info-item">
              <span class="info-label">联系电话</span>
              <span class="info-value phone-number">{{ detail.data.Sub_Number || '暂无信息' }}</span>
            </div>
          </div>

          <!-- 施工单位信息 -->
          <div class="info-section">
            <div class="section-title">
              <span class="section-icon">🔧</span>
              施工单位信息
            </div>
            <div class="info-item">
              <span class="info-label">施工单位</span>
              <span class="info-value">{{ detail.data.Const_Unit || '暂无信息' }}</span>
            </div>
            <div class="info-item">
              <span class="info-label">项目经理</span>
              <span class="info-value">{{ detail.data.Co_Manager || '暂无信息' }}</span>
            </div>
            <div class="info-item">
              <span class="info-label">联系电话</span>
              <span class="info-value phone-number">{{ detail.data.Co_Number || '暂无信息' }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="personnelCount z-index-100 back-white border-R6 absolute box-shadow">
      <div>
        <PersonnelCountEchart :ProjectsCount class="all" />
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed, ref, reactive, watch } from 'vue'
import { MenuFoldOutlined, RedoOutlined, MenuUnfoldOutlined, ContainerOutlined, SearchOutlined, CloseOutlined, DownloadOutlined } from '@ant-design/icons-vue'
import PersonnelCountEchart from './PersonnelCountEchart.vue'
import Echarts from '@/components/Echarts/index.vue'
import twiceProvideKeys from '@/assets/geojson/twiceProvideKeys.json'
import ExcelJS from 'exceljs'
import { useRouter } from 'vue-router'
import { message } from 'ant-design-vue'
import pumpHouseNodeKeys from '@/assets/geojson/pump_house.json'

const router = useRouter()
const fold = ref(false)
const isShow = ref(false)
const pieDataType = ref(null)
const times = ref([])
const personnelCountBoxHeight = ref(0)
const ProjectsCount = ref({ key: [], value: [] })
const LayersTable = { 片区: 'BelongingArea', 网格: 'Gridding', 街道: 'BelongingStreet' }

const cascaderValue = ref([])
const cascaderOptions = computed(() => {
  return [
    {
      value: 'state',
      label: '主要节点',
      children: [
        {
          value: '异常',
          label: '异常'
        },
        {
          value: '临供',
          label: '临供'
        },
        {
          value: '切换',
          label: '切换'
        },
        {
          value: '初验',
          label: '初验'
        }
      ]
    },
    {
      value: 'RemouldState',
      label: '改造状态',
      children: [
        {
          value: '未改造',
          label: '未改造'
        },
        {
          value: '正在改造',
          label: '正在改造'
        },
        {
          value: '已改造',
          label: '已改造'
        },
        {
          value: '已达标',
          label: '已达标'
        },
        {
          value: '未达标',
          label: '未达标'
        }
      ]
    },
    {
      value: 'OperationManagementState',
      label: '运营管理状态',
      children: [
        {
          value: '小区物业管理',
          label: '小区物业管理'
        },
        {
          value: '施工单位管理',
          label: '施工单位管理'
        },
        {
          value: '集团自管（移交水务科技）',
          label: '集团自管（移交水务科技）'
        },
        {
          value: '集团自管',
          label: '集团自管'
        }
      ]
    },
    {
      value: 'Batch',
      label: '批次',
      children: [
        {
          value: 1,
          label: '已纳改:利源代建'
        },
        {
          value: 2,
          label: '已纳改:查漏补缺'
        },
        {
          value: 3,
          label: '需纳改'
        },
        {
          value: 4,
          label: '无需纳改'
        },
        {
          value: 5,
          label: '已纳改:应改未改'
        }
      ]
    },
    {
      value: 'isCollect',
      label: '位置采集',
      children: [
        {
          value: '已采集',
          label: '已采集'
        },
        {
          value: '未采集',
          label: '未采集'
        }
      ]
    }
  ].filter((item) => item.value !== pumpHouseShowValue.value)
})

const selectValue = defineModel()

const props = defineProps({
  detail: { type: Object, default: () => ({}) },
  data: { type: Object, default: () => ({}) },
  getUpdateFeatures: Function,
  getUpdateLength: Function,
  getPumpHouseList: Function,
  pumpHouseDots: { type: Array, default: () => [] },
  Map: { type: Object, default: () => ({}) }
})
const batchKeys = { 1: '已纳改:利源代建', 2: '已纳改:查漏补缺', 3: '需纳改', 4: '无需纳改', 5: '已纳改:应改未改' }
const keys = computed(() => Object.keys(props.data[selectValue.value]))

const layers = { 片区: 'FTModificationWorks', 网格: 'tenGrid', 街道: 'FTstreet' }

watch(selectValue, (val, oldVal) => {
  props.Map.setLayoutProperty(`${layers[val]}_fill`, 'visibility', 'visible')
  props.Map.setLayoutProperty(`${layers[val]}_line`, 'visibility', 'visible')
  props.Map.setLayoutProperty(`${layers[val]}_symbol`, 'visibility', 'visible')
  props.Map.setLayoutProperty(`${layers[oldVal]}_fill`, 'visibility', 'none')
  props.Map.setLayoutProperty(`${layers[oldVal]}_line`, 'visibility', 'none')
  props.Map.setLayoutProperty(`${layers[oldVal]}_symbol`, 'visibility', 'none')
  handleChange()
})

watch(isShow, () => {
  if (isShow.value) {
    if (times.value.length > 0) {
      recoveryData()
    }
    handleChange()
  }
})

// 柱形图点击事件
function eventBarClick(e) {
  pumpHouseShowValue.value = 'state'
  props.Map.setLayoutProperty('dot_symbol', 'icon-image', ['get', 'state'])
  props.Map.setFilter('dot_symbol', ['all', ['==', ['get', 'state'], e.seriesName], ['==', ['get', LayersTable[selectValue.value]], e.name]])
}

// 饼图点击事件
let pieFilter = null
function eventPieClick(e) {
  const keys = { '已纳改:利源代建': 1, '已纳改:查漏补缺': 2, 需纳改: 3, 无需纳改: 4, '已纳改:应改未改': 5 }
  const name = pumpHouseShowValue.value == 'Batch' ? keys[e.name] : e.name
  if (pieDataType.value) {
    pieFilter = ['all', ['==', ['get', pumpHouseShowValue.value], name], ['==', ['get', LayersTable[selectValue.value]], pieDataType.value]]
  } else {
    pieFilter = ['all', ['==', ['get', pumpHouseShowValue.value], name]]
  }
  cascaderValue.value = []
  props.Map.setFilter('dot_symbol', pieFilter)
}
// 饼图下拉框改变
function handlepumpHouseShowValue() {
  const selectKey = selectValue.value == '片区' ? 'BelongingArea' : selectValue.value == '网格' ? 'Gridding' : 'BelongingStreet'
  if (pieDataType.value) {
    props.Map.setFilter('dot_symbol', ['all', ['==', ['get', selectKey], pieDataType.value]])
  } else {
    props.Map.setFilter('dot_symbol', null)
  }
  props.Map.setLayoutProperty('dot_symbol', 'icon-image', ['get', pumpHouseShowValue.value])
  cascaderValue.value = []
  pieFilter = null
}
// 点击区块划分切换
function handlerClickItem(value, index) {
  optionPie.value.title.text = value
  props.Map.setFilter('dot_symbol', ['all', ['==', ['get', LayersTable[selectValue.value]], value]])
  option.value.dataZoom = [{ rangeMode: ['value', 'value'], type: 'slider', startValue: index, endValue: index }]
  pieDataType.value = value
  ProjectsCount.value = props.getUpdateFeatures(value)
  pieFilter = null
}

//柱形图数据处理
const serverValue = computed(() => {
  const keys = ['异常', '临供前', '临供', '切换', '初验']
  const arr = []
  const data = props.data[selectValue.value]
  for (let i = 0; i < 5; i++) {
    const obj = { type: 'bar', data: [], showBackground: true, barWidth: '15%', name: keys[i], label: { show: true, position: 'top', color: 'black' } }
    for (const key in data) {
      obj.data.push(data[key][i])
    }
    arr.push(obj)
  }

  return arr
})

// 区块细分数据
const showData = computed(() => {
  const data = props.data[selectValue.value]
  const entries = Object.entries(data).map((item) => {
    item[1] = item[1].reduce((a, b) => a + b)
    return item
  })
  return entries
})

const option = ref({
  grid: { top: '10%', left: '3%', right: '4%', bottom: '14%', containLabel: true },
  color: ['#FF4D4F', '#b962fb', '#FFA54F', '#1677ff', '#51c31a'],
  tooltip: { trigger: 'axis' },
  legend: { itemWidth: 20, itemHeight: 20, right: 20 },
  dataZoom: [{ rangeMode: ['value', 'value'], type: 'slider', startValue: 0, endValue: 100 }],
  xAxis: {
    type: 'category',
    data: keys,
    axisTick: {
      // show: false
    },
    axisLabel: {
      // interval: 0,
      // rotate: 40,
      fontFamily: 'Microsoft YaHei',
      color: '#000', // x轴颜色
      fontWeight: 'normal',
      fontSize: 14,
      lineHeight: 17,
      interval: 0, //标签设置为全部显示
      margin: 10,

      formatter: function (params) {
        if (/片区/.test(params)) return params.replace(/片区/, '\n片区')
        if (/-/.test(params)) return params.replace(/-/, '\n-')
        if (/街道办/.test(params)) return params.replace(/街道办/, '\n街道办')
        return params
      }
    }
  },
  yAxis: { type: 'value' },
  series: serverValue ?? []
})

const optionPie = ref({
  title: { text: '总览' },
  tooltip: { trigger: 'item' },
  legend: { type: 'scroll', orient: 'vertical', right: 10, top: 100, bottom: 20, itemWidth: 20, itemHeight: 20 },
  // color: ['#FF4D4F', '#FFA54F', '#1677ff', '#51c31a'],
  // color: ['#FF4D4F', '#b962fb', '#FFA54F', '#1677ff', '#51c31a'],

  series: [
    {
      name: '福田泵房状态',
      type: 'pie',
      radius: ['0', '100%'],
      center: ['40%', '50%'],
      avoidLabelOverlap: false,
      padAngle: 5,
      label: {
        show: true,
        position: 'inner',
        formatter: '{b}\n{c}(个)\n{d}%',
        distance: 0.5, //标识距离中心点的距离
        align: 'center',
        baseline: 'middle',
        fontSize: 12,
        fontWeight: 'bolder'

        // color: '#fff'
      },
      itemStyle: {
        borderRadius: 10
      },

      labelLine: { show: false },
      data: []
    }
  ]
})

function getAll() {
  recoveryData()
  handleChange()
}

// 重置泵房图层与柱形图
function handleChange() {
  option.value.dataZoom = [{ type: 'slider', start: 0, end: 100 }] //设置初始显示比例
  pieDataType.value = null
  optionPie.value.title.text = '总览'
  props.Map.setFilter('dot_symbol', null)
  props.Map.flyTo({ center: [114.07528061331722, 22.54740197444606], zoom: 12.8, duration: 1000 })
  ProjectsCount.value = props.getUpdateFeatures()
  pieFilter = null
}

// 获取所有数据
function recoveryData() {
  props.getPumpHouseList({ all: true })
  times.value = null
}
// 时间选择筛选泵房
function handleTimeChange() {
  const startTime = times.value && times.value[0] ? new Date(times.value[0]) : null
  const endTime = times.value && times.value[1] ? new Date(new Date(times.value[1] + ' 23:59:59').getTime() + 1000 * 60 * 60 * 8) : null
  props.getPumpHouseList({ startTime, endTime, all: true })
  handleChange()
}

function mouseenterItem() {
  if (!times.value?.length) return
  personnelCountBoxHeight.value = '260px'
  ProjectsCount.value = props.getUpdateFeatures()
}

function mouseleaveItem() {
  if (!times.value?.length) return
  personnelCountBoxHeight.value = 0
}

// 饼图数据统计
const pumpHouseShowValue = ref('state')
const pumpHouseStatistics = computed(() => {
  let dataArr = props.pumpHouseDots
  if (pieDataType.value) {
    dataArr = props.pumpHouseDots.filter((item) => item.properties[LayersTable[selectValue.value]] === pieDataType.value)
  }
  let obj = {}
  if (pumpHouseShowValue.value === 'state') {
    obj = { 异常: 0, 临供前: 0, 临供: 0, 切换: 0, 初验: 0 }
  }

  for (let i = 0; i < dataArr.length; i++) {
    const item = dataArr[i].properties
    obj[item[pumpHouseShowValue.value]] = obj[item[pumpHouseShowValue.value]] ? obj[item[pumpHouseShowValue.value]] + 1 : 1
  }

  const colors = {
    异常: '#ee6666',
    临供前: '#b962fb',
    临供: '#fac858',
    切换: '#5470c6',
    初验: '#91cc75',
    已改造: '#5470c6',
    已达标: '#91cc75',
    未改造: '#fac858',
    未达标: '#ee6666',
    正在改造: '#73c0de',

    小区物业管理: '#73c0de',
    施工单位管理: '#fac858',
    '集团自管（移交水务科技）': '#5470c6',
    集团自管: '#91cc75',

    1: '#5470c6',
    2: '#91cc75',
    3: '#fac858',
    4: '#73c0de',
    5: '#ee6666',

    已采集: '#91cc75',
    未采集: '#ee6666'
  }

  const data = Object.entries(obj).map(([name, value]) => {
    return { name: pumpHouseShowValue.value === 'Batch' ? batchKeys[name] : name, value, itemStyle: { color: colors[name] } }
  })
  optionPie.value.series[0].data = data
  props.getUpdateLength(obj, props.pumpHouseDots.length, batchKeys, pumpHouseShowValue.value)
  return ''
})

// 导出文件
function handlerExport() {
  const hide = message.loading('正在导出文件...', 0)
  props.Map.flyTo({ center: [114.07528061331722, 22.54740197444606], zoom: 12.8, duration: 1000 })
  setTimeout(async () => {
    const features = props.Map.queryRenderedFeatures({ layers: ['dot_symbol'] })

    const header = Object.entries(twiceProvideKeys).map(([key, header]) => ({ header, key }))
    const workbook = new ExcelJS.Workbook()
    const sheet = workbook.addWorksheet('Sheet1')
    sheet.columns = header
    // 添加数据
    const keys = Object.values(pumpHouseNodeKeys)
    features.forEach((item) => {
      const data = JSON.parse(item.properties.Id)
      data.CurrentNode = keys[data.CurrentNode - 1]
      data.Batch = batchKeys[data.Batch]
      sheet.addRow(data)
    })

    // 文件下载
    const buffer = await workbook.xlsx.writeBuffer()
    const blob = new Blob([buffer], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' })
    const url = window.URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = `${new Date().getTime()}.xlsx`
    a.click()
    window.URL.revokeObjectURL(url)
    hide()
  }, 1100)
}

function handleCascaderChange(val) {
  const filter = [...pieFilter, ['==', ['get', val[0]], val[1]]]
  props.Map.setFilter('dot_symbol', filter)
}
</script>

<style lang="less" scoped>
.general-situation {
  width: 660px;
  height: 920px;
  background-color: #fff;
  top: 90px;
  right: 30px;
  transition: all 0.4s ease-in-out;
}
.fold {
  width: 45px !important;
  height: 45px !important;
  transition: all 0.4s ease-in-out;
}
.fold-icon {
  top: 0;
  right: 0;
}
.detail {
  bottom: 10px;
  right: 10px;
  left: 10px;
  height: 0;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  overflow: hidden;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);

  .detail-content {
    display: none;
    height: 100%;
    overflow-y: auto;
    padding: 0;
  }
}

.box-shadow-enhanced {
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12), 0 2px 8px rgba(0, 0, 0, 0.08);
}

.detail-header {
  padding: 20px 20px 0 20px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border-radius: 12px 12px 0 0;

  .area-name {
    font-size: 24px;
    font-weight: 700;
    text-align: center;
    margin-bottom: 16px;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  }

  .detail-divider {
    height: 3px;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.6), transparent);
    margin-bottom: 20px;
    border-radius: 2px;
  }
}

.detail-body {
  padding: 24px 20px 20px 20px;
  background: #fafbfc;
}

.info-section {
  margin-bottom: 24px;
  background: white;
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  border: 1px solid #e8eaed;
  transition: all 0.3s ease;

  &:hover {
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
    transform: translateY(-2px);
  }

  &:last-child {
    margin-bottom: 0;
  }
}

.section-title {
  display: flex;
  align-items: center;
  font-size: 16px;
  font-weight: 600;
  color: #2c3e50;
  margin-bottom: 16px;
  padding-bottom: 12px;
  border-bottom: 2px solid #e8eaed;

  .section-icon {
    margin-right: 8px;
    font-size: 18px;
  }
}

.info-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 0;
  border-bottom: 1px solid #f0f2f5;

  &:last-child {
    border-bottom: none;
    padding-bottom: 0;
  }

  .info-label {
    font-size: 14px;
    font-weight: 500;
    color: #5a6c7d;
    min-width: 80px;
    flex-shrink: 0;
  }

  .info-value {
    font-size: 14px;
    color: #2c3e50;
    font-weight: 400;
    text-align: right;
    flex: 1;
    margin-left: 16px;
    word-break: break-all;

    &.phone-number {
      color: #1890ff;
      font-weight: 500;
      font-family: 'Courier New', monospace;
    }
  }
}
.personnelCount {
  bottom: 10px;
  right: 10px;
  left: 10px;
  height: v-bind('personnelCountBoxHeight');
  transition: all 0.4s ease-in-out;
  overflow: hidden;
  & > div {
    height: 260px;
  }
}
.active {
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  height: 770px !important;

  .detail-content {
    display: block;
    animation: fadeInUp 0.5s ease-out;
  }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
.item {
  transition: all 0.6s ease-in-out;
  &:hover {
    box-shadow: inset 0px 0px 0px 2px #2f3e53;
    transition: all 0.6s ease-in-out;
  }
}

.seek {
  top: 60px;
  width: 0;
  height: 0;
  transition: all 0.4s ease-in-out;
}
.seek-active {
  width: 230px;
  height: 220px;
  transition: all 0.4s ease-in-out;
  padding: 10px;
}

.cascader_c {
  right: 10px;
  top: 50px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .detail {
    left: 5px;
    right: 5px;
    bottom: 5px;
  }

  .detail-header {
    padding: 16px 16px 0 16px;

    .area-name {
      font-size: 20px;
      margin-bottom: 12px;
    }
  }

  .detail-body {
    padding: 16px 12px 12px 12px;
  }

  .info-section {
    padding: 16px;
    margin-bottom: 16px;
  }

  .section-title {
    font-size: 14px;
    margin-bottom: 12px;
    padding-bottom: 8px;
  }

  .info-item {
    flex-direction: column;
    align-items: flex-start;
    padding: 8px 0;

    .info-label {
      margin-bottom: 4px;
      font-size: 13px;
    }

    .info-value {
      text-align: left;
      margin-left: 0;
      font-size: 13px;
    }
  }

  .active {
    height: 480px !important;
  }
}

/* 暗色主题支持 */
@media (prefers-color-scheme: dark) {
  .detail-body {
    background: #1a1a1a;
  }

  .info-section {
    background: #2d2d2d;
    border-color: #404040;

    &:hover {
      background: #333333;
    }
  }

  .section-title {
    color: #e0e0e0;
    border-bottom-color: #404040;
  }

  .info-item {
    border-bottom-color: #404040;

    .info-label {
      color: #b0b0b0;
    }

    .info-value {
      color: #e0e0e0;

      &.phone-number {
        color: #4da6ff;
      }
    }
  }
}
</style>
