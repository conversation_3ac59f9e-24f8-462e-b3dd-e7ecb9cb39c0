<template>
  <div class="flex border-R6 absolute" style="top: 10px; right: 50px">
    <div class="pad-12 back-white mar-R4 border-R4 relative box-shadow" v-if="visible">
      <a-tooltip title="关闭">
        <CloseOutlined style="top: 5px; right: 5px" class="close fon-S12 absolute pointer" @click="handlerVisible" />
      </a-tooltip>
      <slot></slot>
    </div>

    <div class="flex">
      <SearchOutlined class="icon fon-S26 color-666 pad-B4" @click="queryClick" />
      <ContainerOutlined class="icon fon-S26 mar-L12 color-666 pad-B4" @click="detail" />
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { useRouter } from 'vue-router'
import { SearchOutlined, CloseOutlined, ContainerOutlined } from '@ant-design/icons-vue'

const visible = defineModel('visible')
const emit = defineEmits(['close', 'change'])

function queryClick() {
  visible.value = !visible.value
  emit('change', visible.value)
}

function handlerVisible() {
  emit('close')
  visible.value = false
}

const router = useRouter()
function detail() {
  router.push('/MoreMaterials')
}
</script>

<style lang="less" scoped>
.item:has(+ .item) {
  border-bottom: 1px solid #999;
}

.icon:hover {
  transition: all 0.5s ease-in;
  color: #0680f3;
}
</style>
