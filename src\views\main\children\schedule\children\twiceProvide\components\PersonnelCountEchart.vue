<template>
  <div class="wrap">
    <Echarts :option />
  </div>
</template>

<script setup>
import { computed } from 'vue'
import Echarts from '@/components/Echarts/index.vue'
const props = defineProps({ ProjectsCount: Object })

const option = computed(() => ({
  title: { text: '泵房修改人统计', top: '4%', left: '2%' },
  tooltip: { trigger: 'axis', formatter: '{b} : {c} 个' },
  grid: [{ left: '8%', right: '5%', top: '25%', bottom: '18%' }],
  xAxis: [
    {
      type: 'category',

      data: props.ProjectsCount.key,
      axisTick: { alignWithLabel: true },
      axisLabel: {
        // interval: 0,
        // rotate: 40,
        fontFamily: 'Microsoft YaHei',
        color: '#000', // x轴颜色
        fontWeight: 'normal',
        fontSize: '14',
        lineHeight: 22,
        interval: 0, //标签设置为全部显示
        margin: 15,
        lineHeight: 15,
        // fontSize: 11,
        formatter: function (params) {
          const arr = params.slice(0, 6)
          const tail = params.slice(6, params.length)
          const str = arr + '\n' + tail
          return tail === '' ? arr : str
        }
      }
    }
  ],
  yAxis: [{ type: 'value', axisLabel: { formatter: '{value} 个' }, axisLine: { show: true, onZero: true, onZeroAxisIndex: null, lineStyle: { color: ' #6E7079', width: 1, type: 'solid' } } }],

  series: [
    {
      name: '项目数量',
      type: 'bar',
      label: { show: true, position: 'top', color: 'black' },
      label: {
        show: true,
        position: 'top',
        formatter: function (params) {
          const total = props.ProjectsCount.value.reduce((a, b) => a + b, 0)
          return params.value + '\n (' + ((params.value / total) * 100).toFixed(2) + '%)'
        }
      },
      showBackground: true,
      backgroundStyle: { color: 'rgba(180, 180, 180, 0.2)' },
      barWidth: '12%',
      data: props.ProjectsCount.value
    }
  ]
}))
</script>
