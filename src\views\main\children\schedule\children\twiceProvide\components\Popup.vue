<template>
  <div class="pump-house-popup">
    <!-- 头部区域 -->
    <div class="popup-header">
      <div class="header-icon">
        <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
          <path d="M12 2C13.1 2 14 2.9 14 4V6H16C17.1 6 18 6.9 18 8V19C18 20.1 17.1 21 16 21H8C6.9 21 6 20.1 6 19V8C6 6.9 6.9 6 8 6H10V4C10 2.9 10.9 2 12 2M12 4V6H12V4M8 8V19H16V8H8M10 10H14V12H10V10M10 14H14V16H10V14Z" />
        </svg>
      </div>
      <div class="header-title">{{ data.PumpHouseName || '泵房信息' }}</div>
    </div>

    <!-- 内容区域 -->
    <div class="popup-content">
      <div class="info-list">
        <div class="info-item">
          <div class="info-row">
            <span class="info-label">🔧 改造状态</span>
            <span class="info-value" :class="getStatusClass(data.RemouldState)">
              {{ data.RemouldState || '--' }}
            </span>
          </div>
        </div>

        <div class="info-item">
          <div class="info-row">
            <span class="info-label">⚙️ 运营管理</span>
            <span class="info-value" :class="getStatusClass(data.OperationManagementState)">
              {{ data.OperationManagementState || '--' }}
            </span>
          </div>
        </div>

        <div class="info-item">
          <div class="info-row">
            <span class="info-label">📋 批次信息</span>
            <span class="info-value batch-value" :class="getBatchClass(data.Batch)">
              {{ batchKeys[data.Batch] || '--' }}
            </span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
defineProps({ data: { type: Object, default: () => ({}) } })

const batchKeys = {
  1: '已纳改:利源代建',
  2: '已纳改:查漏补缺',
  3: '需纳改',
  4: '无需纳改',
  5: '已纳改:应改未改'
}

// 根据状态返回对应的样式类
const getStatusClass = (status) => {
  if (!status) return 'status-default'

  const statusText = status.toString().toLowerCase()
  if (statusText.includes('完成') || statusText.includes('正常') || statusText.includes('已')) {
    return 'status-success'
  } else if (statusText.includes('进行') || statusText.includes('处理')) {
    return 'status-warning'
  } else if (statusText.includes('异常') || statusText.includes('故障')) {
    return 'status-error'
  }
  return 'status-default'
}

// 根据批次返回对应的样式类
const getBatchClass = (batch) => {
  switch (batch) {
    case 1:
    case 2:
    case 5:
      return 'batch-completed'
    case 3:
      return 'batch-pending'
    case 4:
      return 'batch-none'
    default:
      return 'batch-default'
  }
}
</script>

<style lang="less" scoped>
.pump-house-popup {
  min-width: 220px;
  max-width: 280px;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
  transition: all 0.2s ease;

  &:hover {
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.12);
    transform: translateY(-1px);
  }

  .popup-header {
    padding: 10px 12px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: #fff;
    display: flex;
    align-items: center;
    gap: 8px;

    .header-icon {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 24px;
      height: 24px;
      background: rgba(255, 255, 255, 0.2);
      border-radius: 4px;
      backdrop-filter: blur(10px);

      svg {
        opacity: 0.9;
      }
    }

    .header-title {
      font-size: 14px;
      font-weight: 600;
      line-height: 1.3;
      flex: 1;
    }
  }

  .popup-content {
    padding: 12px;
    background: #fff;
  }

  .info-list {
    display: flex;
    flex-direction: column;
    gap: 6px;

    .info-item {
      .info-row {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 6px 8px;
        background: #f8f9fa;
        border-radius: 4px;
        border: 1px solid #e9ecef;
        transition: all 0.2s ease;

        &:hover {
          border-color: #667eea;
          background: #f0f4ff;
        }

        .info-label {
          font-size: 11px;
          font-weight: 500;
          color: #6c757d;
          flex-shrink: 0;
          margin-right: 8px;
        }

        .info-value {
          font-size: 11px;
          font-weight: 600;
          color: #2c3e50;
          padding: 2px 6px;
          border-radius: 3px;
          background: #fff;
          border: 1px solid transparent;
          transition: all 0.2s ease;
          text-align: right;
          min-width: 0;
          flex: 1;

          &.batch-value {
            font-size: 10px;
            text-align: center;
          }

          // 状态样式
          &.status-success {
            background: #d4edda;
            color: #155724;
            border-color: #c3e6cb;
          }

          &.status-warning {
            background: #fff3cd;
            color: #856404;
            border-color: #ffeaa7;
          }

          &.status-error {
            background: #f8d7da;
            color: #721c24;
            border-color: #f5c6cb;
          }

          &.status-default {
            background: #e9ecef;
            color: #495057;
            border-color: #dee2e6;
          }

          // 批次样式
          &.batch-completed {
            background: #d4edda;
            color: #155724;
            border-color: #c3e6cb;
          }

          &.batch-pending {
            background: #fff3cd;
            color: #856404;
            border-color: #ffeaa7;
          }

          &.batch-none {
            background: #e2e3e5;
            color: #383d41;
            border-color: #c6c8ca;
          }

          &.batch-default {
            background: #f8f9fa;
            color: #6c757d;
            border-color: #dee2e6;
          }
        }
      }
    }
  }
}

// 响应式设计
@media (max-width: 480px) {
  .pump-house-popup {
    min-width: 200px;
    max-width: 260px;

    .popup-header {
      padding: 8px 10px;

      .header-title {
        font-size: 13px;
      }

      .header-icon {
        width: 20px;
        height: 20px;
      }
    }

    .popup-content {
      padding: 10px;
    }

    .info-list {
      gap: 4px;

      .info-item {
        .info-row {
          padding: 4px 6px;

          .info-label {
            font-size: 10px;
          }

          .info-value {
            font-size: 10px;

            &.batch-value {
              font-size: 9px;
            }
          }
        }
      }
    }
  }
}
</style>
