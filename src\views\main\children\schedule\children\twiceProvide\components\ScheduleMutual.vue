<template>
  <a-modal :footer="null" :maskClosable="false" v-model:open="open" centered width="750px">
    <!-- 紧凑标题 -->
    <div class="compact-header">
      <h3>{{ data.PumpHouseName }}</h3>
      <div class="header-divider"></div>
    </div>

    <!-- 超紧凑信息表格 -->
    <div class="compact-table">
      <div class="table-row">
        <div class="cell">
          <span class="key">街道:</span>
          <span class="val">{{ data.BelongingStreet || '--' }}</span>
        </div>
        <div class="cell">
          <span class="key">网格:</span>
          <span class="val">{{ data.Gridding || '--' }}</span>
        </div>
        <div class="cell">
          <span class="key">片区:</span>
          <span class="val">{{ data.BelongingArea || '--' }}</span>
        </div>
      </div>

      <div class="table-row">
        <div class="cell">
          <span class="key">改造状态:</span>
          <span class="val status">{{ data.RemouldState || '--' }}</span>
        </div>
        <div class="cell">
          <span class="key">进展状态:</span>
          <div class="val status">{{ data.ProgressStatus || '--' }}</div>
        </div>
        <div class="cell">
          <span class="key">批次:</span>
          <span class="val">{{ batchKeys[data.Batch] || '--' }}</span>
        </div>
      </div>

      <div class="table-row">
        <div class="cell">
          <span class="key">停水数:</span>
          <span class="val">{{ data.TemporarySupplyEvents || '--' }}</span>
        </div>
        <div class="cell">
          <span class="key">泵房管理:</span>
          <span class="val">{{ data.PumpRoomControlledState || '--' }}</span>
        </div>
        <div class="cell">
          <span class="key">运营管理:</span>
          <span class="val">{{ data.OperationManagementState || '--' }}</span>
        </div>
      </div>

      <div class="table-row">
        <div class="cell">
          <span class="key">物业单位:</span>
          <span class="val">{{ data.PropertyUnit || '--' }}</span>
        </div>
        <div class="cell">
          <span class="key">联系人:</span>
          <span class="val">{{ data.ContactPerson || '--' }}</span>
        </div>
        <div class="cell">
          <span class="key">电话:</span>
          <span class="val">{{ data.PhoneNumber || '--' }}</span>
        </div>
      </div>

      <div class="table-row">
        <div class="cell">
          <span class="key">施工单位:</span>
          <span class="val">{{ data.ConstructionUnit || '--' }}</span>
        </div>
        <div class="cell">
          <span class="key">责任人:</span>
          <span class="val">{{ data.PersonInCharge || '--' }}</span>
        </div>
        <div class="cell">
          <span class="key">位置:</span>
          <span class="val">{{ data.AccuratePosition || '--' }}</span>
        </div>
      </div>

      <div class="table-row">
        <div class="cell">
          <span class="key">更新人:</span>
          <span class="val">{{ data.UpdatePerson || '--' }}</span>
        </div>
        <div class="cell">
          <span class="key">更新时间:</span>
          <span class="val">{{ data.UpdateTime || '--' }}</span>
        </div>
        <div class="cell">
          <span class="key">备注:</span>
          <span class="val">{{ data.Remark || '--' }}</span>
        </div>
      </div>
    </div>

    <!-- 紧凑图片 -->
    <div v-if="data?.PumpHouseImg?.length" class="compact-images">
      <span class="img-label">图片:</span>
      <div class="img-list">
        <template v-for="item in (data.PumpHouseImg ?? '').split(',')" :key="item">
          <a-image v-if="item" :width="40" :height="40" :src="item.replace('http:', 'https:')" :preview="{ mask: '' }" class="mini-img" />
        </template>
      </div>
    </div>

    <div class="wire">
      <div></div>
      <span>节点信息</span>
      <div></div>
    </div>
    <div class="relative">
      <DownCircleOutlined class="absolute pointer fon-S26" v-if="nowIndex <= data.CurrentNode - 2" @click="scrollToTop(true)" style="right: 58px; color: #1677ff; bottom: 4px" />
      <UpCircleOutlined class="absolute pointer fon-S26" v-if="nowIndex > 0" @click="scrollToTop(false)" style="right: 58px; color: #1677ff; top: 4px" />
      <div class="content_box" @scrollend="scrollEnd" ref="scrollRef">
        <template v-for="(item, index) in pumpHouseNodeValue.slice(0, data.CurrentNode)">
          <div class="item flex">
            <div class="f-1 flex pad-12">
              <div class="f-1">
                <div class="title fon-S18 fon-W600">{{ item.DictValue }} {{ pumpHouseNodeDetail?.CompletionTime?.slice(0, 10) }}</div>
                <div class="fon-W600" style="color: dodgerblue">节点备注：</div>
                <div>{{ pumpHouseNodeDetail?.Remark }}</div>
              </div>
              <div style="width: 55%" v-if="pumpHouseNodeDetail">
                <div class="fon-W600" style="color: dodgerblue">节点文件:</div>
                <div class="overflow-auto" style="height: 160px" v-if="pumpHouseNodeDetail?.Files?.length">
                  <template v-for="file in pumpHouseNodeDetail.Files" :key="file.Id">
                    <div style="padding: 6px; border-bottom: 1px solid #fff; color: #1677ff" class="flex f-between f-y-center">
                      <span style="width: 280px" class="text-nowrap">{{ file.FileKey }}</span>
                      <div>
                        <a-button type="primary" style="width: 26px; height: 26px" @click="downloadFile(file)">
                          <template #icon> <DownloadOutlined class="fon-S12" /> </template>
                        </a-button>
                      </div>
                    </div>
                  </template>
                </div>
                <a-empty v-else />
              </div>
              <div v-else class="f-xy-center fon-S16 fon-W600 color-333">节点未补充消息</div>
            </div>
            <div class="flex f-column pad-Y36 f-xy-center" style="width: 20%">
              <div class="f-1" style="width: 2px; background-color: rgb(0, 182, 55)"></div>
              <div class="num mar-12 fon-W600 border-Rall f-xy-center">{{ index + 1 }}</div>
              <div class="f-1" style="width: 2px; background-color: rgb(0, 182, 55)"></div>
            </div>
          </div>
        </template>
      </div>
    </div>

    <div class="flex mar-T12 f-row-reverse">
      <a-button type="primary" @click="openUpdateModalFn">更新泵房</a-button>
    </div>
  </a-modal>
</template>

<script setup>
import { ref, watch, nextTick } from 'vue'

import { DownloadOutlined, UpCircleOutlined, DownCircleOutlined } from '@ant-design/icons-vue'

import { PumpHouseApi } from '@/services/modules/pump.house'
import { CommonApi } from '@/services/modules/common'

const open = defineModel()
const props = defineProps({ data: Object })
const emit = defineEmits(['openUpdateModal'])
const scrollRef = ref(null)

function openUpdateModalFn() {
  emit('openUpdateModal')
  open.value = false
}

const pumpHouseNodeValue = ref([])
const batchKeys = ref([]) //泵房批次
async function queryDictionaries() {
  const { data: pumpHouseBatch } = await CommonApi.queryDictionaries('pumpHouseBatch')
  const { data: pumpHouseNode } = await CommonApi.queryDictionaries('pumpHouseNode')
  batchKeys.value = Object.fromEntries(pumpHouseBatch.map((item) => [item.DictCode, item.DictValue]))
  pumpHouseNodeValue.value = pumpHouseNode
}
queryDictionaries()

watch(
  () => props.data,
  (val) => {
    nextTick(() => {
      // pumpHouseNodeList(val.PumpRoomNumber)
      handlerNodeClick(val.CurrentNode)
      scrollRef.value.scrollTo(0, val.CurrentNode * 202)
      // fileList.value = {}
      // pumpHouseFileList()
    })
  }
)

// 下载文件
async function downloadFile(file) {
  const filePath = file.Path
  // const filePath = file.Path.replace('https://www.szwgft.cn/nodeServer', 'http://10.1.60.61:3001')

  try {
    const E = ['.png', '.jpeg', '.jpg', '.gif', '.pdf', '.xlsx', '.docx', '.doc', '.pptx', '.ppt', '.txt', 'webp', '.mp4', '.mp3']
    const isImage = E.find((i) => filePath.includes(i))
    if (isImage) {
      const response = await fetch(filePath)
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }
      const blob = await response.blob()
      const url = URL.createObjectURL(blob)
      const a = document.createElement('a')
      a.href = url
      a.download = file.FileKey
      document.body.appendChild(a)
      a.click()
      document.body.removeChild(a)
      URL.revokeObjectURL(url) // 清理 URL 对象
    } else {
      window.location.href = filePath
    }
  } catch (error) {
    // console.error('Error downloading image:', error)
    //  在此处处理错误，例如向用户显示错误消息。
  }
}

const nowIndex = ref(0)
function scrollEnd(e) {
  const children = Array.from(e.target.children)
  nowIndex.value = children.findIndex((i) => i.offsetTop === e.srcElement.scrollTop)
}

function scrollToTop(val) {
  nowIndex.value = val ? nowIndex.value + 1 : nowIndex.value - 1

  scrollRef.value.scrollTop = nowIndex.value * 202
}
watch(nowIndex, (val) => handlerNodeClick(val + 1))

const pumpHouseNodeDetail = ref(null)
const pumpHouseNodeFileKeyMap = ref(new Map())

async function handlerNodeClick(nodeNum) {
  if (!pumpHouseNodeFileKeyMap.value.has(nodeNum)) {
    const { data: FileKey } = await CommonApi.queryDictionaries(`pumpHouseNodeFile_${nodeNum}`)
    pumpHouseNodeFileKeyMap.value.set(nodeNum, FileKey)
  }
  const { data } = await PumpHouseApi.nodeDetail(props.data.PumpRoomNumber, { Node: nodeNum })
  if (data) {
    if (data.Files) {
      const keys = Object.fromEntries(pumpHouseNodeFileKeyMap.value.get(nodeNum).map(({ DictCode, DictValue }) => [DictCode, DictValue]))
      data.Files = JSON.parse(data.Files).map((item) => {
        item.FileKey = keys[item.FileType]
        return item
      })
    } else {
      data.Files = []
    }
  }
  pumpHouseNodeDetail.value = data ?? null
}
</script>

<style lang="less" scoped>
// 紧凑标题
.compact-header {
  text-align: center;
  margin-bottom: 12px;

  h3 {
    margin: 0 0 8px 0;
    font-size: 18px;
    font-weight: 600;
    color: #1677ff;
  }

  .header-divider {
    height: 2px;
    background: linear-gradient(90deg, transparent, #1677ff, transparent);
    border-radius: 1px;
  }
}

// 超紧凑表格
.compact-table {
  border: 1px solid #e8e8e8;
  border-radius: 6px;
  overflow: hidden;
  margin-bottom: 12px;

  .table-row {
    display: flex;
    border-bottom: 1px solid #f0f0f0;

    &:last-child {
      border-bottom: none;
    }

    &:nth-child(even) {
      background: #fafafa;
    }

    .cell {
      flex: 1;
      padding: 6px 8px;
      border-right: 1px solid #f0f0f0;
      display: flex;
      align-items: center;
      min-height: 32px;

      &:last-child {
        border-right: none;
      }

      .key {
        font-size: 12px;
        color: #666;
        font-weight: 500;
        margin-right: 4px;
        white-space: nowrap;
        min-width: fit-content;
      }

      .val {
        font-size: 12px;
        color: #333;
        font-weight: 600;
        word-break: break-all;

        &.status {
          color: #1677ff;
          background: rgba(22, 119, 255, 0.1);
          padding: 1px 6px;
          border-radius: 10px;
          font-size: 11px;
          display: inline-block;
        }
      }
    }
  }
}

// 紧凑图片
.compact-images {
  display: flex;
  align-items: center;
  padding: 8px;
  background: #fafafa;
  border-radius: 6px;
  margin-bottom: 12px;

  .img-label {
    font-size: 12px;
    color: #666;
    font-weight: 500;
    margin-right: 8px;
    white-space: nowrap;
  }

  .img-list {
    display: flex;
    gap: 6px;
    flex-wrap: wrap;

    .mini-img {
      border-radius: 4px;
      border: 1px solid #e8e8e8;

      &:hover {
        border-color: #1677ff;
      }
    }
  }
}

// 保留原有的wire样式用于节点信息
.wire {
  display: flex;
  justify-content: center;
  align-items: center;
  & > span {
    margin: 0 10px;
    font-size: 16px;
    font-weight: 600;
  }
  & > div {
    flex: 1;
    background-color: #666;
    height: 1px;
  }
}

:deep(.slick-track) {
  width: 100% !important;
  height: 100% !important;
  transform: none !important;
}

:deep(.slick-slide) {
  text-align: center;
  overflow: hidden;
}

:deep(.slick-arrow.custom-slick-arrow) {
  width: 25px;
  height: 25px;
  font-size: 25px;
  color: #000;
  z-index: 1;
}
:deep(.slick-arrow.custom-slick-arrow:before) {
  display: none;
}

.content_box {
  width: 100%;
  height: 200px;
  overflow-y: scroll;
  scroll-snap-type: y mandatory;
  transition: scrollTop 0.5s ease;
  .item {
    height: 100%;
    background: #eee;
    margin-bottom: 2px;
    border-radius: 8px;
    scroll-snap-align: center;
    scroll-snap-stop: always;
    .title {
      color: #1677ff;
    }
    .num {
      color: rgb(0, 182, 55);
      border: 2px solid rgb(0, 182, 55);
      width: 26px;
      height: 26px;
    }
  }
}
</style>
