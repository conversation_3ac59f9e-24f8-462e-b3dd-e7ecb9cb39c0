<template>
  <div style="width: 230px" class="fon-S14 color-333 fon-W600 mar-B8">模糊查询泵站</div>
  <a-input-search style="width: 210px" v-model:value="text" placeholder="输入泵站名称" nter-button @change="throttleSearch" @search="onSearch">
    <template #suffix>
      <a-tooltip title="清除搜索"> <CloseOutlined v-show="text" @click="reset" /> </a-tooltip>
    </template>
  </a-input-search>
  <!-- 下拉列表 -->
  <div class="border-eee overflow-auto mar-T8 border-R4" style="max-height: 140px" v-if="list.length">
    <template v-for="item in list" :key="item.Zone_Code">
      <div class="item border-B-eee pointer f-y-center f-between fon-S12" @click="handlerClick(item)">
        <div class="text-nowrap">{{ item.PumpHouseName }}</div>
      </div>
    </template>
  </div>

  <a-empty class="mar-T6" v-else />
</template>

<script setup>
import { ref } from 'vue'
import { CloseOutlined } from '@ant-design/icons-vue'

import { getPumpHouseNameLikeApi } from '@/services/modules/map'
import { PumpHouseApi } from '@/services/modules/pump.house'
import { message } from 'ant-design-vue'
import throttle from 'lodash/throttle'
const list = ref([])
const emit = defineEmits(['change'])

const text = ref('')
async function onSearch() {
  try {
    if (!text.value) {
      list.value = []
      return message.warning('请输入泵站名称')
    }
    const { data } = await PumpHouseApi.seek({ pumpHouseName: text.value })
    list.value = data
  } catch (error) {
    message.warning('搜索发生错误请联系管理员')
  }
}

function handlerClick(item) {
  emit('change', item)

  reset()
}

function reset() {
  text.value = ''
  list.value = []
}

const throttleSearch = throttle(onSearch, 500)
</script>

<style lang="less" scoped>
.item {
  padding: 10px 16px;
  transition: all 0.5s;
  &:hover {
    padding-left: 20px;
    background: #eeeeee;
  }
}
</style>
