<template>
  <a-modal :maskClosable="false" :closable="!opMap" okText="修改" v-model:open="updeteModal" :footer="null" centered width="860px">
    <div class="relative">
      <div class="fon-S18 fon-W600 text-center mar-B14">{{ formData.pumpHouseName }}</div>
      <div class="update-detail">
        <a-form ref="formRef" :model="formData" :label-col="{ span: 8 }">
          <div class="flex">
            <a-form-item class="f-1" name="pumpHouseName" label="泵房名称" required> <a-input v-model:value="formData.pumpHouseName" /> </a-form-item>
            <a-form-item class="f-1" name="belongingArea" label="所属片区" required>
              <a-select v-model:value="formData.belongingArea" placeholder="选择所属片区">
                <a-select-option v-for="item in selectData.PQ" :value="item.value">{{ item.label }}</a-select-option>
              </a-select>
            </a-form-item>
            <a-form-item class="f-1" name="gridding" label="网格划分" required>
              <a-select v-model:value="formData.gridding" placeholder="选择所属网格">
                <a-select-option v-for="item in selectData.WG" :value="item.value">{{ item.label }}</a-select-option>
              </a-select>
            </a-form-item>
          </div>
          <div class="flex">
            <a-form-item class="f-1" name="type" label="泵房类型" required>
              <a-select v-model:value="formData.type" placeholder="选择泵房类型">
                <a-select-option v-for="item in selectData.LX" :value="item.value">{{ item.label }}</a-select-option>
              </a-select>
            </a-form-item>
            <a-form-item class="f-1" name="residentialAddress" label="小区地址"> <a-input v-model:value="formData.residentialAddress" /> </a-form-item>
            <a-form-item class="f-1" name="classify" label="分类"> <a-input v-model:value="formData.classify" /> </a-form-item>
          </div>
          <div class="flex">
            <a-form-item class="f-1" label="加压户数" name="pressurizedHouseholds"> <a-input type="number" v-model:value="formData.pressurizedHouseholds" /></a-form-item>
            <a-form-item class="f-1" label="建设时间" name="constructionTime"> <a-input v-model:value="formData.constructionTime" /></a-form-item>
            <a-form-item class="f-1" label="物业单位" name="propertyUnit"> <a-input v-model:value="formData.propertyUnit" /></a-form-item>
          </div>
          <div class="flex">
            <a-form-item class="f-1" label="联系人" name="contactPerson"> <a-input v-model:value="formData.contactPerson" /></a-form-item>
            <a-form-item class="f-1" label="电话" name="phoneNumber"> <a-input v-model:value="formData.phoneNumber" /></a-form-item>
            <a-form-item class="f-1" label="泵房管理" name="pumpRoomControlledState"> <a-input v-model:value="formData.pumpRoomControlledState" /></a-form-item>
          </div>
          <div class="flex">
            <a-form-item class="f-1" name="progressStatus" label="项目进展" required>
              <a-select v-model:value="formData.progressStatus" placeholder="选择项目进展状态">
                <a-select-option v-for="item in selectData.ZT" :value="item.value">{{ item.label }}</a-select-option>
              </a-select>
            </a-form-item>
            <a-form-item class="f-1" label="运营状态" name="operationManagementState"><a-input v-model:value="formData.operationManagementState" placeholder="请输入管理状态" /></a-form-item>
            <a-form-item class="f-1" label="初验时间" name="acceptanceTime"> <a-date-picker v-model:value="formData.acceptanceTime" value-format="YYYY-MM-DD" /></a-form-item>
          </div>
          <div class="flex">
            <a-form-item class="f-1" label="临供停水数" name="temporarySupplyEvents"><a-input type="number" v-model:value="formData.temporarySupplyEvents" placeholder="请输入停水数" /></a-form-item>
            <a-form-item class="f-1" label="施工单位" name="constructionUnit"><a-input v-model:value="formData.constructionUnit" placeholder="请输入施工单位" /></a-form-item>
            <a-form-item class="f-1" label="现场责任人" name="personInCharge"><a-input v-model:value="formData.personInCharge" placeholder="请输入责任人" /></a-form-item>
          </div>
          <div class="flex">
            <a-form-item class="f-1" label="泵房编号" name="pumpRoom_ID"><a-input v-model:value="formData.pumpRoom_ID" placeholder="请输入施工单位" /></a-form-item>
            <a-form-item class="f-1" label="GIS编号" name="systeM_ID"><a-input v-model:value="formData.systeM_ID" placeholder="请输入责任人" /></a-form-item>
            <a-form-item class="f-1" label="备注" name="remark"><a-textarea v-model:value="formData.remark" placeholder="请输入备注" /></a-form-item>
          </div>
          <div class="flex">
            <a-form-item class="f-1" name="belongingStreet" label="所属街道" required>
              <a-select v-model:value="formData.belongingStreet" placeholder="所属街道">
                <a-select-option v-for="item in selectData.JD" :value="item.value">{{ item.label }}</a-select-option>
              </a-select>
            </a-form-item>
            <a-form-item :rules="[{ required: true, validator: validatexy, trigger: 'change' }]" class="f-1" label="X坐标" name="x"><a-input v-model:value="formData.x" placeholder="请输入施工单位" /></a-form-item>
            <a-form-item :rules="[{ required: true, validator: validatexy, trigger: 'change' }]" class="f-1" label="Y坐标" name="y"><a-input v-model:value="formData.y" placeholder="请输入施工单位" /></a-form-item>
            <a-button type="primary" @click="modifyPosition">修改坐标</a-button>
          </div>
          <div class="flex">
            <div class="f-3"></div>
            <a-form-item :rules="[{ required: true, validator: validatexy, trigger: 'change' }]" class="f-6" label="泵房精确位置" name="accuratePosition">
              <a-textarea v-model:value="formData.accuratePosition" placeholder="泵房精确位置填写规则 如：xxx小区xxx栋地下二层" />
            </a-form-item>
          </div>

          <div style="width: 100%; height: 180px" class="relative">
            <template v-for="([key, value], index) in Object.entries(keys)" :key="key">
              <a-popover :title="value.key" trigger="click">
                <template #content>
                  <div class="f-xy-center" style="width: 380px">
                    <a-radio-group v-model:value="formData[key]" button-style="solid">
                      <a-radio-button value="是">已完成</a-radio-button>
                      <a-radio-button value="否">未完成</a-radio-button>
                    </a-radio-group>
                  </div>
                  <div class="mar-T10">时间：<a-date-picker v-model:value="formData[`${key}_time`]" value-format="YYYY-MM-DD" /></div>
                  <div>备注:<a-textarea v-model:value="formData[`${key}_remark`]" placeholder="请输入备注" /></div>
                  <div>需上传文件：</div>
                  <template v-for="text in fileNames[index]" :key="text">
                    <div class="fon-S12 color-666">{{ text }}</div>
                  </template>
                  <div class="file">
                    <div>当前节点已上传文件：</div>
                    <div class="border-eee mar-B12 overflow-auto" style="max-height: 150px">
                      <template v-for="item in fileList[index + 1]" :key="item.file">
                        <div style="padding: 8px 10px" class="flex f-between f-y-center border-B-eee">
                          <span style="width: 280px" class="text-nowrap">{{ item.file }}</span>
                          <span>{{ item.size }} M</span>
                          <div></div>
                        </div>
                      </template>
                    </div>
                    <a-upload v-model:file-list="files" :action="`https://www.szwgft.cn/nodeServer/upload/pump_house/${formData.pumpRoomNumber}?step=${index + 1}`">
                      上传当前节点文件：<a-button> <upload-outlined />上传文件 </a-button>
                    </a-upload>
                  </div>
                </template>
                <a-button :style="value.seat" class="absolute z-index-10" :type="index < formData.current ? 'primary' : 'dashed'" @click="files = []">{{ value.key }}</a-button>
              </a-popover>
            </template>

            <div class="absolute" style="width: 90%; height: 3px; background-color: #666; left: 30px; top: 26px"></div>
            <div class="absolute" style="width: 90%; height: 3px; background-color: #666; left: 20px; top: 86px"></div>
            <div class="absolute" style="width: 70%; height: 3px; background-color: #666; left: 30px; top: 145px"></div>
            <div class="absolute" style="width: 3px; height: 60px; background-color: #666; left: 70px; top: 85px"></div>
            <div class="absolute" style="width: 3px; height: 60px; background-color: #666; right: 70px; top: 25px"></div>
          </div>
        </a-form>
        <div>上传泵房图片：</div>
        <div v-if="updeteModal"><UploadImage ref="UploadImageRef" :url @update="updateImage" /></div>
      </div>
      <div class="flex f-row-reverse">
        <a-popconfirm title="确认修改吗？" @confirm="update">
          <template #icon><question-circle-outlined style="color: red" /></template>
          <a-button type="primary">修改</a-button>
        </a-popconfirm>
        <a-button class="mar-R12" @click="updeteModal = false">取消</a-button>
      </div>
      <!-- 修改位置 -->
      <div class="mapbox" v-if="opMap">
        <MapGlS :options @load="mapLoad">
          <template #rightTop> <MapInputSearch class="mar-12" @handlerZoneClick="handlerClick" /> </template>
          <template #rightBottom> <a-button class="mar-24" @click="opMap = false" type="primary">取消</a-button> </template>
        </MapGlS>
      </div>
    </div>
  </a-modal>
</template>

<script setup>
import { ref, watch, computed } from 'vue'
import { getSecondaryWaterProgressUpdate, getPumpHouseFileList } from '@/services/modules/map'
import MapGlS from '@/components/MapGlS2/index.vue'
import UploadImage from '@/components/UploadImage/index.vue'
import { UploadOutlined, DownloadOutlined, FolderViewOutlined } from '@ant-design/icons-vue'
import debounce from 'lodash/debounce'

import Cache from '@/utils/cache'
import { message } from 'ant-design-vue'
import options from '@/ownedByAllMapConfig'
import mapboxgl from 'mapbox-gl'

import { fileNames } from '@/assets/geojson/twiceProvideFileKeys.json'

const updeteModal = defineModel()
const props = defineProps({ data: Object })
const emit = defineEmits(['loadAll'])
const formRef = ref(null)
const formData = computed(() => props.data.data)
const url = computed(() => `https://www.szwgft.cn/nodeServer/upload/pump_house/${formData.value.pumpRoomNumber}?step=img`)
const opMap = ref(false)
let Marker = null
let images = []
const UploadImageRef = ref(null)
const files = ref([])

const keys = {
  schemeReview: { key: '1方案审查', seat: 'top: 10px; left: 28px' },
  drawingReview: { key: '2施工图审查', seat: 'top: 10px; left: 150px' },
  constructionEntry: { key: '3施工进场', seat: 'top: 10px; left: 290px' },
  temporaryWaterSupply: { key: '4临时供水', seat: 'top: 10px; left: 420px' },
  equipmentDemolition: { key: '5拆除旧设备', seat: 'top: 10px; left: 546px' },
  equipmentInstallation: { key: '6设备进场验收', seat: 'top: 10px; left: 680px' },
  poolRenovation: { key: '7水池改造', seat: 'top: 70px; left: 696px' },
  cleaningDisinfection: { key: '8清洗消毒', seat: 'top: 70px; left: 570px' },
  equipmentDebug: { key: '9设备调试', seat: 'top: 70px; left: 446px' },
  pumpHouseRenovation: { key: '10泵房装修', seat: 'top: 70px; left: 315px' },
  waterInspection: { key: '11通水前检查', seat: 'top: 70px; left: 170px' },
  systemSwitching: { key: '12切换新系统', seat: 'top: 70px; left: 20px' },
  videoSecurity: { key: '13视频安防', seat: 'top: 130px; left: 25px' },
  preliminaryAcceptance: { key: '14竣工验收', seat: 'top: 130px; left: 160px' },
  completionAcceptance: { key: '15二供平台', seat: 'top: 130px; left: 295px' },
  retestDate: { key: '16复验日期', seat: 'top: 130px; left: 430px' },
  completionHandover: { key: '17完成移交', seat: 'top: 130px; left: 565px' }
}

const selectData = {
  ZT: [
    { value: '正常', label: '正常' },
    { value: '滞后', label: '滞后' },
    { value: '停工', label: '停工' }
  ],
  PQ: [
    { value: '梅林片区', label: '梅林片区' },
    { value: '香蜜片区', label: '香蜜片区' },
    { value: '景田片区', label: '景田片区' },
    { value: '莲花片区', label: '莲花片区' },
    { value: '新洲片区', label: '新洲片区' },
    { value: '福保片区', label: '福保片区' },
    { value: '福民片区', label: '福民片区' },
    { value: '中心城片区', label: '中心城片区' },
    { value: '福东北片区', label: '福东北片区' },
    { value: '福东南片区', label: '福东南片区' }
  ],
  WG: [
    { value: '梅林-1', label: '梅林-1' },
    { value: '梅林-2', label: '梅林-2' },
    { value: '香蜜-1', label: '香蜜-1' },
    { value: '香蜜-2', label: '香蜜-2' },
    { value: '香蜜-3', label: '香蜜-3' },
    { value: '福中-1', label: '福中-1' },
    { value: '福中-2', label: '福中-2' },
    { value: '福中-3', label: '福中-3' },
    { value: '福东-1', label: '福东-1' },
    { value: '福东-2', label: '福东-2' }
  ],
  JD: [
    { value: '梅林街道办', label: '梅林街道办' },
    { value: '莲花街道办', label: '莲花街道办' },
    { value: '香蜜湖街道办', label: '香蜜湖街道办' },
    { value: '沙头街道办', label: '沙头街道办' },
    { value: '福田街道办', label: '福田街道办' },
    { value: '福保街道办', label: '福保街道办' },
    { value: '华富街道办', label: '华富街道办' },
    { value: '华强北街道办', label: '华强北街道办' },
    { value: '园岭街道办', label: '园岭街道办' },
    { value: '南园街道办', label: '南园街道办' }
  ],
  LX: [
    { value: '1', label: '自管泵房' },
    { value: '2', label: '二供改造泵房（一阶段）' },
    { value: '3', label: '二供改造泵房（二阶段）' },
    { value: '4', label: '二供改造泵房（三阶段）' },
    { value: '5', label: '二供兜底泵房' }
  ]
}

function update() {
  formRef.value.validate().then(async () => {
    formData.value.current = countCurrent()
    formData.value.updatePerson = Cache.get('userInfo').name
    formData.value.updateTime = new Date(new Date().getTime() + 8 * 60 * 60 * 1000)
    // formData.value.pic = formData.value?.pic?.length ? formData.value.pic + ',' + images.join(',') : images.join(',')
    if (formData.value?.pic?.length) {
      if (images.length) {
        formData.value.pic = formData.value.pic + ',' + images.join(',')
      }
    } else {
      if (images.length) {
        formData.value.pic = images.join(',')
      }
    }
    const { code } = await getSecondaryWaterProgressUpdate(formData.value)
    if (code === 200) {
      emit('loadAll')
      UploadImageRef.value.reset()
    } else {
      message.error('修改失败')
    }
  })
}

async function validatexy({ field }) {
  if (formData.value[field] === '' || formData.value[field] === null) {
    return Promise.reject('不可为空')
  } else {
    return Promise.resolve()
  }
}

function countCurrent() {
  let current = 0
  Object.keys(keys).forEach((key, index) => {
    if (formData.value[key] == '是') current = index + 1
  })
  return current
}
let Map
function mapLoad(map) {
  Map = map
  const { x, y } = formData.value
  map.setCenter([x, y])
  map.setZoom(17)
  Marker = new mapboxgl.Marker().setLngLat([x, y]).addTo(map)
  Marker.getElement().addEventListener('click', handlerMarkerClick)
  map.on('move', handlerMoveEvent)
}

// 修改位置
function modifyPosition() {
  opMap.value = true
}

function handlerMarkerClick() {
  const { lng, lat } = Marker.getLngLat()
  formData.value.x = lng.toString()
  formData.value.y = lat.toString()

  opMap.value = false
  Map.off('move', handlerMoveEvent)
  Marker.getElement().removeEventListener('click', handlerMarkerClick)
  Marker.remove()
  Map = null
}
function handlerMoveEvent() {
  let { lng, lat } = Map.getCenter() // 获取新的中心点坐标
  Marker.setLngLat([lng, lat]) // 跟新标记位置
}

function updateImage(url) {
  images = url.map((item) => item.https)
}

// 获取泵房文件列表
const fileList = ref({})

async function pumpHouseFileList() {
  try {
  } catch (error) {}
  const { code, data } = await getPumpHouseFileList(props.data.data.pumpRoomNumber)
  if (code == 200) {
    fileList.value = data
  } else {
    fileList.value = {}
  }
}
watch(
  () => props.data,
  () => pumpHouseFileList()
)

const watchCallbackDebounce = debounce(watchCallback, 500)
watch(files, watchCallbackDebounce, { deep: true })

function watchCallback(val, newVal) {
  if (!val.length) return
  pumpHouseFileList()
}

function handlerClick(e) {
  const center = e.Center_Point.split(',')
  Map.setCenter(center)
}
</script>

<style lang="less" scoped>
.update-detail {
  height: 700px;
  overflow: auto;
  :deep(:where(.css-dev-only-do-not-override-mb15du).ant-form-item) {
    margin-bottom: 14px;
  }
}
.mapbox {
  height: 774px;
  width: 100%;
  position: absolute;
  background-color: #fff;
  top: 0;
  z-index: 999;
}
.file {
  margin-top: 12px;
  // :deep(.ant-upload-list) {
  //   display: none;
  // }
}
</style>
