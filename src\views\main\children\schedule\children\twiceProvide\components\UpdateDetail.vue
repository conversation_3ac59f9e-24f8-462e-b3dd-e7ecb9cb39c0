<template>
  <a-modal :maskClosable="false" :closable="!opMap" okText="修改" v-model:open="updeteModal" :footer="null" centered width="860px">
    <div class="relative">
      <div class="fon-S18 fon-W600 text-center mar-B14">{{ formData.PumpHouseName }}</div>

      <div class="update-detail">
        <a-form layout="vertical" ref="formRef" :model="formData">
          <div class="wire">
            <div></div>
            <span>基础信息</span>
            <div></div>
          </div>

          <div class="flex">
            <a-form-item class="f-1 pad-X10" name="PumpHouseName" label="泵房名称" required> <a-input v-model:value="formData.PumpHouseName" /> </a-form-item>
            <a-form-item class="f-1 pad-X10" name="Batch" label="泵房批次" required>
              <a-select v-model:value="formData.Batch" placeholder="选择泵房批次" v-if="selectOption[1].length">
                <a-select-option v-for="item in selectOption[1]" :value="item.value">{{ item.label }}</a-select-option>
              </a-select>
            </a-form-item>
            <a-form-item class="f-1 pad-X10" name="RemouldState" label="改造状态">
              <a-select v-model:value="formData.RemouldState" placeholder="选择项改造状态">
                <a-select-option v-for="item in selectOption[3]" :value="item.value">{{ item.label }}</a-select-option>
              </a-select>
            </a-form-item>
            <a-form-item class="f-1 pad-X10" label="加压户数" name="PressurizedHouseholds"> <a-input type="number" v-model:value="formData.PressurizedHouseholds" placeholder="请输入加压户数" /></a-form-item>
          </div>
          <div class="flex">
            <a-form-item class="f-1 pad-X10" label="建设时间" name="ConstructionTime"> <a-input v-model:value="formData.ConstructionTime" /></a-form-item>
            <a-form-item class="f-1 pad-X10" name="ProgressStatus" label="项目进展状态" required>
              <a-select v-model:value="formData.ProgressStatus" placeholder="选择项目进展状态">
                <a-select-option v-for="item in selectOption[0]" :value="item.value">{{ item.label }}</a-select-option>
              </a-select>
            </a-form-item>
            <a-form-item class="f-1 pad-X10" label="临供停水数" name="TemporarySupplyEvents"><a-input type="number" v-model:value="formData.TemporarySupplyEvents" placeholder="请输入停水数" /></a-form-item>
            <a-form-item class="f-1 pad-X10" label="初验时间" name="AcceptanceTime"> <a-date-picker v-model:value="formData.AcceptanceTime" value-format="YYYY-MM-DD" /></a-form-item>
          </div>

          <div class="wire">
            <div></div>
            <span>管理信息</span>
            <div></div>
          </div>
          <div class="flex">
            <a-form-item class="f-1 pad-X10" label="物业单位" name="PropertyUnit"> <a-input v-model:value="formData.PropertyUnit" placeholder="请输入物业单位" /></a-form-item>
            <a-form-item class="f-1 pad-X10" label="物业联系人" name="ContactPerson"> <a-input v-model:value="formData.ContactPerson" placeholder="请输入物业联系人" /></a-form-item>
            <a-form-item class="f-1 pad-X10" label="物业电话" name="PhoneNumber"> <a-input v-model:value="formData.PhoneNumber" placeholder="请输入物业电话" /></a-form-item>
            <a-form-item class="f-1 pad-X10" label="泵房管理状态" name="PumpRoomControlledState"> <a-input v-model:value="formData.PumpRoomControlledState" placeholder="请输入泵房管理状态" /></a-form-item>
          </div>
          <div class="flex">
            <a-form-item class="f-1 pad-X10" label="运营管理状态" name="OperationManagementState">
              <a-select v-model:value="formData.OperationManagementState" placeholder="选择管理状态">
                <a-select-option v-for="item in selectOption[2]" :value="item.value">{{ item.label }}</a-select-option>
              </a-select>
            </a-form-item>
            <a-form-item class="f-1 pad-X10" label="施工单位" name="ConstructionUnit"><a-input v-model:value="formData.ConstructionUnit" placeholder="请输入施工单位" /></a-form-item>
            <a-form-item class="f-1 pad-X10" label="现场责任人" name="PersonInCharge"><a-input v-model:value="formData.PersonInCharge" placeholder="请输入责任人" /></a-form-item>
            <a-form-item class="f-1 pad-X10" label="备注信息" name="remark"><a-input v-model:value="formData.remark" placeholder="请输入备注" /></a-form-item>
          </div>
          <div class="wire">
            <div></div>
            <span>位置信息</span>
            <div></div>
          </div>

          <div class="flex f-between pad-X10">
            <div class="flex">
              <div class="mar-X10 flex f-1">
                <div class="fon-W600">网格：</div>
                <span>{{ formData.Gridding }}</span>
              </div>
              <div class="mar-X10 flex">
                <div class="fon-W600">街道：</div>
                <span>{{ formData.BelongingStreet }}</span>
              </div>
              <div class="mar-X10 flex">
                <div class="fon-W600">片区：</div>
                <span>{{ formData.BelongingArea }}</span>
              </div>
              <div class="mar-X10 flex">
                <div class="fon-W600">坐标：</div>
                <span>[{{ formData.X }},{{ formData.Y }}]</span>
              </div>
            </div>
            <a-button type="primary" @click="modifyPosition">修改坐标</a-button>
          </div>
          <div class="flex">
            <a-form-item class="f-2 pad-X10" name="residentialAddress" label="小区地址"> <a-textarea v-model:value="formData.ResidentialAddress" /> </a-form-item>
            <a-form-item :rules="[{ required: true, validator: validatexy, trigger: 'change' }]" class="f-2 pad-X10" label="泵房精确位置" name="accuratePosition">
              <a-textarea v-model:value="formData.AccuratePosition" placeholder="泵房精确位置填写规则 如：xxx小区xxx栋地下二层" />
            </a-form-item>
            <a-form-item :rules="[{ required: true, validator: validatexy, trigger: 'change' }]" class="f-3 pad-X10" label="泵房图片" name="accuratePosition">
              <div class="f-y-center">
                <template v-for="img in (formData.PumpHouseImg ?? '').split(',')" :key="img">
                  <div class="mar-L10">
                    <a-image v-if="img" :width="50" :height="50" :src="img" />
                  </div>
                </template>
                <a-upload
                  v-if="(formData.PumpHouseImg ?? '').split(',').length < 4"
                  v-model:file-list="files"
                  @change="handleUploadImgChange"
                  :headers
                  :action="`https://www.szwgft.cn/nodeServer/resource/upload/pump_house?url=${formData.PumpRoomNumber}/img`"
                >
                  <div class="f-xy-center mar-L10" style="width: 50px; height: 50px; background-color: #1677ff; border-radius: 4px"><upload-outlined style="color: #fff; font-size: 20px" /></div>
                </a-upload>
              </div>
            </a-form-item>
          </div>

          <div class="wire">
            <div></div>
            <span>节点信息{{ pumpHouseNodeKeys.length }}</span>
            <div></div>
          </div>

          <div style="width: 100%; height: 180px" class="relative">
            <template v-for="(item, index) in pumpHouseNodeKeys" :key="item.DictCode">
              <!-- <a-popover :title="item.DictValue" trigger="click">
                <template #content>
                  <div style="width: 380px; min-height: 160px">
                    <div v-if="pumpHouseNodeDetail">
                      <div class="f-xy-center" style="width: 380px">
                        <a-radio-group v-model:value="pumpHouseNodeDetail.IsEnd" button-style="solid">
                          <a-radio-button :value="true">已完成</a-radio-button>
                          <a-radio-button :value="false">未完成</a-radio-button>
                        </a-radio-group>
                        <div class="mar-L10"><a-date-picker v-model:value="pumpHouseNodeDetail.CompletionTime" value-format="YYYY-MM-DD" /></div>
                      </div>
                      <div class="pad-10"><a-textarea v-model:value="pumpHouseNodeDetail.Remark" placeholder="请输入备注" /></div>
                      <div>需上传文件：</div>
                      <div style="max-height: 100px; border-radius: 4px" class="overflow-auto border-666 pad-Y10">
                        <template v-for="text in pumpHouseNodeFileKeyMap.get(item.DictCode)" :key="text.Id">
                          <div class="flex f-between border-B-eee pad-X10" style="padding: 2px 10px">
                            <div class="fon-S12 color-666" :class="{ 'fon-W600 color-000': pumpHouseNodeDetail.Files.includes(text.DictCode) }">{{ text.DictValue }}</div>
                            <a-upload
                              v-if="!pumpHouseNodeDetail.Files.includes(text.DictCode)"
                              v-model:file-list="files"
                              @change="(e) => handleUploadChange(e, item.DictCode, text.DictCode)"
                              :headers
                              :action="`https://www.szwgft.cn/nodeServer/resource/upload/pump_house?url=${formData.PumpRoomNumber}/${item.DictCode}`"
                            >
                              <div class="f-xy-center" style="width: 22px; height: 20px; background-color: #1677ff; border-radius: 4px"><upload-outlined style="color: #fff" /></div>
                            </a-upload>
                          </div>
                        </template>
                      </div>
                      <div class="pad-T12 text-center">
                        <a-button type="primary" @click="updatePumpHouseNodeDetail(item.DictCode)">保存节点修改</a-button>
                      </div>
                    </div>
                    <div class="f-xy-center f-column" v-else>
                      <div class="mar-B30">无节点消息点击创建节点</div>
                      <PlusOutlined @click="addPumpHouseNode(item.DictCode)" style="font-size: 36px; color: #1677ff" />
                    </div>
                  </div>
                </template>
                <a-button :style="location[item.DictCode]" class="absolute z-index-10" :type="index < formData.CurrentNode ? 'primary' : 'dashed'" @click="handlerNodeClick(item.DictCode)">{{ item.DictValue }}</a-button>
              </a-popover> -->
              <a-button :style="location[item.DictCode]" class="absolute z-index-10" :type="index < formData.CurrentNode ? 'primary' : 'dashed'" @click="handlerNodeClick(item.DictCode)">{{ item.DictValue }}</a-button>
            </template>

            <div class="absolute" style="width: 90%; height: 3px; background-color: #666; left: 30px; top: 26px"></div>
            <div class="absolute" style="width: 90%; height: 3px; background-color: #666; left: 20px; top: 86px"></div>
            <div class="absolute" style="width: 70%; height: 3px; background-color: #666; left: 30px; top: 145px"></div>
            <div class="absolute" style="width: 3px; height: 60px; background-color: #666; left: 70px; top: 85px"></div>
            <div class="absolute" style="width: 3px; height: 60px; background-color: #666; right: 70px; top: 25px"></div>
          </div>
        </a-form>
        <!-- <div>上传泵房图片：</div>
        <div v-if="updeteModal"><UploadImage ref="UploadImageRef" :url @update="updateImage" /></div> -->
      </div>
      <div class="flex f-row-reverse">
        <a-popconfirm title="确认修改吗？" @confirm="update">
          <template #icon><question-circle-outlined style="color: red" /></template>
          <a-button type="primary">修改</a-button>
        </a-popconfirm>
        <a-button class="mar-R12" @click="updeteModal = false">取消</a-button>
      </div>
      <div class="mapbox" v-if="opMap">
        <MapGlS :options @load="mapLoad">
          <template #rightTop> <MapInputSearch class="mar-12" @handlerZoneClick="handlerClick" /> </template>
          <template #rightBottom>
            <a-button @click="opMap = false">取消</a-button>
            <a-button class="mar-24" @click="handlerMarkerClick" type="primary">确定</a-button>
          </template>
        </MapGlS>
      </div>
    </div>
  </a-modal>

  <a-modal :footer="null" v-model:open="childNodeOpen" :title="pumpHouseNodeKeys[pumpHouseNodeDetail?.Node - 1]?.DictValue" centered @ok="modal2Visible = false">
    <div style="width: 472px; min-height: 160px">
      <div v-if="pumpHouseNodeDetail">
        <div class="f-xy-center" style="width: 472px">
          <a-radio-group v-model:value="pumpHouseNodeDetail.IsEnd" button-style="solid">
            <a-radio-button :value="true">已完成</a-radio-button>
            <a-radio-button :value="false">未完成</a-radio-button>
          </a-radio-group>
          <div class="mar-L10"><a-date-picker v-model:value="pumpHouseNodeDetail.CompletionTime" value-format="YYYY-MM-DD" /></div>
        </div>
        <div class="pad-10"><a-textarea v-model:value="pumpHouseNodeDetail.Remark" placeholder="请输入备注" /></div>
        <div>需上传文件：</div>
        <div style="max-height: 100px; border-radius: 4px" class="overflow-auto border-666 pad-Y10">
          <template v-for="text in pumpHouseNodeFileKeyMap.get(pumpHouseNodeDetail.Node + '')" :key="text.Id">
            <div class="flex f-between border-B-eee pad-X10" style="padding: 2px 10px">
              <div class="fon-S12 color-666" :class="{ 'fon-W600 color-000': pumpHouseNodeDetail.Files.includes(text.DictCode) }">{{ text.DictValue }}</div>
              <a-upload
                v-if="!pumpHouseNodeDetail.Files.includes(text.DictCode)"
                v-model:file-list="files"
                @change="(e) => handleUploadChange(e, pumpHouseNodeDetail.Node, text.DictCode)"
                :headers
                :action="`https://www.szwgft.cn/nodeServer/resource/upload/pump_house?url=${formData.PumpRoomNumber}/${pumpHouseNodeDetail.Node}`"
              >
                <div class="f-xy-center" style="width: 22px; height: 20px; background-color: #1677ff; border-radius: 4px"><upload-outlined style="color: #fff" /></div>
              </a-upload>
            </div>
          </template>
        </div>
        <div class="pad-T12 text-center">
          <a-button type="primary" @click="updatePumpHouseNodeDetail(pumpHouseNodeDetail.Node)">保存节点修改</a-button>
        </div>
      </div>
      <div class="f-xy-center f-column" v-else>
        <div class="mar-B30">无节点消息点击创建节点</div>
        <PlusOutlined @click="addPumpHouseNode(currentClickNode)" style="font-size: 36px; color: #1677ff" />
      </div>
    </div>
  </a-modal>
</template>

<script setup>
import { ref, watch, computed } from 'vue'
import { getSecondaryWaterProgressUpdate, getPumpHouseFileList } from '@/services/modules/map'
import MapGlS from '@/components/MapGlS2/index.vue'
import UploadImage from '@/components/UploadImage/index.vue'
import { UploadOutlined, DownloadOutlined, FolderViewOutlined, PlusOutlined } from '@ant-design/icons-vue'
import debounce from 'lodash/debounce'

import Cache from '@/utils/cache'
import { message } from 'ant-design-vue'
import options from '@/ownedByAllMapConfig'
import mapboxgl from 'mapbox-gl'
import dayjs from 'dayjs'

import { fileNames } from '@/assets/geojson/twiceProvideFileKeys.json'
import { PumpHouseApi } from '@/services/modules/pump.house'
import { CommonApi } from '@/services/modules/common'

const updeteModal = defineModel()
const props = defineProps({ data: Object, getPumpHouseList: Function })
const emit = defineEmits(['loadAll'])
const formRef = ref(null)
const formData = ref(null)
const url = computed(() => `http://47.106.81.163:3003/upload/pump_house/${formData.value.pumpRoomNumber}?url=img`)
const opMap = ref(false)
let Marker = null
let images = []
const UploadImageRef = ref(null)
const childNodeOpen = ref(false)
const files = ref([])

const location = {
  1: 'top: 10px; left: 28px',
  2: 'top: 10px; left: 150px',
  3: 'top: 10px; left: 290px',
  4: 'top: 10px; left: 420px',
  5: 'top: 10px; left: 546px',
  6: 'top: 10px; left: 680px',
  7: 'top: 70px; left: 696px',
  8: 'top: 70px; left: 570px',
  9: 'top: 70px; left: 446px',
  10: 'top: 70px; left: 315px',
  11: 'top: 70px; left: 170px',
  12: 'top: 70px; left: 20px',
  13: 'top: 130px; left: 25px',
  14: 'top: 130px; left: 160px',
  15: 'top: 130px; left: 295px',
  16: 'top: 130px; left: 430px',
  17: 'top: 130px; left: 565px'
}
watch(
  () => props.data,
  async (val) => {
    const { data } = await PumpHouseApi.detail(val?.PumpRoomNumber)
    formData.value = data
  }
)
const headers = computed(() => ({ Authorization: `Bearer ${Cache.get('userInfo')?.token}` }))

const selectOption = ref([])
async function getQueryDictionaries() {
  const res = await Promise.all([
    CommonApi.queryDictionaries('pumpHouseProjectStatus'), //项目进展状态
    CommonApi.queryDictionaries('pumpHouseProjectBatch'), //泵房批次
    CommonApi.queryDictionaries('pumpHouseOperationState'), //运营管理状态
    CommonApi.queryDictionaries('pumpHouseRemouldState') //改造状态
  ])
  selectOption.value = res.map((item, index) => item.data.map(({ DictValue, DictCode }) => ({ label: DictValue, value: index === 1 ? Number(DictCode) : DictValue })))
}
getQueryDictionaries()

// 泵房信息更新
function update(type = true) {
  try {
    formRef.value.validate().then(async () => {
      await PumpHouseApi.update(formData.value)
      await props.getPumpHouseList({ all: true })
      if (type) {
        message.success('更新成功')
        setTimeout(() => (updeteModal.value = false), 1000)
      }
    })
  } catch (error) {
    message.error('更新失败')
  }
}

async function validatexy({ field }) {
  if (formData.value[field] === '' || formData.value[field] === null) {
    return Promise.reject('不可为空')
  } else {
    return Promise.resolve()
  }
}
let MapL
function mapLoad(map) {
  MapL = map
  const { X, Y } = formData.value
  map.setCenter([X, Y])
  map.setZoom(17)
  Marker = new mapboxgl.Marker().setLngLat([X, Y]).addTo(map)
  Marker.getElement().addEventListener('click', handlerMarkerClick)
  map.on('move', handlerMoveEvent)
}

// 修改位置
function modifyPosition() {
  opMap.value = true
}
function handlerMarkerClick() {
  const { lng, lat } = Marker.getLngLat()
  formData.value.X = lng.toString()
  formData.value.Y = lat.toString()
  const { x, y } = Marker._pos

  const [feature] = MapL.queryRenderedFeatures([x, y], { target: { layerId: 'zone_fill' } })
  const { Subdistric, Grid, Belong_Are, Zone_Code } = feature.properties
  console.log(feature)
  formData.value.Gridding = Grid.replace('ML', '梅林').replace('FZ', '福中').replace('FD', '福东').replace('XM', '香蜜')
  formData.value.BelongingStreet = Subdistric
  formData.value.BelongingArea = Belong_Are
  formData.value.ZoneCode = Zone_Code
  opMap.value = false
  MapL.off('move', handlerMoveEvent)
  Marker.getElement().removeEventListener('click', handlerMarkerClick)
  Marker.remove()
  MapL = null
}
function handlerMoveEvent() {
  let { lng, lat } = MapL.getCenter() // 获取新的中心点坐标
  Marker.setLngLat([lng, lat]) // 跟新标记位置
}

function handlerClick(e) {
  const center = e.Center_Point.split(',')
  MapL.setCenter(center)
}

async function queryDictionaries(type) {
  try {
    const { data } = await CommonApi.queryDictionaries(type)
    return data
  } catch (error) {
    message.error(error.message)
  }
}

const pumpHouseNodeKeys = ref([])
async function getpumpHouseNodeKeys() {
  pumpHouseNodeKeys.value = await queryDictionaries('pumpHouseNode')
}
getpumpHouseNodeKeys()

// 处理节点点击事件
const currentClickNode = ref(null)
const pumpHouseNodeDetail = ref(null)
const pumpHouseNodeFileKeyMap = new Map()
async function handlerNodeClick(nodeNum) {
  if (!pumpHouseNodeFileKeyMap.has(nodeNum)) {
    const { data: FileKey } = await CommonApi.queryDictionaries(`pumpHouseNodeFile_${nodeNum}`)
    pumpHouseNodeFileKeyMap.set(nodeNum, FileKey)
  }
  const { data } = await PumpHouseApi.nodeDetail(props.data.PumpRoomNumber, { Node: nodeNum })
  if (data) {
    if (data.Files) {
      data.Files = JSON.parse(data.Files).map(({ FileType }) => FileType)
    } else {
      data.Files = []
    }
  }

  pumpHouseNodeDetail.value = data
  currentClickNode.value = nodeNum
  childNodeOpen.value = true
}

async function addPumpHouseNode(nodeNum) {
  try {
    const CompletionTime = dayjs().format('YYYY-MM-DD')
    await PumpHouseApi.createNode({ PumpRoomNumber: props.data.PumpRoomNumber, Node: nodeNum, CompletionTime })
    await handlerNodeClick(nodeNum)
  } catch (error) {
    message.error('创建失败')
  }
}

// 文件上传监听事件
let hideFile
async function handleUploadChange(info, nodeNum, FileType) {
  if (!hideFile) {
    hideFile = message.loading('文件上传中...', 0)
  }
  try {
    if (info.fileList.length > 0) {
      const file = info.fileList[0]
      if (file.status === 'done') {
        message.success(`${file.name} 文件上传成功`)
        await PumpHouseApi.createNodeFile({ Path: file.response.data, Node: nodeNum, PumpRoomNumber: props.data.PumpRoomNumber, FileType })
        await handlerNodeClick(nodeNum)
        files.value = []
        hideFile()
        hideFile = null
      } else if (file.status === 'error') {
        message.error(`${file.name} 文件上传失败`)
        hideFile()
        hideFile = null
      }
    }
  } catch (error) {
    hideFile()
    hideFile = null
    message.error('文件上传失败')
  }
}

async function updatePumpHouseNodeDetail(nodeNum) {
  try {
    if (!pumpHouseNodeDetail.value.CompletionTime) return message.error('请选择完成时间')
    update(false)
    await PumpHouseApi.updateNode(pumpHouseNodeDetail.value)
    await handlerNodeClick(nodeNum)
    const { data } = await PumpHouseApi.detail(formData.value.PumpRoomNumber)
    formData.value = data
    childNodeOpen.value = false
    message.success('更新成功')
    await props.getPumpHouseList({ all: true })
  } catch (error) {
    message.error('更新失败')
  }
}

function handleUploadImgChange(info) {
  if (!hideFile) {
    hideFile = message.loading('文件上传中...', 0)
  }

  try {
    if (info.fileList.length > 0) {
      const file = info.fileList[0]
      if (file.status === 'done') {
        message.success(`${file.name} 图片上传成功`)
        formData.value.PumpHouseImg = formData.value.PumpHouseImg ? formData.value.PumpHouseImg + ',' + file.response.data : file.response.data
        files.value = []
        hideFile()
        hideFile = null
      } else if (file.status === 'error') {
        message.error(`${file.name} 图片上传失败`)
        hideFile()
        hideFile = null
      }
    }
  } catch (error) {
    hideFile()
    hideFile = null
    message.error('文件上传失败')
  }
}
</script>

<style lang="less" scoped>
.update-detail {
  height: 700px;
  overflow: auto;
  :deep(:where(.css-dev-only-do-not-override-mb15du).ant-form-item) {
    margin-bottom: 14px;
  }
}
.mapbox {
  height: 774px;
  width: 100%;
  position: absolute;
  background-color: #fff;
  top: 0;
  z-index: 999;
}
.file {
  margin-top: 12px;
  // :deep(.ant-upload-list) {
  //   display: none;
  // }
}

.wire {
  display: flex;
  justify-content: center;
  align-items: center;
  & > span {
    margin: 0 10px;
    font-size: 16px;
    font-weight: 600;
  }
  & > div {
    flex: 1;
    background-color: #666;
    height: 1px;
  }
}
:deep(.ant-upload-list) {
  display: none;
}
.color-000 {
  color: #000;
}
</style>
