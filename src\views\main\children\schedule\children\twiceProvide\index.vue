<template>
  <!-- 二供 -->
  <div class="all relative">
    <MapGlS ref="mapComponentRef" :options :legend @load="mapLoad" />
    <!-- 工程进度 -->
    <ScheduleMutual v-model="schedule.open" :data="schedule.data" @openUpdateModal="updateOpen = true" />
    <!-- 概况 -->
    <GeneralSituation :detail :data="count" :getUpdateFeatures :getUpdateLength :Map :getPumpHouseList v-model="selectValue" :pumpHouseDots>
      <SearchMarker @change="queryDot" />
    </GeneralSituation>

    <UpdateDetail :getPumpHouseList v-model="updateOpen" :data="schedule.data" />

    <div class="Layer_switching box-shadow border-666">
      <a-segmented size="large" v-model:value="selectValue" block :options="LayersTable" />
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, watch, computed, nextTick, onUnmounted } from 'vue'
import mapboxgl from 'mapbox-gl'
import ScheduleMutual from './components/ScheduleMutual.vue'
import SearchMarker from './components/SearchMarker.vue'
import Popup from './components/Popup.vue'
import GeneralSituation from './components/GeneralSituation.vue'
import UpdateDetail from './components/UpdateDetail.vue'
import { transitionArr } from '@/utils'
import { getSecondaryWaterProgress } from '@/services/modules/map'
import throttle from 'lodash/throttle'

import icon0 from '@/assets/images/异常.png'
import icon1 from '@/assets/images/临供.png'
import icon2 from '@/assets/images/切换.png'
import icon3 from '@/assets/images/初验.png'
import icon4 from '@/assets/images/其他.png'
import icon5 from '@/assets/images/临供前.png'

import MapGlS from '@/components/MapGlS2/index.vue'
import { flyTo, transitionComponent } from '@/components/MapGlS2/utils'
import centers from '@/assets/geojson/centers.json'
import { message } from 'ant-design-vue'

import { PumpHouseApi } from '@/services/modules/pump.house'

let Map = ref(null)
const mapComponentRef = ref(null)
const detail = reactive({ isShow: false, data: null })

const schedule = reactive({ open: false, data: null })
const updateOpen = ref(false)
// 图层展示列表数据
const LayersTable = reactive(['片区', '网格', '街道'])
const selectValue = ref(LayersTable[1])
const currentLayer = computed(() => {
  const keys = { 片区: 'BelongingArea', 网格: 'Gridding', 街道: 'BelongingStreet' }
  return keys[selectValue.value]
})

const zoneColor = {
  梅林片区: '#1677ff',
  景田片区: 'red',
  香蜜片区: '#fa9600',
  福东南片区: '#0000FF',
  福东北片区: '#ffd447',
  莲花片区: '#bdfb9b',
  中心城片区: '#ffb4a6',
  福保片区: '#9f4fff',
  福民片区: '#00ffff',
  新洲片区: '#00ff00'
}
const tenGridColor = {
  '福中-1': '#999999',
  '福中-2': '#947dd2',
  '福中-3': '#38056c',
  '福东-1': '#ffc300',
  '福东-2': '#1677ff',
  '梅林-2': '#8d0e8d',
  '梅林-1': '#cfc00e',
  '香蜜-3': '#37840b',
  '香蜜-2': '#900C3F',
  '香蜜-1': '#0B7784'
}
const streetColor = {
  梅林街道办: '#999999',
  莲花街道办: '#947dd2',
  香蜜湖街道办: '#38056c',
  沙头街道办: '#ffc300',
  福保街道办: '#1677ff',
  福田街道办: '#8d0e8d',
  南园街道办: '#cfc00e',
  华强北街道办: '#37840b',
  华富街道办: '#900C3F',
  园岭街道办: '#0B7784'
}

const options = {
  map: {
    center: [114.07528061331722, 22.54740197444606], //中心点
    zoom: 12.8 //默认缩放
  },
  sources: {
    // 地形图
    Polyline_FT: {
      type: 'vector',
      scheme: 'tms',
      tiles: ['https://www.szwgft.cn/mapServer/geoserver/gwc/service/tms/1.0.0/Code%3APolyline_FT@EPSG%3A3857@pbf/{z}/{x}/{y}.pbf'],
      layers: {
        PolylineFT_line: {
          options: { minzoom: 16.5, 'source-layer': 'Polyline_FT', paint: { 'line-width': 2, 'line-color': '#666' } }
        }
      }
    },
    //无缝区块
    FT_ZONE: {
      type: 'vector',
      scheme: 'tms',
      tiles: ['https://www.szwgft.cn/mapServer/geoserver/gwc/service/tms/1.0.0/Code%3AFT_ZONE@EPSG%3A3857@pbf/{z}/{x}/{y}.pbf'],
      layers: {
        zone_fill: {
          options: {
            'source-layer': 'FT_ZONE',
            minzoom: 14.5,
            paint: {
              'fill-opacity': 0.35,

              'fill-color': [
                'case',
                ['==', ['get', 'Type'], '市政路'],
                '#999',
                ['==', ['get', 'Type'], '其他'],
                '#888',
                [
                  'match',
                  ['get', 'ManagerNam'], // type 等于 小区，根据 ManagerNam 设置颜色
                  ['福中水务所'],
                  '#ee0000',
                  ['福东水务所'],
                  '#bdfb9b',
                  ['梅林水务所'],
                  '#1677ff',
                  ['香蜜水务所'],
                  '#fa9600',
                  '#999' // 默认颜色，如果 ManagerNam 不匹配以上任何值
                ]
              ]
            }
          }
        },
        zone_line: {
          options: { minzoom: 14.5, 'source-layer': 'FT_ZONE', paint: { 'line-width': 1, 'line-color': '#000' } }
        },
        zone_symbol: {
          options: { 'source-layer': 'FT_ZONE', minzoom: 15, layout: { 'text-field': ['get', 'Zone_Name'], 'text-size': 12 }, paint: { 'text-color': 'black', 'text-halo-color': 'white', 'text-halo-width': 1 } }
        }
      }
    },
    // 片区图层
    FT_ModificationWorks: {
      type: 'vector',
      scheme: 'tms',
      tiles: ['https://www.szwgft.cn/mapServer/geoserver/gwc/service/tms/1.0.0/Code%3AFT_ModificationWorks@EPSG%3A3857@pbf/{z}/{x}/{y}.pbf'],
      layers: {
        FTModificationWorks_fill: {
          events: {
            mouseover: () => (detail.isShow = true),
            mouseout: () => (detail.isShow = false)
          },
          options: {
            'source-layer': 'FT_ModificationWorks',
            maxzoom: 14.5,
            layout: { visibility: 'none' },
            paint: {
              'fill-opacity': 0.35,
              'fill-outline-color': '#000000',
              'fill-color': ['match', ['get', 'AreaName'], ...transitionArr(zoneColor, 'pink')]
            }
          }
        },
        FTModificationWorks_line: {
          options: { 'source-layer': 'FT_ModificationWorks', paint: { 'line-width': 1, 'line-color': '#000' }, layout: { visibility: 'none' } }
        },
        FTModificationWorks_symbol: {
          options: {
            'source-layer': 'FT_ModificationWorks',
            layout: { 'text-field': ['get', 'AreaName'], 'text-anchor': 'center', 'text-size': 22, visibility: 'none' },
            paint: { 'text-color': '#c500ff', 'text-halo-color': 'white', 'text-halo-width': 1 }
          }
        }
      }
    },
    // 十网格图层
    tenGrid: {
      type: 'vector',
      scheme: 'tms',
      tiles: ['https://www.szwgft.cn/mapServer/geoserver/gwc/service/tms/1.0.0/Code%3AtenGrid@EPSG%3A3857@pbf/{z}/{x}/{y}.pbf'],
      layers: {
        tenGrid_fill: {
          options: {
            'source-layer': 'tenGrid',
            maxzoom: 14.5,
            paint: {
              'fill-opacity': 0.5,

              'fill-outline-color': '#000000',
              'fill-color': ['match', ['get', 'name'], ...transitionArr(tenGridColor, 'pink')]
            }
          }
        },
        tenGrid_line: {
          options: { 'source-layer': 'tenGrid', paint: { 'line-width': 1, 'line-color': '#000' } }
        },
        tenGrid_symbol: {
          options: {
            'source-layer': 'tenGrid',
            layout: { 'text-field': ['get', 'name'], 'text-anchor': 'center', 'text-size': 22 },
            paint: { 'text-color': '#c500ff', 'text-halo-color': 'white', 'text-halo-width': 1 }
          }
        }
      }
    },
    // 街道图层
    FT_street: {
      type: 'vector',
      scheme: 'tms',
      tiles: ['https://www.szwgft.cn/mapServer/geoserver/gwc/service/tms/1.0.0/Code%3AFT_street@EPSG%3A3857@pbf/{z}/{x}/{y}.pbf'],
      layers: {
        FTstreet_fill: {
          options: {
            'source-layer': 'FT_street',
            maxzoom: 14.5,
            paint: {
              'fill-opacity': 0.5,

              'fill-outline-color': '#000000',
              'fill-color': ['match', ['get', 'name'], ...transitionArr(streetColor, 'pink')]
            },
            layout: { visibility: 'none' }
          }
        },
        FTstreet_line: {
          options: { 'source-layer': 'FT_street', paint: { 'line-width': 1, 'line-color': '#000' }, layout: { visibility: 'none' } }
        },
        FTstreet_symbol: {
          options: {
            'source-layer': 'FT_street',
            layout: { 'text-field': ['get', 'name'], 'text-anchor': 'center', 'text-size': 22, visibility: 'none' },
            paint: { 'text-color': '#c500ff', 'text-halo-color': 'white', 'text-halo-width': 1 }
          }
        }
      }
    },
    dot: {
      type: 'geojson',
      data: { type: 'FeatureCollection', features: [] },
      layers: {
        dot_symbol: {
          events: {
            click: (e, feature) => {
              schedule.open = true
              schedule.data = feature.properties
            },
            mouseenter: (e, feature, map) => {
              map.popup = new mapboxgl.Popup({ maxWidth: 'none', offset: [0, -30] })
                .setLngLat([feature.properties.X, feature.properties.Y])
                .setDOMContent(component({ data: feature.properties }))
                .addTo(map)
            },
            mouseleave: (e, feature, map) => map.popup.remove()
          },
          images: {
            异常: icon0,
            临供前: icon5,
            临供: icon1,
            切换: icon2,
            初验: icon3,
            其他: icon4,
            已改造: icon2,
            已达标: icon3,
            未改造: icon1,
            未达标: icon0,
            正在改造: icon4,

            小区物业管理: icon4,
            施工单位管理: icon1,
            '集团自管（移交水务科技）': icon2,
            集团自管: icon3,

            1: icon2,
            2: icon3,
            3: icon1,
            4: icon4,
            5: icon0,

            已采集: icon3,
            未采集: icon0
          },
          options: {
            layout: {
              'icon-image': ['get', 'state'],
              'icon-size': 0.08,
              'icon-allow-overlap': true
            }
          }
        }
      }
    }
  }
}
const throttleMouseoverCallBack = throttle(mouseoverCallBack, 300)
function mapLoad(map) {
  Map.value = map
  getPumpHouseList({ all: true }) //获取泵房列表
  Map.value.on('mousemove', 'FTModificationWorks_fill', throttleMouseoverCallBack)
}

onUnmounted(() => {
  Map.value.off('mousemove', 'FTModificationWorks_fill', throttleMouseoverCallBack)
})

// 片区消息
function mouseoverCallBack(e) {
  if (!detail.isShow) return
  const [feature] = Map.value.queryRenderedFeatures(e.point, { layers: ['FTModificationWorks_fill'] })
  detail.data = feature.properties
}

// 柱形图数据统计
const count = computed(() => {
  const categories = ['网格', '街道', '片区']
  const types = ['异常', '临供前', '临供', '切换', '初验']

  // 使用 reduce 初始化对象结构
  const obj = categories.reduce((acc, category) => ({ ...acc, [category]: {} }), {})

  // 统计数据
  pumpHouseDots.value.forEach(({ properties }) => {
    const { state } = properties
    categories.forEach((category) => {
      const key = properties[getCategoryKey(category)]
      obj[category][key] = obj[category][key] || types.reduce((acc, t) => ({ ...acc, [t]: 0 }), {})
      obj[category][key][state]++
    })
  })

  // 转换为数组格式
  for (const category in obj) {
    for (const key in obj[category]) {
      obj[category][key] = types.map((type) => obj[category][key][type])
    }
  }
  return obj
})

// 辅助函数,获取对应的数据键名
function getCategoryKey(category) {
  const keyMap = { 网格: 'Gridding', 街道: 'BelongingStreet', 片区: 'BelongingArea' }
  return keyMap[category]
}

// 图例
const legend = ref([])
function getUpdateLength(val, length, keys, pumpHouseShowValue) {
  const images = options.sources.dot.layers.dot_symbol.images
  const data = Object.entries(val).map(([key, value]) => {
    return { url: images[key], label: pumpHouseShowValue == 'Batch' ? keys[key] : key, type: key }
  })

  nextTick(() => (legend.value = data))
}

// 渲染标记
const component = transitionComponent(Popup)
// 搜索泵房
function queryDot(e) {
  Map.value.setFilter('dot_symbol', ['match', ['get', 'PumpHouseName'], e.PumpHouseName, true, false])
  const [feature] = Map.value.querySourceFeatures('dot', { sourceLayer: 'FTModificationWorks_fill', filter: ['==', ['get', 'PumpHouseName'], e.PumpHouseName] })
  flyTo(Map.value, feature.geometry.coordinates, 13)
}

// 更新人员统计
function getUpdateFeatures(type) {
  const obj = {}
  let list = pumpHouseDots.value.map(({ properties }) => properties)
  if (type) {
    list = pumpHouseDots.value.filter(({ properties }) => properties[currentLayer.value] === type).map(({ properties }) => properties)
  }
  list.forEach((item) => (obj[item.UpdatePerson] = obj[item.UpdatePerson] ? obj[item.UpdatePerson] + 1 : 1))
  const data = Object.entries(obj)
    .sort((a, b) => a[1] - b[1])
    .reverse()

  return { key: data.map((item) => item[0]), value: data.map((item) => item[1]) }
}

// 获取泵房列表数据
const pumpHouseDots = ref([])
async function getPumpHouseList(params) {
  const hide = message.loading('数据加载中...', 0)
  try {
    const { data } = await PumpHouseApi.list(params)
    pumpHouseDots.value = data.map((item) => {
      const state = item.ProgressStatus != '正常' ? '异常' : item.CurrentNode < 4 ? '临供前' : item.CurrentNode < 12 ? '临供' : item.CurrentNode < 14 ? '切换' : '初验'
      const isCollect = item.AccuratePosition ? '已采集' : '未采集'
      item.Id = JSON.stringify(item)
      return {
        type: 'Feature',
        geometry: { type: 'Point', coordinates: [item.X, item.Y] },
        properties: { ...item, state, isCollect }
      }
    })
    mapComponentRef.value?.updatedData('dot', pumpHouseDots.value)
    hide()
  } catch (error) {
    message.error('数据加载失败')
    hide()
  }
}
</script>

<style lang="less">
.twice_marker {
  width: 35px;
  height: 35px;
  display: flex;
  justify-content: center;
  padding-top: 5px;
  cursor: pointer;
  .twice_warp {
    width: 18px;
    height: 18px;
    background-color: #fff;
    border-radius: 50%;
    display: flex;
    justify-content: center;
    align-items: center;
  }
}
</style>
<style lang="less" scoped>
.twice_provide {
}

.Layer_switching {
  position: absolute;
  left: 23%;
  bottom: 24px;
  z-index: 9999;
  width: 620px;
}
</style>
