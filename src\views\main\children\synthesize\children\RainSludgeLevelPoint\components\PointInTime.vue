<template>
  <div class="warp border-R20">
    <div class="line_box relative">
      <div class="line f-y-center f-between">
        <div class="absolute" style="left: -90px">时间点：</div>
        <template v-for="(item, index) in 24" :key="item">
          <div
            :style="{ left: (index / 23) * 100 + '%' }"
            style="margin-left: -5px"
            class="item pointer border-Rall back-white absolute"
            @mouseover="mouseover(index)"
            @mouseout="mouseout"
            :class="{ before: index < active, active: index === active }"
          >
            <div style="white-space: nowrap" class="num mar-T26 f-xy-center">{{ index < 10 ? '0' + index : index }} : 00</div>
          </div>
        </template>
        <div :style="{ width: (active / 23) * 100 + '%', height: '4px' }" class="activeline"></div>
      </div>
    </div>

    <div class="detail_box flex" :class="{ detail_active: detail?.LiquidLevels?.length }">
      <div class="pad-10">充盈：</div>
      <template v-for="(item, index) in detail?.LiquidLevels" :key="item">
        <div class="f-1 fon-S14 f-column f-xy-center">
          <div>{{ Math.floor(((item * 1000) / detail.caliber) * 100) }}%</div>
        </div>
      </template>
    </div>
    <div class="detail_box flex" :class="{ detail_active: detail?.LiquidLevels?.length }">
      <div class="pad-10">液位：</div>
      <template v-for="(item, index) in detail?.LiquidLevels" :key="item">
        <div class="f-1 fon-S14 f-column f-xy-center">
          <div :style="{ color: item / detail.depth >= 1 ? 'red' : 'black' }">{{ item }}m</div>
        </div>
      </template>
    </div>
    <div class="detail_box flex" :class="{ detail_active: detail?.LiquidLevels?.length }">
      <div class="pad-10">满井：</div>
      <template v-for="(item, index) in detail?.LiquidLevels" :key="item">
        <div class="f-1 fon-S14 f-column f-xy-center">
          <div style="color: red" class="fon-W600" v-if="item / detail.depth >= 1">是</div>
          <div v-else>否</div>
        </div>
      </template>
    </div>
  </div>
</template>

<script setup>
import { ref, watch, onMounted, onUnmounted } from 'vue'

const emit = defineEmits(['change'])
const porps = defineProps({ detail: Object, interval: { type: Number, default: 3000 } })
const active = ref(0)
let timer = null

onMounted(() => setTime())
onUnmounted(() => clearInterval(timer))
watch(active, (val) => emit('change', val))
function setTime() {
  timer = setInterval(() => (active.value = active.value >= 23 ? 0 : active.value + 1), porps.interval)
}

function mouseover(val) {
  clearInterval(timer)
  active.value = val
}

function mouseout() {
  setTime()
}
</script>

<style lang="less" scoped>
.warp {
  width: 1600px;
  background-color: #ffffff;
  border: 1px solid #999;
  box-sizing: content-box;
  margin: 0px 24px 24px 24px;
  .line_box {
    padding: 34px 40px 45px 100px;
  }
}
.line {
  height: 4px;
  background-color: #2d69a3;
  position: relative;
  border-radius: 8px;
}
.activeline {
  background-color: #1ddb63;
  transition: all 0.6s;
}
.active {
  border: 4px solid #1ddb63 !important;
  transition: all 0.5s !important;
  width: 16px !important;
  height: 16px !important;
  .num {
    font-weight: 600;
    color: #333;
  }
}
.before {
  border: 3px solid #1ddb63 !important;
}
.item {
  width: 10px;
  height: 10px;
  border: 2px solid #2d69a3;
  transition: all 0.5s;
  box-sizing: content-box;
}
.num {
  font-size: 14px;
}

.detail_box {
  overflow: hidden;
  height: 0px;
  transition: all 0.8s;
}
.detail_active {
  border-top: 1px solid #999;
  transition: all 0.8s;
  height: 40px;
}
</style>
