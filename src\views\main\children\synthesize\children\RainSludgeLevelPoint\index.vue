<template>
  <!-- 网管自评 -->
  <div class="all">
    <MapGlS @load="loadMap" :options="options" :legend>
      <template #rightBottom>
        <div class="pad-16 border-R12 text-right">
          <div class="mar-R30">
            <a-button type="primary" @click="sampledDateControl('reduce')" :disabled="disabledLeft">
              <template #icon> <LeftOutlined /> </template>
            </a-button>
            <a-date-picker class="mar-X10" v-model:value="time" valueFormat="YYYY-MM-DD" format="YYYY-MM-DD" :disabledDate @change="getPipeFullness" />
            <a-button type="primary" @click="sampledDateControl('add')" :disabled="disabledRight">
              <template #icon> <RightOutlined /> </template>
            </a-button>
          </div>
        </div>
        <PointInTime @change="changeTime" :detail />
      </template>
    </MapGlS>
  </div>
</template>

<script setup>
import { ref, onBeforeUnmount, computed } from 'vue'
import MapGlS from '@/components/MapGlS2/index.vue'
import { MapboxOverlay } from '@deck.gl/mapbox'
import dayjs from 'dayjs'
import { GeoJsonLayer } from '@deck.gl/layers'
import { scaleThreshold } from 'd3-scale'

import { transitionComponent } from '@/utils/transitionDom.js'
import Popup from './components/Popup.vue'
import mapboxgl from 'mapbox-gl'
import { PipeFullness, PipeFullnessScope } from '@/services/modules/pipe.fullness'
import PointInTime from './components/PointInTime.vue'
import { LeftOutlined, RightOutlined } from '@ant-design/icons-vue'
import { createDot } from '@/utils'
import { message } from 'ant-design-vue'

const detail = ref(null)
const component = transitionComponent(Popup)

// 地图配置，可自定义中心点和 token
const options = {
  map: {
    accessToken: 'pk.eyJ1Ijoibm9ydGh2aSIsImEiOiJjbGVydjM1OXYwMnpkM3BxZGw5Ynlrbm13In0.queCazXHMgl8WHfZ1lF4xg',
    minZoom: 13
  },
  sources: {
    Plotoutercontour: {
      type: 'vector',
      tiles: ['https://www.szwgft.cn/mapServer/geoserver/gwc/service/tms/1.0.0/Code%3APlotoutercontour@EPSG%3A3857@pbf/{z}/{x}/{y}.pbf'],
      scheme: 'tms',
      layers: {
        Plotoutercontour_fill: {
          options: { 'source-layer': 'Plotoutercontour', paint: { 'fill-color': '#999', 'fill-opacity': 0.4 } }
        }
      }
    },
    Fullness: {
      type: 'vector',
      tiles: ['https://www.szwgft.cn/mapServer/geoserver/gwc/service/tms/1.0.0/Code%3APS_PIPE_BG@EPSG%3A3857@pbf/{z}/{x}/{y}.pbf'],
      scheme: 'tms',
      layers: {
        Fullness_line: {
          options: { 'source-layer': 'PS_PIPE_BG', paint: { 'line-width': 2, 'line-color': '#666' } }
        }
      }
    },
    tubeLine: {
      type: 'vector',
      scheme: 'tms',
      tiles: ['https://www.szwgft.cn/mapServer/geoserver/gwc/service/tms/1.0.0/group_fullness_layer@EPSG%3A3857@pbf/{z}/{x}/{y}.pbf'],
      layers: {
        tubeLayer_line: {
          events: {
            mouseenter: ({ lngLat }, feature, map) => {
              const properties = feature.properties
              const { lng, lat } = lngLat
              if (map.isDot) return
              if (map.popup) map.popup.remove()
              map.popup = new mapboxgl.Popup({ closeButton: false, maxWidth: 'none', offset: [0, -10] }).setLngLat([lng, lat])
              map.popup.setDOMContent(component({ data: properties, type: 'line' })).addTo(map)
            },
            mouseleave: (e, feature, map) => {
              if (map.isDot) return
              map.popup.remove()
              map.popup = null
            }
          },
          options: {
            'source-layer': 'DRAIN_PIPE',
            paint: { 'line-color': '#000000', 'line-width': 5, 'line-opacity': 0 }
          }
        },
        dot_symbol: {
          events: {
            mouseenter: (e, feature, map) => {
              detail.value = PipeFullnessData.value[feature.properties['检查井']] ?? null
              if (!detail.value) {
                message.error('当前时间点无数据')
              }
              detail.value.caliber = feature.properties['口径']
              detail.value.depth = feature.properties['井深']
              const properties = feature.properties
              if (map.popup) map.popup.remove()
              map.isDot = true
              map.popup = new mapboxgl.Popup({ closeButton: false, maxWidth: 'none', offset: [0, -10] }).setLngLat(feature.geometry.coordinates)
              map.popup.setDOMContent(component({ data: properties, type: 'dot' })).addTo(map)
            },
            mouseleave: (e, feature, map) => {
              map.popup.remove()
              map.popup = null
              map.isDot = false
              detail.value = null
            }
          },
          options: { 'source-layer': 'WastewaterPonit', layout: { 'icon-image': 'green' } }
        }
      }
    }
  }
}
const legend = [
  { label: '管道充盈 0%~50%', color: 'green' },
  { label: '管道充盈 50%~80%', color: 'yellow' },
  { label: '管道充盈 80%~100%', color: 'red' }
]

// mapboxgl 实例
let myMap = null
let overlay = null
const time = ref('2025-04-01')
const disabledDate = (current) => {
  if (!FullnessScope.value) return false
  return current <= dayjs(FullnessScope.value.minTime).add(-1, 'd').endOf('day') || current > dayjs(FullnessScope.value.maxTime).add(-1, 'd').endOf('day')
}
const disabledLeft = computed(() => {
  if (!FullnessScope.value?.minTime) return true
  return dayjs(time.value).endOf('day') <= dayjs(FullnessScope.value.minTime).endOf('day')
})

const disabledRight = computed(() => {
  if (!FullnessScope.value?.maxTime) return true
  return dayjs(time.value).endOf('day') >= dayjs(FullnessScope.value.maxTime).add(-1, 'd').endOf('day')
})

function sampledDateControl(type) {
  if (type === 'add') {
    time.value = dayjs(time.value).add(1, 'd').format('YYYY-MM-DD')
  } else {
    time.value = dayjs(time.value).add(-1, 'd').format('YYYY-MM-DD')
  }
  getPipeFullness(time.value)
}

onBeforeUnmount(() => {
  PipeFullnessDataMap.value = null
})

const COLOR_SCALE = scaleThreshold()
  .domain([50, 80])
  .range([
    [0, 128, 0],
    [255, 255, 0],
    [255, 0, 0]
  ])
const COLOR_SCALE_W = scaleThreshold().domain([50, 80]).range([4, 6, 8])

function loadMap(map) {
  myMap = map
  map.addImage('green', createDot(map, 'green'), { pixelRatio: 2 })
  map.addImage('yellow', createDot(map, 'yellow'), { pixelRatio: 2 })
  map.addImage('red', createDot(map, 'red'), { pixelRatio: 2 })
  overlay = new MapboxOverlay({ interleaved: true })
  myMap.addControl(overlay)
}

function setLineProps(index) {
  // 获取可视区的所有线要素
  const LineFeatures = myMap.queryRenderedFeatures({ layers: ['tubeLayer_line'] })
  const geoJsonLayer = new GeoJsonLayer({
    id: 'deck-geojson',
    data: { type: 'FeatureCollection', features: LineFeatures },
    lineWidthMinPixels: 0.5,
    getLineColor: ({ properties }) => {
      const data = PipeFullnessData.value[properties['检查井']]
      if (!data) return COLOR_SCALE(0)
      const LiquidLevel = (data.LiquidLevels[index] ?? 0) * 1000
      return COLOR_SCALE((LiquidLevel / properties.DIAMETER) * 100)
    },
    getLineWidth: ({ properties }) => {
      const data = PipeFullnessData.value[properties['检查井']]
      if (!data) return COLOR_SCALE_W(0)
      const LiquidLevel = (data.LiquidLevels[index] ?? 0) * 1000
      return COLOR_SCALE_W((LiquidLevel / properties.DIAMETER) * 100)
    },
    pickable: true,
    lineWidthUnits: 'pixels',
    lineCapRounded: true,

    beforeId: 'dot_symbol',
    transitions: { getLineColor: 2000, getLineWidth: 2000 }
  })

  overlay.setProps({ layers: [geoJsonLayer] })
}

function changeTime(e) {
  setLineProps(e)
  const DotsFeatures = myMap.queryRenderedFeatures({ layers: ['dot_symbol'] })
  const styleColor = []
  DotsFeatures.forEach((item) => {
    const SystemId = item.properties['检查井']
    const data = PipeFullnessData.value[SystemId]
    const percent = data ? (((data.LiquidLevels[e] ?? 0) * 1000) / item.properties['口径']) * 100 : 0
    styleColor.push([SystemId])
    styleColor.push(percent < 50 ? 'green' : percent < 80 ? 'yellow' : 'red')
  })

  myMap.setLayoutProperty('dot_symbol', 'icon-image', ['match', ['get', '检查井'], ...styleColor, 'green'])
}
const FullnessScope = ref(null)
async function getPipeFullnessScope() {
  try {
    const { data } = await PipeFullnessScope()
    FullnessScope.value = data
    time.value = dayjs(data.maxTime).add(-1, 'd').format('YYYY-MM-DD')
    await getPipeFullness(time.value)
    setTimeout(() => changeTime(0), 100)
  } catch (error) {
    console.log(error)
  }
}
getPipeFullnessScope()

const PipeFullnessData = ref(null)
const PipeFullnessDataMap = new Map()
async function getPipeFullness(time) {
  try {
    if (PipeFullnessDataMap.has(time)) {
      message.success('数据查询成功')
      return (PipeFullnessData.value = PipeFullnessDataMap.get(time))
    }
    const DT = {}
    const { data } = await PipeFullness(time)
    for (let i = 0; i < data.length; i++) {
      const item = data[i]
      DT[item.SystemId] = item
    }
    PipeFullnessDataMap.set(time, DT)
    PipeFullnessData.value = DT
    message.success('数据查询成功')
  } catch (error) {
    console.log(error)
  }
}
</script>

<style lang="less" scoped></style>
