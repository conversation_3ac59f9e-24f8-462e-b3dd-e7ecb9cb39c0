<template>
  <div class="home all relative">
    <div class="title absolute fon-W600 fon-S20">福田智能供水决策支持系统</div>
    <div class="map absolute">
      <MapInputSearch @handlerZoneClick="handlerClick" class="input-search" />
      <MapGlS ref="mapComponentRef" :options @load="mapLoad" />
    </div>

    <div class="left">
      <div class="box1 box_d">
        <div class="title_b">标题1</div>
        <div class="content"></div>
      </div>
      <div class="box2 box_d">
        <div class="title_b">标题2</div>
        <div class="content"></div>
      </div>
      <div class="box3 box_d">
        <div class="title_b">标题3</div>
        <div class="content"></div>
      </div>
    </div>
    <div class="right">
      <div class="box1 box_d">
        <div class="title_b">标题1</div>
        <div class="content"></div>
      </div>
      <div class="box2 box_d">
        <div class="title_b">标题2</div>
        <div class="content"></div>
      </div>
      <div class="box3 box_d">
        <div class="title_b">标题3</div>
        <div class="content"></div>
      </div>
    </div>
    <div class="bottom absolute f-xy-center">
      <!-- <MainHander /> -->
      <Home_Tab />
    </div>
  </div>
</template>

<script setup>
import MapGlS from '@/components/MapGlS2/index.vue'
import MapInputSearch from '@/components/MapInputSearch/index.vue'
import pandectPopup from '@/components/MapPopup/pandectPopup.vue'
import Home_Tab from '@/components/HomeTab/index.vue'

import { flyTo, transitionComponent } from '@/components/MapGlS2/utils'
import { getZoneData } from '@/services/modules/map'

import mapboxgl from 'mapbox-gl'
import { FT_ZONE, highlightZone, ZONE_FILL_LAYER, ZONE2_LINE_LAYER } from '@/ownedByAllMapConfig'

let Map
const options = {
  map: {
    style: {
      version: 8,
      sources: {
        'osm-tiles1': { type: 'raster', tiles: ['https://t4.tianditu.gov.cn/DataServer?T=img_w&x={x}&y={y}&l={z}&tk=c4422fec9d5e394411da10d3f1838c84'], tileSize: 256, maxzoom: 18 }
      },
      glyphs: 'mapbox://fonts/mapbox/{fontstack}/{range}.pbf',
      layers: [{ id: 'simple-tiles1', type: 'raster', source: 'osm-tiles1' }]
    }
  },
  sources: { FT_ZONE }
}
const component = transitionComponent(pandectPopup)

function mapLoad(map) {
  Map = map
  insertMapPopup()
  highlightZone(Map)
}

// 跳转区块与区块高亮
function handlerClick(e) {
  flyTo(Map, e.Center_Point.split(','), 17)
  Map.setFilter(ZONE2_LINE_LAYER, ['match', ['get', 'Zone_Name'], e.Zone_Name, true, false])
}

function insertMapPopup() {
  Map.on('click', ZONE_FILL_LAYER, async (e) => {
    const [feature] = Map.queryRenderedFeatures(e.point, { layers: [ZONE_FILL_LAYER] })
    const { properties } = feature
    const result = await getZoneData(properties.Zone_Code)
    const [res] = JSON.parse(result.data)
    const dom = component({ data: res })
    const popup = new mapboxgl.Popup({ maxWidth: 'none' })
    popup.setLngLat(e.lngLat).setDOMContent(dom).addTo(Map)
  })
}
</script>

<style lang="less" scoped>
.home {
  height: 1920px;
  background: url('../../assets/images/home_gb.png') no-repeat;
  background-size: 100% 100%;
  .title {
    left: 40px;
    top: 15px;
    color: rgb(0, 255, 255);
  }
  .map {
    width: 45.8333%;
    height: 730px;
    left: 49.5%;
    top: 50%;
    border: 3px solid #125985;
    transform: translate(-50%, -50%);
  }
  .bottom {
    width: 45.8333%;
    height: 150px;
    left: 49.5%;
    bottom: 30px;
    border: 3px solid #125985;
    transform: translate(-50%, 0);
  }
  .box_d {
    display: flex;
    flex-direction: column;
    & > .title_b {
      font-size: 16px;
      font-weight: 600;
      color: aqua;
    }
    & > .content {
      flex: 1;
    }
  }

  .left {
    width: 21.23758%;
    height: 890px;
    position: absolute;
    left: 40px;
    top: 154px;

    & > .box1 {
      height: 308px;
      & > .title_b {
        height: 38px;
        line-height: 38px;
        padding-left: 20px;
      }
    }
    & > .box2 {
      height: 280px;
      & > .title_b {
        height: 38px;
        line-height: 38px;
        padding-left: 20px;
      }
    }
    & > .box3 {
      height: 305px;
      & > .title_b {
        height: 34px;
        line-height: 34px;
        padding-left: 20px;
      }
    }
  }
  .right {
    width: 23.53758%;
    height: 850px;
    position: absolute;
    right: 32px;
    top: 190px;

    .box1 {
      height: 290px;
      padding-left: 10px;
      & > .title_b {
        height: 38px;
        text-align: center;
        line-height: 38px;
        padding-left: 20px;
      }
    }
    .box2 {
      height: 330px;
      padding: 28px 0 0 38px;
      & > .title_b {
        height: 35px;
        padding-right: 35px;
        text-align: right;
        line-height: 35px;
        padding-left: 20px;
      }
    }
    .box3 {
      height: 230px;
      padding-top: 30px;
      & > .title_b {
        height: 35px;
        padding-right: 35px;
        text-align: center;
        line-height: 35px;
        padding-left: 20px;
      }
    }
  }
}
.input-search {
  position: absolute;
  top: -50px !important;
  right: 0;
}
</style>
