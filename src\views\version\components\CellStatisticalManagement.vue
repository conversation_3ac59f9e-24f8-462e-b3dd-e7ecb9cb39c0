<template>
  <div class="wrap" v-if="detail">
    <div class="text-center fon-S18 fon-W600">小区</div>
    <div class="flex f-between pad-X24 mar-B10 border-B-eee">
      <div class="f-y-center">
        <div>供水小区：</div>
        <a-input-number v-model:value="detail.zone['供水小区']" :min="0" />
      </div>
      <div class="f-y-center">
        <div>居住性质小区：</div>
        <a-input-number v-model:value="detail.zone['居住性质']" :min="0" />
      </div>
    </div>

    <div class="flex f-between pad-X24 mar-B10 border-B-eee">
      <div class="f-y-center">
        <div>优饮小区：</div>
        <a-input-number v-model:value="detail.zone['优饮小区']" :min="0" />
      </div>
      <div class="f-y-center">
        <div>二供小区：</div>
        <a-input-number v-model:value="detail.zone['二供小区']" :min="0" />
      </div>
    </div>

    <div class="flex f-between pad-X24 mar-B10 border-B-eee">
      <div class="f-y-center">
        <div>二供泵房：</div>
        <a-input-number v-model:value="detail.zone['二供泵房']" :min="0" />
      </div>
      <div class="f-y-center">
        <div>其中无需改造泵房：</div>
        <a-input-number v-model:value="detail.zone['无改造需求泵房']" :min="0" />
      </div>
    </div>

    <div class="flex f-between pad-X24 mar-B10 border-B-eee">
      <div class="f-y-center">
        <div>在建工地：</div>
        <a-input-number v-model:value="detail.zone['在建工地']" :min="0" />
      </div>
    </div>

    <div class="text-center fon-S18 fon-W600">GIS管网设施</div>

    <div class="flex f-between pad-X24 mar-B10 border-B-eee">
      <div class="f-y-center">
        <div>检查井：</div>
        <a-input-number v-model:value="detail.facility['检查井']" :min="0" />
      </div>
      <div class="f-y-center">
        <div>雨水箅子：</div>
        <a-input-number v-model:value="detail.facility['雨水箅子']" :min="0" />
      </div>
    </div>

    <div class="flex f-between pad-X24 mar-B10 border-B-eee">
      <div class="f-y-center">
        <div>排水管件：</div>
        <a-input-number v-model:value="detail.facility['排水管件']" :min="0" />
      </div>
    </div>

    <div class="flex f-between pad-X24 mar-B10 border-B-eee">
      <div class="f-y-center">
        <div>排水渠总长：</div>
        <a-input-number v-model:value="detail.facility['排水渠长度']['总长']" :min="0" />
      </div>
      <div class="f-y-center">
        <div>市政：</div>
        <a-input-number v-model:value="detail.facility['排水渠长度']['市政']" :min="0" />
      </div>
      <div class="f-y-center">
        <div>小区：</div>
        <a-input-number v-model:value="detail.facility['排水渠长度']['小区']" :min="0" />
      </div>
    </div>

    <div class="flex f-between pad-X24 mar-B10 border-B-eee">
      <div class="f-y-center">
        <div>排水管总长：</div>
        <a-input-number v-model:value="detail.facility['排水管长度']['总长']" :min="0" />
      </div>
      <div class="f-y-center">
        <div>市政：</div>
        <a-input-number v-model:value="detail.facility['排水管长度']['市政']" :min="0" />
      </div>
      <div class="f-y-center">
        <div>小区：</div>
        <a-input-number v-model:value="detail.facility['排水管长度']['小区']" :min="0" />
      </div>
    </div>

    <div class="flex f-between pad-X24 mar-B10 border-B-eee">
      <div class="f-y-center">
        <div>控制阀：</div>
        <a-input-number v-model:value="detail.facility['控制阀']" :min="0" />
      </div>
      <div class="f-y-center">
        <div>调节阀：</div>
        <a-input-number v-model:value="detail.facility['调节阀']" :min="0" />
      </div>
    </div>
    <div class="flex f-between pad-X24 mar-B10 border-B-eee">
      <div class="f-y-center">
        <div>消火栓：</div>
        <a-input-number v-model:value="detail.facility['消火栓']" :min="0" />
      </div>
      <div class="f-y-center">
        <div>给水管件：</div>
        <a-input-number v-model:value="detail.facility['给水管件']" :min="0" />
      </div>
    </div>

    <div class="flex f-between pad-X24 mar-B10 border-B-eee">
      <div class="f-y-center">
        <div>给水管长度：</div>
        <a-input-number v-model:value="detail.facility['给水管长度']['总长']" :min="0" />
      </div>
      <div class="f-y-center">
        <div>市政：</div>
        <a-input-number v-model:value="detail.facility['给水管长度']['市政']" :min="0" />
      </div>
      <div class="f-y-center">
        <div>小区：</div>
        <a-input-number v-model:value="detail.facility['给水管长度']['小区']" :min="0" />
      </div>
    </div>

    <div class="f-x-center pad-24">
      <a-button type="primary" @click="submit">提交修改</a-button>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { message } from 'ant-design-vue'

import { getStatisticsData, updataStatisticsData } from '@/services/modules/neighbour.statistics'

const detail = ref(null)
async function getDetail() {
  const { data } = await getStatisticsData()
  detail.value = data
}

getDetail()

async function submit() {
  const { code } = await updataStatisticsData(detail.value)
  if (code === 200) {
    message.success('修改成功')
  }
  console.log(code)
}
</script>

<style lang="less" scoped>
.wrap {
}
</style>
