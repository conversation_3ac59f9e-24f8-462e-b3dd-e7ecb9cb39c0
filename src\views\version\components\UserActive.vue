<template>
  <div class="user_active pad-16 back-white border-R12 H100 mar-R12 overflow-auto">
    <div class="flex f-column border-eee pad-12" style="height: 300px">
      <div class="title">年度活跃记录</div>
      <div class="f-1"><Echarts :option="optionsList" :events /></div>
    </div>
    <div class="flex f-column border-eee pad-12" style="height: 300px">
      <div class="title">用户月度活跃记录</div>
      <div class="f-1"><Echarts :option="options" /></div>
    </div>
    <div class="flex f-column border-eee pad-12" style="height: 300px">
      <div class="title">用户活跃统计</div>
      <div class="f-1"><Echarts :option="optionsUserList" :events="UserListEvents" /></div>
    </div>
    <div class="flex f-column border-eee pad-12" style="height: 300px" v-if="activeUserLine.key.length">
      <div class="title">用户活跃统计</div>
      <div class="f-1"><Echarts :option="optionsUserLine" /></div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'
import { getActiveRecordList, getActiveRecordLine, getActiveUserList, getActiveUserLine } from '@/services/modules/active.record'
import Echarts from '@/components/Echarts/index.vue'

// 年度数据统计
const activeList = ref({ key: [], value: [] })
const events = {
  click: (event) => {
    activeRecordLine(event.name)
    getActiveUser(event.name)
  }
}
async function activeRecordList() {
  const { data } = await getActiveRecordList()
  activeList.value = { key: Object.keys(data), value: Object.values(data) }
}
activeRecordList()
const optionsList = computed(() => ({
  grid: { left: '3%', right: '4%', bottom: '3%', containLabel: true },
  xAxis: { type: 'category', data: activeList.value.key },
  yAxis: { type: 'value' },
  series: [{ data: activeList.value.value, type: 'bar' }]
}))

// 月度数据统计
activeRecordLine(null)
const activeLine = ref({ key: [], value: [] })
async function activeRecordLine(time) {
  const { data } = await getActiveRecordLine(time)
  activeLine.value = { key: Object.keys(data), value: Object.values(data) }
}
const options = computed(() => ({
  grid: { left: '3%', right: '4%', bottom: '3%', containLabel: true },
  tooltip: { trigger: 'axis' },
  xAxis: { type: 'category', data: activeLine.value.key },
  yAxis: { type: 'value', axisLabel: { formatter: '{value} 次' }, axisLine: { show: true } },
  series: [{ data: activeLine.value.value, type: 'line', smooth: true }]
}))

getActiveUser()
const activeUserList = ref({ key: [], value: [] })
const UserListEvents = { click: (event) => getActiveListUser(event.name) }
async function getActiveUser(time) {
  const { data } = await getActiveUserList(time)
  activeUserList.value = { key: Object.keys(data), value: Object.values(data) }
}

const optionsUserList = computed(() => ({
  grid: { left: '3%', right: '4%', bottom: '3%', containLabel: true },
  xAxis: { type: 'category', data: activeUserList.value.key },
  yAxis: { type: 'value' },
  series: [{ data: activeUserList.value.value, type: 'bar' }]
}))

const activeUserLine = ref({ key: [], value: [] })

async function getActiveListUser(name) {
  const { data } = await getActiveUserLine(name)
  activeUserLine.value = { key: Object.keys(data), value: Object.values(data) }
}
const optionsUserLine = computed(() => ({
  grid: { left: '3%', right: '4%', bottom: '3%', containLabel: true },
  tooltip: { trigger: 'axis' },
  xAxis: { type: 'category', data: activeUserLine.value.key },
  yAxis: { type: 'value', axisLabel: { formatter: '{value} 次' }, axisLine: { show: true } },
  series: [{ data: activeUserLine.value.value, type: 'line', smooth: true }]
}))
</script>

<style lang="less" scoped>
.user_active {
  width: 900px;
}
</style>
