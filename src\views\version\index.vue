<template>
  <div class="all back-eee f-xy-center pad-32">
    <UserActive />

    <div class="version back-white border-R12 H100 overflow-auto">
      <template v-for="item in versions" :key="item.version">
        <div class="item border-R10 f-column flex" :class="{ itemActive: item.version === onlineVersion }">
          <div class="item_info f-1 flex pad-12">
            <div class="f-1 flex f-column">
              <div class="flex f-1 f-y-center">
                <div>{{ item.platform }}：</div>
                <span class="version_text fon-S18 fon-W600">{{ item.version }}</span>
              </div>

              <div class="flex f-1 f-y-center">
                <div>强制更新：</div>
                <a-switch v-model:checked="item.force" />
              </div>

              <div class="flex f-1 f-y-center">
                <div>测试更新：</div>
                <a-switch v-model:checked="item.isTest" />
              </div>

              <div class="flex f-1 f-y-center">
                <div>更新时间：</div>
                <a-date-picker show-time v-model:value="item.allowUptateTime" format="YYYY-MM-DD HH:mm:ss" valueFormat="YYYY-MM-DD HH:mm:ss" />
              </div>
              <div class="flex f-1 f-y-center">
                <div>创建时间：</div>
                <a-date-picker disabled show-time v-model:value="item.updateTime" format="YYYY-MM-DD HH:mm:ss" valueFormat="YYYY-MM-DD HH:mm:ss" />
              </div>
            </div>
            <div class="f-2 pad-X16 item_text" style="border-left: 1px solid #eee">
              <a-textarea class="H100" v-model:value="item.updateContent" placeholder="Basic usage" :rows="9" />
            </div>
          </div>

          <div class="item_control flex pad-X20">
            <div class="f-1 f-y-center text-nowrap">
              <a-input v-model:value="item.downUrl"></a-input>
            </div>
            <div class="f-y-center">
              <a-button type="primary" class="mar-R20" @click="handelDeleteClick(item)" danger><DeleteOutlined />删除</a-button>
              <a-button type="primary" class="mar-R20" @click="handelUpdateClick(item)"><EditOutlined />修改</a-button>
              <a-switch @click="handelUpdateClick(item)" v-model:checked="item.allowUptate" />
            </div>
          </div>
        </div>
      </template>
    </div>
  </div>

  <a-float-button type="default" @click="createVarsionOpen = true" :style="{ right: '100px', bottom: '100px' }">
    <template #icon> <PlusOutlined /> </template>
  </a-float-button>

  <a-modal v-model:open="open" width="260px" title="删除版本" centered @ok="handelClick">
    <div class="">确认删除{{ current.version }}版本？</div>
  </a-modal>
  <a-modal v-model:open="createVarsionOpen" width="600px" title="新增版本" centered @ok="createVersionClick">
    <div class="create_wrap">
      <div class="flex mar-B10">
        <div class="f-y-center">
          <div>平台：</div>
          <a-radio-group v-model:value="newVersion.platform" name="radioGroup">
            <a-radio value="android">android</a-radio>
            <a-radio disabled value="ios">ios</a-radio>
          </a-radio-group>
        </div>
        <div class="f-y-center" style="margin-left: 88px">
          <div>版本号：</div>
          <a-input v-model:value="newVersion.version" style="width: 120px" placeholder="请输入版本号" />
        </div>
      </div>

      <div class="flex mar-B10">
        <div class="flex f-1 f-y-center">
          <div>强制更新：</div>
          <a-switch v-model:checked="newVersion.force" />
        </div>

        <div class="flex f-1 f-y-center">
          <div>测试更新：</div>
          <a-switch v-model:checked="newVersion.isTest" />
        </div>
      </div>

      <div class="flex mar-B10">
        <div class="flex f-1 f-y-center">
          <div>更新时间：</div>
          <a-date-picker style="width: 180px" show-time v-model:value="newVersion.allowUptateTime" format="YYYY-MM-DD HH:mm:ss" valueFormat="YYYY-MM-DD HH:mm:ss" />
        </div>
      </div>
      <div class="mar-B10">
        <a-textarea class="H100" v-model:value="newVersion.updateContent" placeholder="Basic usage" :rows="3" />
      </div>
      <div><a-input v-model:value="newVersion.downUrl"></a-input></div>
      <div class="mar-B10">
        <a-upload v-model:file-list="files" name="file" :headers action="https://www.szwgft.cn/nodeServer/update?fileType=apk" @change="handleChangeFile">
          <a-button> <UploadOutlined /> 上传apk </a-button>
        </a-upload>
      </div>
      <div>{{ newVersion.downUrl }}</div>
    </div>
  </a-modal>

  <a-button type="primary" class="absolute" style="right: 20px; top: 40px" @click="statisticsOpen = true">小区数量统计管理</a-button>

  <a-modal :footer="null" width="580px" v-model:visible="statisticsOpen" title="小区数量统计管理"><CellStatisticalManagement v-if="statisticsOpen" /></a-modal>
</template>

<script setup>
import { ref, reactive, computed } from 'vue'
import { getVersionList, updataVersion, createVersion, deleteVersion } from '@/services/modules/version'
import { DeleteOutlined, EditOutlined, PlusOutlined, UploadOutlined } from '@ant-design/icons-vue'
import { message } from 'ant-design-vue'
import UserActive from './components/UserActive.vue'
import CellStatisticalManagement from './components/CellStatisticalManagement.vue'
import Cache from '@/utils/cache'

const open = ref(false)
const createVarsionOpen = ref(false)
const current = ref(null)
const versions = ref([])
const files = ref([])
let newVersion = reactive({
  platform: 'android',
  updateContent: '',
  downUrl: '',
  version: '',
  force: true,
  isTest: true,
  allowUptate: false,
  updateTime: '',
  allowUptateTime: ''
})
const onlineVersion = computed(() => versions.value.filter((i) => i.allowUptate)[0].version)
const headers = computed(() => ({ Authorization: `Bearer ${Cache.get('userInfo')?.token}` }))

getVersions()
async function getVersions() {
  try {
    const { data } = await getVersionList()
    versions.value = data
  } catch (error) {
    console.error(error)
  }
}

// 删除版本
async function handelClick() {
  try {
    const { code } = await deleteVersion(current.value.id)
    if (code === 200) message.success('操作成功')
    getVersions()
  } catch (error) {
    message.success('操作失败' + error)
  }
  open.value = false
}

function handelDeleteClick(val) {
  open.value = true
  current.value = val
}

async function handelUpdateClick(val) {
  try {
    const { code } = await updataVersion(val)
    if (code === 200) message.success('操作成功')
  } catch (error) {
    console.error(error)
  }
}

async function createVersionClick() {
  try {
    if (newVersion.downUrl.length && newVersion.version.length) {
      newVersion.updateTime = new Date().toLocaleString().replace(/\//g, '-')
      const { code, data, message: msg } = await createVersion(newVersion)
      if (code === 200) {
        message.success(msg)
        createVarsionOpen.value = false
        getVersions()
        newVersion = {
          platform: 'android',
          updateContent: '',
          downUrl: '',
          version: '',
          force: true,
          isTest: false,
          allowUptate: false,
          updateTime: '',
          allowUptateTime: ''
        }
      } else {
        message.error(msg)
      }
    } else {
      message.error('apk与版本号不能为空')
    }
  } catch (error) {
    console.error(error)
    message.error('操作失败', +error)
  }
}

function handleChangeFile(e) {
  if (e.fileList[0]?.response?.data?.http) {
    newVersion.downUrl = e.fileList[0].response.data.http
  }
}

const statisticsOpen = ref(false)
</script>

<style lang="less" scoped>
.version {
  width: 1000px;
  padding: 15px 30px;
  .item {
    height: 300px;
    margin-bottom: 12px;
    border: 1px solid #ccc;
    .item_info {
    }
    .item_control {
      height: 50px;
      border-top: 1px solid #ccc;
    }
  }
  .itemActive {
    border: 1px solid #66da49 !important;
    .item_control {
      border-top: 1px solid #66da49 !important;
    }
    .item_text {
      border-left: 1px solid #66da49 !important;
    }
    .version_text {
      color: #66da49 !important;
    }
  }
}

.create_wrap {
}
</style>
