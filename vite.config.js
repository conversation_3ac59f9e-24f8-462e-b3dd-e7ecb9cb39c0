import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import path from 'path'

import Components from 'unplugin-vue-components/vite'
import { AntDesignVueResolver } from 'unplugin-vue-components/resolvers'

import { visualizer } from 'rollup-plugin-visualizer'

import compression from 'vite-plugin-compression'

const resolve = (dir) => path.resolve(__dirname, dir)

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [
    vue(),
    Components({ resolvers: [AntDesignVueResolver({ importStyle: false })] }),
    visualizer({ open: true, filename: 'stats.html' }),
    // 文件打包压缩
    compression({ verbose: true, disable: false, threshold: 5120, algorithm: 'gzip', ext: '.gz', deleteOriginFile: false })
  ],

  resolve: { alias: { '@': resolve('src') } },

  server: {
    host: '0.0.0.0',
    port: 3056,
    open: true,
    https: false,
    proxy: {
      '/api2': {
        target: 'https://www.szwgft.cn:5001',
        changeOrigin: true,
        rewrite: (path) => path.replace(/^\/api2/, '')
      },
      '/api': {
        target: 'https://www.szwgft.cn:5000',
        changeOrigin: true,
        rewrite: (path) => path.replace(/^\/api/, '')
      },
      '/nodeServer': {
        target: 'https://www.szwgft.cn',
        changeOrigin: true
        // rewrite: (path) => path.replace(/^\/nodeServer/, '')
      }
    }
  },

  // 分包配置
  build: {
    rollupOptions: {
      output: {
        chunkFileNames: 'js/[name]-[hash].js',
        entryFileNames: 'js/[name]-[hash].js',
        assetFileNames: '[ext]/[name]-[hash].[ext]',
        // 拆包node_modules
        manualChunks(id) {
          if (id.includes('node_modules')) {
            return id.toString().split('node_modules/')[1].split('/')[0].toString()
          }
        }
      }
    }
  }
})
